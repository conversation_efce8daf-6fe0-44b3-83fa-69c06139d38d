<template>
  <!--通用表格弹窗-->
  <cnd-dialog
    :title="title"
    :visible="visible"
    :show-close="true"
    :fullscreen="false"
    :append-to-body="appendToBody"
    :width="width"
    :height="height"
    @close="handleClose"
  >
    <template slot="content">
      <div v-if="showSearch" class="mb-10">
        <el-input
          ref="searchInput"
          v-model="filterText"
          clearable
          :placeholder="placeholder"
          size="mini"
          @keyup.native.stop.enter="queryTableList('search')"
          @clear="queryTableList('search')"
        >
          <i slot="suffix" class="el-input__icon el-icon-search pointer" @click="queryTableList('search')" />
        </el-input>
      </div>
      <div v-if="showDelete" class="mb-10">
        <el-button type="danger" size="mini" @click="handleDelete">{{ $t('btns.delete') }}</el-button>
      </div>
      <div class="flexV h100">
        <ag-grid-vue
          :class="['table', 'ag-theme-balham', 'grid-class', 'master-detail']"
          :row-drag-managed="true"
          :grid-options="gridOptions"
          :row-data="rowData"
          :column-defs="gridColumnDefs"
          :modules="modules"
          @selection-changed="onSelectionChangedRightData"
        />
        <cnd-pagination
          v-if="showPagination"
          :total="pagination.total"
          :page="pagination.pageNo"
          :limit="pagination.pageSize"
          :event="pageChange"
        />
      </div>
    </template>
    <div v-if="!onlyShow" slot="footer">
      <el-button size="mini" @click="handleClose">{{ $t('btns.cancel') }}</el-button>
      <el-button type="primary" size="mini" @click="handleConfirm">{{ $t('btns.confirmKey') }}</el-button>
    </div>
  </cnd-dialog>
</template>

<script>
import { AgGridVue } from '@ag-grid-community/vue'
import { request, MessageUtil, Format } from 'cnd-horizon-utils'
// import AutoWrap from '../../AutoWrap/index.js'
// import { handleGridColumnDefs } from './columnDefs'
import { AllModules } from '@ag-grid-enterprise/all-modules'
// import { AllModules } from '../../../utils/agGridModules'
// console.log('this.$t()', this.$t('grid.others.pleaseEnterTheContent'))
export default {
  name: 'DialogTable',
  components: {
    AgGridVue
  },
  props: {
    // 标题
    title: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '700px'
    },
    height: {
      type: Number,
      default: 450
    },
    // 是否插入body(嵌套时使用)
    appendToBody: {
      type: Boolean,
      default: true
    },
    // 弹窗显隐
    visible: {
      type: Boolean,
      default: false
    },
    // 是否单选
    radio: {
      type: Boolean,
      default: true
    },
    // 搜索框提示文字
    placeholder: {
      type: String,
      default: '请输入内容'
    },
    // 是否展示搜索框
    showSearch: {
      type: Boolean,
      default: false
    },
    // 是否需要分页
    showPagination: {
      type: Boolean,
      default: true
    },
    // 是否展示确定/取消
    onlyShow: {
      type: Boolean,
      default: false
    },
    // 禁用选择的key值
    disableSelectKey: {
      type: String,
      default: ''
    },
    // 禁用选择的数组
    disableSelected: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 接口调用成功的回调函数
    afterFetch: {
      type: Function,
      default: () => {}
    },
    selectChange: {
      type: Function,
      default: () => {}
    },
    customSelect: {
      type: Boolean,
      default: false
    },
    // ag-grid【列】配置
    columnDefs: {
      type: Array,
      default: () => {
        return [
          { field: 'test1', headerName: '表头1', width: 100 },
          { field: 'test2', headerName: '表头2', width: 100 }
        ]
      }
    },
    // agGrid表格显示全选按钮
    selectAll: {
      type: Boolean,
      default: true
    },
    // 其他请求参数
    otherParams: {
      type: Object,
      default: () => {
        return {}
      }
    },
    reqConfig: {
      type: Object,
      default: () => {
        return {
          url: '', // 接口api
          method: '', // 请求方法
          params: {}, // 请求参数
          keyword: '', // 查询关键字
          searchValue: '', // 搜索框默认值设置
          dataFormat: {}, // 数据格式
          updataUrl: false // 更新分页信息到路径上
        }
      }
    },
    suppressRowClickSelection: {
      type: Boolean,
      default: true
    },
    // 是否展示搜索框
    showDelete: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      modules: AllModules,
      gridColumnDefs: [],
      // grid
      gridOptions: {},
      rowData: [],
      selectedRightDataNodes: [],
      // 分页配置
      tempInitparams: {},
      pagination: {
        pageNo: 0,
        pageSize: 10,
        total: 0
      },
      filterText: '',
      // ag-grid【列】默认配置
      defColumnDefs: [
        {
          field: 'index',
          headerName: '',
          maxWidth: 40,
          minWidth: 40,
          checkboxSelection: true,
          headerCheckboxSelection: !this.radio,
          cellStyle: { 'justify-content': 'Center', textAlign: 'center' },
          cellClass: 'grid-cell-centered',
          // headerCheckboxSelection: this.selectAll ? true : false,
          // checkboxSelection: true,
          cellRenderer: (params) => {
            // let str = ''
            let isDisable = false
            if (this.disableSelected && this.disableSelected.length) {
              this.disableSelected.map(item => {
                if (item === params.data[this.disableSelectKey]) {
                  isDisable = true
                }
              })
            }
            if (this.disableSelectKey && isDisable) {
              params.eGridCell.children[0].children[0].style.setProperty('display', 'flex', 'important')
              params.eGridCell.children[0].children[0].children[0].children[1].style.background = '#e4e4e4'
            }
            // if (this.disableSelectKey && isDisable) {
            //   params.colDef.cellStyle = {
            //     'cursor': 'not-allowed'
            //   }
            //   params.node.setSelected(false)
            //   params.eGridCell.children[0].children[0].children[1].style.cursor = 'not-allowed'
            //   params.eGridCell.children[0].children[0].children[1].style.background = '#e4e4e4'
            //   params.eGridCell.children[0].children[0].children[1].setAttribute('title', '不可选')
            //   str = `<span class="ag-cell-value" title="不可选" ></span>`
            // } else {
            //   str = `<span class="ag-cell-value" ></span>`
            // }
            // return str
          }
          // rowDrag: true,
          // pinned: 'left',
        }
      ]
    }
  },
  computed: {
    // 接口地址
    url() {
      return this.reqConfig.url
    },
    //
    updataUrl() {
      return this.reqConfig.updataUrl
    },
    // 请求方法
    method() {
      return this.reqConfig.method || 'get'
    },
    // 请求参数
    params() {
      return this.reqConfig.params || {}
    },
    // 查询关键字
    keyword() {
      return this.reqConfig.keyword
    },
    // 查询内容
    searchValue() {
      return this.reqConfig.searchValue
    },
    // 数据格式
    dataFormat() {
      return this.reqConfig.dataFormat
    },
    // 当前grid配置
    curConfig() {
      // this.columnDefs.forEach(item => {
      //   if (!item.width) {
      //     item.width = 70
      //   }
      // })
      if (this.onlyShow) return this.columnDefs
      return this.defColumnDefs.concat(this.columnDefs)
    },
    // 单选/多选
    rowSelection() {
      // multiple / single
      return this.radio ? 'single' : 'multiple'
    }
  },
  watch: {
    visible(nv, ov) {
      if (nv) {
        // 获取初始分页配置
        this.tempInitparams = Format.deepClone(this.params)
        this.filterText = this.searchValue
        this.pagination = {
          pageNo: 0,
          pageSize: 10,
          total: 0
        }
        if (this.showPagination) {
          this.pagination.pageNo = this.tempInitparams.pageNo > -1 ? this.tempInitparams.pageNo : this.pagination.pageNo
          this.pagination.pageSize = this.tempInitparams.pageSize > -1 ? this.tempInitparams.pageSize : this.pagination.pageSize
        }
        this.queryTableList()
      } else {
        this.rowData = []
      }
    }
  },
  created() {
    // 初始化ag-grid
    this.initGridOptions()
  },
  methods: {
    initGridOptions() {
      // const columnCount = 0
      // const fillWidth = 0
      // const _fill = 0
      const _this = this
      // const hasSecMeter = this.curConfig.some(item => item.children)
      const autoGroupColumnDef = {
        headerName: 'Athlete', field: 'id', width: 200,
        cellRenderer: 'agGroupCellRenderer',
        cellRendererParams: {
          checkbox: function(params) {
            return params.node.group === true
          }
        }
      }
      const gridOptions = {
        suppressClipboardPaste: true,
        embedFullWidthRows: true,
        defaultColDef: {
          filter: true,
          sortable: true,
          resizable: true
        },
        rowHeight: 28,
        rowSelection: this.rowSelection, // multiple / single
        rowMultiSelectWithClick: true,
        autoGroupColumnDef: autoGroupColumnDef,
        overlayLoadingTemplate: `<span class="ag-overlay-loading-center">${this.$t('grid.others.dataLoading')}...</span>`,
        overlayNoRowsTemplate: `<span class="ag-overlay-loading-center">${this.$t('grid.others.noData')}<span>`,
        suppressRowClickSelection: this.suppressRowClickSelection,
        enableRangeSelection: true,
        onGridReady: function(params) {
          params.api.sizeColumnsToFit()
        },
        isRowSelectable: function(params) {
          let isDisable = false
          if (_this.disableSelected && _this.disableSelected.length) {
            _this.disableSelected.map(item => {
              if (item === params.data[_this.disableSelectKey]) {
                isDisable = true
              }
            })
          }
          return !(_this.disableSelectKey && isDisable)
        },
        onRowDoubleClicked: (e) => {
          // 单选的情况下可以双击直接 = 确定
          if (this.rowSelection === 'single') {
            this.handleClose([e.data])
            this.selectedRightDataNodes = []
          }
        }
      }
      this.gridColumnDefs = this.curConfig
      // this.gridColumnDefs = handleGridColumnDefs(this.curConfig)
      this.gridOptions = gridOptions
    },
    // 数据赋值
    assignData(rowData) {
      if (this.dataFormat) {
        if (this.dataFormat.key) {
          setTimeout(() => {
            this.gridOptions.api.setRowData(rowData.data[this.dataFormat.key])
          }, 300)
        } else {
          this.rowData = rowData.data
        }
        if (this.showPagination) {
          if (this.dataFormat.total) {
            this.pagination.total = rowData.data[this.dataFormat.total]
          } else {
            this.pagination.total = rowData.data.totalElements
          }
        }
      } else {
        this.rowData = rowData.data
        if (this.showPagination) {
          this.pagination.total = rowData.data.totalElements
        }
      }
    },
    // 格式化获取url
    formatUrl(params, url) {
      let urlStr = ''
      if (url.indexOf('{') > -1) {
        const urlArr = url.split('/')
        urlArr.forEach((item, index) => {
          if (index !== 0) {
            let temp
            if (item.indexOf('{') > -1) {
              temp = `/${params[item.substring(1, item.length - 1)]}`
            } else {
              temp = `/${item}`
            }
            urlStr += temp
          }
        })
      } else {
        urlStr = url
      }
      console.log('urlStr--', urlStr)
      // 需要将分页数据更新到路径上
      if (this.updataUrl) {
        urlStr = this.upDataUrl(urlStr)
      }
      return urlStr
    },
    // 获取列表
    queryTableList(search) {
      console.log('重新刷新当前接口')
      const params = {}
      if (this.gridOptions && this.gridOptions.api) {
        this.gridOptions.api.showLoadingOverlay()
      }
      // 有搜索框
      if (this.showSearch && this.keyword) {
        params[this.keyword] = this.filterText
      }
      // 有分页
      if (this.showPagination) {
        params.pageNo = this.pagination.pageNo
        params.pageSize = this.pagination.pageSize
        if (search) {
          params.pageNo = this.tempInitparams.pageNo
          params.pageSize = this.tempInitparams.pageSize
        }
      }
      console.log('this.otherParams: ', this.otherParams)
      const p = Format.deepClone(this.params)
      Object.assign(p, this.otherParams)
      Object.assign(p, params)
      const obj = { ...p }
      // 接口请求
      if (this.method.toLowerCase() === 'get') {
        request({
          url: this.formatUrl(obj, this.url),
          method: this.method,
          params: obj
        }).then(rowData => {
          this.assignData(rowData)
          this.$emit('afterFetch', rowData)
        }).catch(error => {
          console.log('查询错误：', error)
          this.rowData = []
        })
      } else {
        console.log('obj: ', obj)
        request({
          url: this.formatUrl(obj, this.url),
          method: this.method,
          data: obj
        }).then(rowData => {
          this.assignData(rowData)
          this.$emit('afterFetch', rowData)
        }).catch(error => {
          console.log('查询错误：', error)
          this.rowData = []
        })
      }
    },
    // 【右表】切换分页数据
    pageChange(obj) {
      this.pagination.pageNo = obj.page
      this.pagination.pageSize = obj.limit
      this.queryTableList()
    },
    // 分页数据更新到url路径上
    upDataUrl(v) {
      if (!this.pagination.pageNo || !this.pagination.pageSize) {
        return v
      }
      let urlArr = []
      let url = ''
      urlArr = v.split('/')
      urlArr.pop()
      urlArr.pop()
      url = urlArr.join('/')
      return `${url}/${this.pagination.pageNo}/${this.pagination.pageSize}`
    },
    // 确定选择
    handleConfirm() {
      if (this.selectedRightDataNodes.length === 0) {
        MessageUtil.warning(this.$t('grid.tips.selectAtLeastOne'))
      } else {
        this.handleClose(this.selectedRightDataNodes, 'confirm')
        this.selectedRightDataNodes = []
      }
    },
    // 头部删除按钮
    handleDelete() {
      if (this.selectedRightDataNodes.length === 0) {
        MessageUtil.warning(this.$t('grid.tips.selectAtLeastOne'))
      } else {
        this.handleClose(this.selectedRightDataNodes, 'delete')
        this.selectedRightDataNodes = []
      }
    },
    // 关闭/取消
    handleClose(value = [], type = 'close') {
      this.$emit('onSelect', value, type)
    },
    // 右表选择事件
    onSelectionChangedRightData() {
      if (this.customSelect) {
        this.$emit('selectChange', this.gridOptions.api)
        return
      }
      if (this.gridOptions && this.gridOptions.api) {
        const selectedNodes = this.gridOptions.api.getSelectedNodes()
        const selectedData = selectedNodes.map(node => node.data)
        this.selectedRightDataNodes = selectedData
      }
    },
    // ------------------右表格------------------
    initGrid_rightData() {
      // const hasSecMeter = this.curConfig.some(item => item.children)
      const vm = this
      const autoGroupColumnDef = {
        headerName: 'Athlete', field: 'id', width: 200,
        cellRenderer: 'agGroupCellRenderer',
        cellRendererParams: {
          checkbox: function(params) {
            return params.node.group === true
          }
        }
      }
      const gridOptions = {
        // columnDefs: handleGridColumnDefs(hasSecMeter,this.curConfig),
        rowHeight: 28,
        defaultColDef: {
          resizable: true
        },
        // suppressRowClickSelection: true,
        rowSelection: this.rowSelection, // multiple / single
        rowMultiSelectWithClick: true,
        autoGroupColumnDef: autoGroupColumnDef,
        overlayLoadingTemplate: `<span class="ag-overlay-loading-center">${this.$t('grid.others.dataLoading')}...</span>`,
        overlayNoRowsTemplate: `<span class="ag-overlay-loading-center">${this.$t('grid.others.noData')}<span>`,
        onGridReady: function(params) {
          // console.log(params)
          // this.gridApi = params.api;
          // this.columnApi = params.columnApi;
          params.api.sizeColumnsToFit()
        },
        isRowSelectable: function(params) {
          let isDisable = false
          if (vm.disableSelected && vm.disableSelected.length) {
            vm.disableSelected.map(item => {
              if (item === params.data[vm.disableSelectKey]) {
                isDisable = true
              }
            })
          }
          return !(vm.disableSelectKey && isDisable)
        }
      }
      this.gridOptions = gridOptions
    }
  }
}
</script>
