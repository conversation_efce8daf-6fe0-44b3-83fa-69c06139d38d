<template>
  <!--通用树形弹窗-->
  <cnd-dialog
    class="dialog-table-container"
    :title="title"
    :visible="visible"
    :show-close="true"
    :fullscreen="false"
    :append-to-body="appendToBody"
    width="700px"
    :height="height"
    @close="handleClose"
  >
    <template slot="content">
      <div v-if="showSearch">
        <el-input
          ref="searchInput"
          v-model="filterText"
          clearable
          :placeholder="isPlaceholder"
          suffix-icon="el-icon-search"
          size="mini"
          @keyup.native.stop.down="focusFirstChildren"
          @keyup.native.stop.tab="focusFirstChildren"
        />
      </div>
      <div class="dialog-tree">
        <div v-if="!localTreeData || localTreeData.length === 0" class="no-data">
          暂无数据
        </div>
        <div v-else>
          <el-tree
            ref="tree"
            show-checkbox
            :data="localTreeData"
            :props="treeProps"
            :check-strictly="checkStrictly"
            :filter-node-method="filterNode"
            :node-key="nodeKey"
            :default-expand-all="defaultExpandAll"
            :default-expanded-keys="defaultShowNodes"
            :class="{ 'no-final-stage-tree': noFinalStage }"
            @keyup.native.stop.up="handleTreeUp"
            @keyup.native.enter="handleTreeEnter"
            @check="checkGroupNode"
            @node-click="handleNodeClick"
          />
        </div>
      </div>
    </template>
    <template slot="footer">
      <el-button size="mini" @click="handleClose">{{
        $t("btns.cancel")
      }}</el-button>
      <el-button type="primary" size="mini" @click="handleConfirm">{{
        $t("btns.confirmKey")
      }}</el-button>
    </template>
  </cnd-dialog>
</template>

<script>
import { request, MessageUtil } from 'cnd-horizon-utils'
export default {
  name: 'CndDialogTree',
  props: {
    // 标题
    title: {
      type: String,
      default: ''
    },
    // 搜索框提示文字
    placeholder: {
      type: String,
      default: ''
    },
    // 弹窗显隐
    visible: {
      type: Boolean,
      default: false
    },
    // 高度
    height: {
      type: Number,
      default: 400
    },
    // 是否插入body(嵌套时使用)
    appendToBody: {
      type: Boolean,
      default: true
    },
    // 是否单选
    radio: {
      type: Boolean,
      default: true
    },
    // 是否展示搜索框
    showSearch: {
      type: Boolean,
      default: false
    },
    nodeKey: {
      type: String,
      default: 'id'
    },
    // 初级,非末级节点不可选
    noFinalStage: {
      type: Boolean,
      default: false
    },
    // 其他参数
    otherParams: {
      type: Object,
      default: () => {
        return {}
      }
    },
    bbClick: {
      type: Boolean,
      default: false
    },
    defaultExpandAll: {
      type: Boolean,
      default: false
    },
    checkStrictly: {
      type: Boolean,
      default: false
    },
    // 请求参数配置
    reqConfig: {
      type: Object,
      default: () => {
        return {
          url: '', // 接口api
          method: '', // 请求方法
          params: {}, // 请求参数
          keyword: '', // 查询关键字
          searchValue: '', // 搜索框默认值设置
          treeProps: {},
          dataFormat: {} // 数据格式
        }
      }
    },
    // 可选中的type数组
    typeArr: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 直接传入的树数据
    treeData: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 树属性配置
    treePropsConfig: {
      type: Object,
      default: () => {
        return {
          children: 'children',
          label: 'name'
        }
      }
    }
  },
  data() {
    return {
      curSelNodePartentClassName: null,
      changeByEnter: false, // node是否是因为enter改变
      checkedNodes_before: [], // 选中的值
      checkedNodes_current: [], // 选中的值
      filterText: '',
      localTreeData: [],
      // 当前选中数据
      selData: [],
      treeClickCount: 0,
      defaultShowNodes: [] // 默认展开id
    }
  },
  computed: {
    isPlaceholder() {
      return this.placeholder || this.$t('components.pleaseEnter')
    },
    // 接口地址
    url() {
      return this.reqConfig.url || ''
    },
    // 请求方法
    method() {
      return this.reqConfig.method || 'get'
    },
    // 请求参数
    params() {
      return this.reqConfig.params || {}
    },
    // 查询关键字
    keyword() {
      return this.reqConfig.keyword || 'param'
    },
    // 查询内容
    searchValue() {
      return this.reqConfig.searchValue || ''
    },
    // 树展示label字段
    treeProps() {
      // 优先使用传入的treePropsConfig
      if (this.treePropsConfig) {
        return this.treePropsConfig
      }
      return (
        this.reqConfig.treeProps || {
          children: 'children',
          label: 'name'
        }
      )
    },
    // 数据格式
    dataFormat() {
      return this.reqConfig.dataFormat || {}
    }
  },
  watch: {
    visible(nv, ov) {
      if (nv) {
        this.filterText = this.searchValue
        // 如果传入了treeData，则直接使用
        if (this.treeData && this.treeData.length > 0) {
          this.localTreeData = this.setDisabled(this.treeData)
          this.treeData.length > 0 && this.defaultShowNodes.push(this.treeData[0][this.nodeKey])
          // 聚焦第一个节点
          this.$nextTick(() => {
            this.focusFirstChildren()
          })
        } else {
          this.query()
        }
      } else {
        this.treeClickCount = 0
        this.localTreeData = []
        if (this.$refs.tree) {
          this.$refs.tree.setCheckedKeys([])
          this.$refs.tree.setCheckedNodes([])
        }
      }
    },
    filterText(val) {
      this.$refs.tree.filter(val)
    },
    // 监听treeData变化
    treeData: {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.localTreeData = this.setDisabled(newVal)
          newVal.length > 0 && this.defaultShowNodes.push(newVal[0][this.nodeKey])
          // 聚焦第一个节点
          this.$nextTick(() => {
            this.focusFirstChildren()
          })
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 节点点击事件 双击/单击
    handleNodeClick(data, node) {
      this.treeClickCount++
      if (this.treeClickCount >= 2) {
        return
      }
      this.timer = window.setTimeout(() => {
        if (this.treeClickCount === 1) {
          this.treeClickCount = 0
          // 单击事件处理
        } else if (this.treeClickCount > 1) {
          this.treeClickCount = 0
          // 双击事件处理
          // 单选，双击父级不返回，双击子级返回
          // 多选，双击父级返回，双击子级返回

          // 单选不能选中父节点
          if (this.typeArr.length > 0 && this.typeArr.indexOf(Number(data.type)) === -1) {
            return
          }
          if (this.bbClick || (!data.children && this.radio) || !this.radio) {
            this.handleClose([data])
          }
        }
      }, 300)
    },
    // 获取数据
    query() {
      // 有搜索框
      if (this.showSearch && this.keyword) {
        this.params[this.keyword] = this.filterText
      }
      // 接口请求
      const reqConfig = {
        url: this.formatUrl(this.params, this.url),
        method: this.method
      }
      if (this.method === 'get') {
        reqConfig.params = this.params
      } else {
        reqConfig.data = this.params
      }
      if (this.otherParams && Object.keys(this.otherParams).length > 0) { Object.assign(reqConfig, this.otherParams) }

      request(reqConfig)
        .then((res) => {
          // 设置不可选节点
          this.localTreeData = this.setDisabled(res.data)
          res.data && this.defaultShowNodes.push(res.data[0][this.nodeKey])
          // 聚焦第一个节点
          this.$nextTick(() => {
            this.focusFirstChildren()
          })
        })
        .catch((error) => {
          console.log('查询错误：', error)
        })
    },
    // 设置不可选节点
    setDisabled(arr) {
      if (this.dataFormat?.lastStageKey && this.dataFormat?.lastStageVal) {
        arr.forEach((item) => {
          if (
            item[this.dataFormat.lastStageKey] === this.dataFormat.lastStageVal
          ) {
            if (item.children && item.children.length > 0) {
              item.children = this.setDisabled(item.children)
            } else {
              item.disabled = true
            }
          }
        })
      }
      return arr
    },
    // 点击复选框事件
    checkGroupNode(item, node) {
      // 如果设置了noFinalStage且当前节点有子节点，则不允许选中
      if (this.noFinalStage && item.children && item.children.length > 0) {
        // 取消选中
        if (node.checkedKeys.includes(item.id)) {
          const newCheckedKeys = node.checkedKeys.filter(key => key !== item.id)
          this.$refs.tree.setCheckedKeys(newCheckedKeys)
          MessageUtil.warning('只能选择末级节点')
          return
        }
      }

      // tree默认是多选，全会保存
      // 复选框状态处理
      if (this.radio && node.checkedKeys.length > 0) {
        if (this.checkStrictly) {
          return this.$refs.tree.setCheckedKeys([item.id])
        }
        // 单选不能选中父节点
        if (this.typeArr.length > 0 && this.typeArr.indexOf(Number(item.type)) === -1) {
          this.$refs.tree.setCheckedKeys([])
          return
        }

        if (
          (item.children && item.children.length) ||
          (this.noFinalStage &&
            (Number(item.parentId) === -1 || Number(item.type) === -1))
        ) {
          this.$refs.tree.setCheckedKeys([])
          return
        }
        this.$refs.tree.setCheckedKeys([item.id])
      }
      // console.log('当前选中的节点是', item.label)
      if (this.changeByEnter) {
        this.changeByEnter = false
      }
      this.checkedNodes_before = this.checkedNodes_current
      this.checkedNodes_current = this.$refs.tree.getCheckedNodes()
      this.judgeAddOrDel(item)
    },
    // 判断增减
    judgeAddOrDel(curItem) {
      let curIndex = -1
      if (this.radio) this.selData = []
      if (this.selData && this.selData.length > 0) {
        this.selData.forEach((item, index) => {
          if (item.id === curItem.id) {
            curIndex = index
          }
        })
        if (curIndex !== -1) {
          this.$nextTick(() => {
            this.selData.splice(curIndex, 1)
          })
          return
        }
      }
      this.selData.push(curItem)
    },
    // 过滤
    filterNode(value, data) {
      if (!value) return true
      const labelField = this.treeProps.label || 'name'
      if (!data[labelField]) return false
      return data[labelField].indexOf(value) !== -1
    },
    // 方向上键
    handleTreeUp(e) {
      const parentClassName = e.srcElement.parentElement.className
      if (
        this.curSelNodePartentClassName === 'el-tree' &&
        parentClassName === 'el-tree'
      ) {
        this.$refs.searchInput.focus()
      }
      this.curSelNodePartentClassName = parentClassName
    },
    // enter键
    handleTreeEnter() {
      // 1：屏蔽回车选中事件(选中后给复原)
      this.changeByEnter = true
      this.checkedNodes_current = this.checkedNodes_before
      const curKeys = []
      this.checkedNodes_before.forEach((item) => {
        curKeys.push(item.id)
      })
      this.$refs.tree.setCheckedKeys(curKeys)

      // 2：延时500毫秒 然后关闭弹窗
      setTimeout(() => {
        this.handleConfirm()
      }, 500)
    },
    // 确定选择
    handleConfirm() {
      const checkedNodes = this.$refs.tree.getCheckedNodes()
      if (checkedNodes.length === 0) {
        MessageUtil.warning(this.$t('grid.tips.selectAtLeastOne'))
      } else {
        // 过滤掉非末级节点（如果设置了noFinalStage）
        let filteredNodes = checkedNodes
        if (this.noFinalStage) {
          filteredNodes = checkedNodes.filter((item) => {
            return !item.children || item.children.length === 0
          })

          // 如果过滤后没有节点，说明用户选择了非末级节点
          if (filteredNodes.length === 0 && checkedNodes.length > 0) {
            MessageUtil.warning('请选择末级节点')
            return
          }
        } else {
          // 原有逻辑：过滤掉非末级节点（仅在非checkStrictly模式下）
          filteredNodes = checkedNodes.filter((item) => {
            return (
              this.checkStrictly || !item.children || item.children.length === 0
            )
          })
        }
        this.handleClose(filteredNodes)
        // 重新聚焦
        // if (this.args && this.args.event && this.args.event.target) {
        //   this.args.event.target.focus()
        // }
      }
    },
    // 关闭/取消
    handleClose(value = []) {
      this.$emit('onSelect', value)
      this.$emit('update:visible', false)
    },
    // 聚焦在第一个选项
    focusFirstChildren() {
      if (
        this.$refs.tree &&
        this.$refs.tree.$children[0] &&
        this.$refs.tree.$children[0].$el
      ) {
        this.$nextTick(() => {
          this.$refs.tree.$children[0].$el.children[0].focus()
        })
      }
    },
    // 格式化获取url
    formatUrl(params = {}, url) {
      let urlStr = ''
      if (url.indexOf('{') > -1) {
        const urlArr = url.split('/')
        urlArr.forEach((item, index) => {
          if (index !== 0) {
            let temp
            if (item.indexOf('{') > -1) {
              temp = `/${params[item.substring(1, item.length - 1)]}`
            } else {
              temp = `/${item}`
            }
            urlStr += temp
          }
        })
      } else {
        urlStr = url
      }
      return urlStr
    }
  }
}
</script>
<style scoped lang="scss">

.dialog-table-container {
    // 末节点才显示选择框
    .el-tree-node{
      .is-leaf + .el-checkbox .el-checkbox__inner{
        display: inline-block;
      }
      .el-checkbox .el-checkbox__inner{
        display: none;
      }
    }
  }
  .dialog-tree{
    border: 1px solid #E6E8EB;
    overflow:auto;
    border-radius: 2px;
    margin-top: 10px;
    height: 100%;
  }
</style>

<style lang="scss">
/* 当设置了noFinalStage属性时，只显示末级节点的复选框 */
/* .no-final-stage-tree {
  .el-tree-node:not(.is-leaf) .el-checkbox {
    display: none !important;
  }
} */
</style>
