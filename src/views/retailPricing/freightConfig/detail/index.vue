<template>
  <div class="page-container">
    <cnd-dialog :title="$t('grid.tabs.basicInformation')" :visible="true" @close="onClose">
      <template slot="leftBtn">
        <el-button
          v-has:esc_stock_delivery_retail_steel_freight_save
          type="primary"
          size="mini"
          @click="save"
        >{{ $t('btns.save') }}</el-button>
        <el-button
          v-if="$route.query.type !== 'add'"
          v-has:esc_stock_delivery_retail_steel_freight_remove
          type="danger"
          size="mini"
          @click="remove"
        >
          {{ $t('btns.delete') }}
        </el-button>
        <el-button
          v-if="$route.query.type !== 'add'"
          type="primary"
          size="mini"
          @click="visibleDialog.annex = true"
        >{{ $t('btns.attachmentManagement') }}</el-button>
      </template>
      <template slot="content">
        <el-form
          ref="form"
          class="el-form-w100"
          label-width="90px"
          inline
          :model="form"
          :rules="rules"
          size="small"
        >
          <cnd-form-card-list :active-panel="activeCollapseName">
            <cnd-form-card class="mb-10" :title="$t('grid.tabs.basicInformation')" name="1">
              <el-row>
                <cnd-form-item :label="$t('grid.others.supplier')" prop="vSupplierName" :error-msg="rules.vSupplierName[0].message">
                  <horizon-search-select-item
                    v-model="form.vSupplierName"
                    type="customer"
                    :default-url="`/esc/customer/page`"
                    :placeholder="$t('components.pleaseSelect')"
                    :disabled="disabled"
                    @change="handleChange($event, 'sSupplierId','vSupplierName')"
                  />
                </cnd-form-item>
                <cnd-form-item label="运费加价" prop="sFreightPrice" :error-msg="rules.sFreightPrice[0].message">
                  <cnd-input-number v-model="form.sFreightPrice" :decimal-digit="2" clearable />
                </cnd-form-item>
                <cnd-form-item
                  label="经营单位"
                  prop="sManagementId"
                  :error-msg="rules.sManagementId[0].message"
                >
                  <el-select v-model="form.sManagementId" :disabled="disabled">
                    <el-option
                      v-for="item in orgList"
                      :key="item.sId"
                      :label="item.sName"
                      :value="item.sId"
                    />
                  </el-select>
                </cnd-form-item>
              </el-row>
            </cnd-form-card>
            <cnd-form-card class="mb-10" title="销售区域" name="2">
              <!-- <cnd-form-item label="销售区域" prop="sAreaName" :error-msg="rules.sAreaName[0].message">
                <el-input v-model="form.sAreaName" />
              </cnd-form-item> -->
              <el-row>
                <cnd-form-item :custom-width="12" label="销售区域" prop="sAreaName" :error-msg="rules.sAreaName[0].message">
                  <cnd-input-dialog
                    v-model="form.sAreaName"
                    :title="form.sAreaName"
                    placeholder="请选择"
                    @focus="treeArea = true"
                  />
                </cnd-form-item>
              </el-row>
            </cnd-form-card>
            <cnd-form-card class="mb-10" :title="$t('grid.tabs.systemInformation')" name="3">
              <el-row>
                <cnd-form-item :label="$t('grid.title.createdBy')" prop="vCreatorName">
                  <el-input v-model="form.vCreatorName" disabled clearable />
                </cnd-form-item>
                <cnd-form-item :label="$t('grid.title.createdAt')" prop="sCreateTime">
                  <el-date-picker
                    v-model="form.sCreateTime"
                    format="yyyy-MM-dd HH:mm:ss"
                    type="date"
                    disabled
                    :placeholder="$t('grid.title.createdAt')"
                  />
                </cnd-form-item>
                <cnd-form-item :label="$t('grid.title.modifiedBy')" prop="sModifierName">
                  <el-input v-model="form.sModifierName" disabled clearable />
                </cnd-form-item>
                <cnd-form-item :label="$t('grid.title.modifiedAt')" prop="sModifyTime">
                  <el-date-picker
                    v-model="form.sModifyTime"
                    format="yyyy-MM-dd HH:mm:ss"
                    type="date"
                    disabled
                    :placeholder="$t('grid.title.modifiedAt')"
                  />
                </cnd-form-item>
              </el-row>
            </cnd-form-card>
          </cnd-form-card-list>
        </el-form>
      </template>
    </cnd-dialog>
    <steel-annex-dialog
      :visible="visibleDialog.annex"
      append-to-body
      :biz-id="id"
      :disabled-btn="{ scan: false, del: false }"
      @onSelect="visibleDialog.annex = false"
    />
    <cnd-dialog
      v-if="treeArea"
      title="选择城市"
      :visible="treeArea"
      class="dialog-table-container"
      :show-close="true"
      :fullscreen="false"
      :append-to-body="true"
      width="700px"
      height="400px"
      @close="handleClose"
    >
      <template slot="content">
        <el-input
          ref="searchInput"
          v-model="filterText"
          clearable
          placeholder="请输入城市名称"
          suffix-icon="el-icon-search"
          size="mini"
          @keyup.native.stop.down="focusFirstChildren"
          @keyup.native.stop.tab="focusFirstChildren"
        />
        <div class="dialog-tree">
          <el-tree
            ref="treeRef"
            :data="defaultData"
            show-checkbox
            node-key="name"
            :filter-node-method="filterNode"
            :default-expanded-keys="defaultKeys"
            :default-checked-keys="defaultKeys"
            :props="defaultProps"
          />
        </div>
      </template>
      <template slot="footer">
        <el-button size="mini" @click="handleClose">{{
          $t("btns.cancel")
        }}</el-button>
        <el-button type="primary" size="mini" @click="handleConfirm">{{
          $t("btns.confirmKey")
        }}</el-button>
      </template>
    </cnd-dialog>
  </div>
</template>

<script>
import {
  freightConfigDetail,
  freightConfigSave,
  freightConfigRemove,
  areaTreeParam
} from '@/api/retailPricing/index.js'
import { getManagement } from '@/utils/auth'
import {
  getOrgDialog
} from '@/api/customerOutConfig'

export default {
  name: 'FreightConfigDetail',
  data() {
    return {
      visibleDialog: {
        annex: false
      },
      activeCollapseName: ['1', '2', '3'],
      form: {},
      rules: {
        vSupplierName: [
          { required: true, message: this.$t('components.pleaseSelect'), trigger: 'change' }
        ],
        sAreaName: [
          { required: true, message: this.$t('components.pleaseEnter'), trigger: 'change' }
        ],
        sFreightPrice: [
          { required: true, message: this.$t('components.pleaseEnter'), trigger: 'change' }
        ],
        sManagementId: [
          { required: true, message: this.$t('components.pleaseSelect'), trigger: 'change' }
        ]
      },
      orgList: [],
      dialogVisible: {
        annex: false
      },
      defaultData: [],
      defaultKeys: [],
      // 弹窗展示
      treeArea: false,
      filterText: ''
    }
  },
  computed: {
    id() {
      return this.$route.query.id
    },
    disabled() {
      return this.id
    },
    // 树展示label字段
    defaultProps() {
      return {
        children: 'children',
        label: 'name'
      }
    }
  },
  watch: {
    filterText(val) {
      this.$refs.treeRef.filter(val)
    }
  },
  mounted() {
    this.loadDict()
    this.loadDetail()
    this.loadAreaData()
  },
  methods: {
    loadDict() {
      getOrgDialog({}).then(res => {
        this.orgList = res.data.content
      })
    },
    loadAreaData() {
      areaTreeParam({
        sId: '01156',
        sType: '40'
      }).then((res) => {
        this.defaultData = res.data
        this.$nextTick(() => {
          this.focusFirstChildren()
        })
      })
    },
    loadDetail() {
      if (this.id) {
        freightConfigDetail(this.id).then(res => {
          this.form = {
            ...res.data
          }
          this.defaultKeys = (res.data.sAreaName).split(',')
          console.log('拿到默认数据keys', this.defaultKeys, res.data.sAreaName)
        })
      } else {
        if (this.$route.query.type === 'add') {
          this.form = {
            vSupplierName: '',
            sSupplierId: '',
            sAreaName: '',
            sFreightPrice: null,
            sManagementId: null
          }
          this.defaultKeys = []
        }
      }
      if (!this.form.sManagementId) {
        const manage = getManagement('all')
        if (manage.length === 1) {
          this.form.sManagementId = manage[0].sId
        }
      }
    },
    save() {
      // 手动验证树形结构是否有值
      if (this.form.sAreaName === '') {
        this.$message.error('请选择销售区域')
      } else {
        this.$refs.form.validate(valid => {
          if (valid) {
            if (this.id) {
              freightConfigSave(this.form).then(res => {
                this.$message.success(this.$t('tips.saveSuccess'))
                this.loadDetail()
              })
            } else {
              freightConfigSave(this.form).then(res => {
                this.$message.success(this.$t('tips.saveSuccess'))
                this.$tabDelete(
                  `/egl/freightConfigDetail/${res.data.sId}?id=${res.data.sId}&type=edit&name=零售运费配置【${res.data.vSupplierName}】&activeId=${localStorage.getItem('menuId')}&random=${Math.random()}`
                )
                this.loadDetail()
              })
            }
          }
        })
      }
    },
    remove() {
      this.$confirm(this.$t('tips.isItOkToDelete'), this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'info'
      }).then(() => {
        freightConfigRemove(this.id).then(() => {
          this.$tabDelete()
        })
      })
    },
    handleChange(val, key, key2) {
      if (key2) {
        this.form[key2] = val ? val.sName : ''
      }
      this.form[key] = val?.sId ?? ''
    },
    handleConfirm() {
      this.form.sAreaName = (this.$refs.treeRef.getCheckedKeys()).toString()
      this.defaultKeys = this.$refs.treeRef.getCheckedKeys()
      this.handleClose()
    },
    handleClose() {
      this.treeArea = false
      this.filterText = ''
    },
    focusFirstChildren() {
      if (
        this.$refs.treeRef &&
        this.$refs.treeRef.$children[0] &&
        this.$refs.treeRef.$children[0].$el
      ) {
        // console.log('this.$refs.tree--', this.$refs.tree)
        this.$nextTick(() => {
          this.$refs.treeRef.$children[0].$el.children[0].focus()
        })
      }
    },
    // 过滤
    filterNode(value, data) {
      if (!value) return true
      if (!data.name) return false
      return data[this.defaultProps.label].indexOf(value) !== -1
    }
  }
}
</script>
