<template>
  <formDialog
    :visible="visible"
    :form-items="formDialogItems"
    width="350px"
    height="60px"
    title="选择复制对象"
    @close="onClose"
    @success="onSuccess"
  />
</template>

<script>
import { fixedPriceBillCopy } from '@/api/retailPricing/index.js'
import formDialog from '@/components/formDialog'
export default {
  name: 'ForceApprovalDialog',
  components: {
    formDialog
  },
  props: {
    tableSelectData: {
      type: Object,
      required: true
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      formDialogItems: [
        {
          label: '销售区域',
          value: 'sAreaName',
          type: 'cndInputDialogItem',
          customWidth: 22,
          multiple: true,
          // firstCall: true
          defaultUrl: '/esc/stock/delivery/retail/fixed_price_bill/copy/area/dialog',
          option: {
            valueKey: 'sAreaName',
            seledLabel: 'sAreaName',
            value: 'sAreaName'
          },
          otherOptions: {
            // sName: this.tableSelectData ? this.tableSelectData[0].sAreaName : '',
            sRetailFixedPriceBillIds: this.tableSelectData ? [this.tableSelectData[0].sId] : ''
          }
        }
      ]
    }
  },
  methods: {
    onSuccess(data) {
      const param = {
        sAreaNames: data.sAreaName,
        sRetailFixedPriceBillIds: [this.tableSelectData[0].sId]
      }
      fixedPriceBillCopy(param).then((res) => {
        this.$message.success('成功')
      })
    },
    onClose() {
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
