<template>
  <div class="page-container">
    <p class="page-title">零售定价管理</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form
        ref="searchForm"
        :form-items="formItems"
        @search="onSearch"
        @searchValue="onSearchValue"
      />

      <div class="btn-group mt-10">
        <div class="text">
          零售定价管理列表
        </div>
        <div>
          <el-button
            v-has:esc_stock_delivery_retail_fixed_price_bill_copy
            type="primary"
            size="mini"
            @click="handleCopyClick()"
          >
            {{ $t('btns.copy') }}
          </el-button>
          <el-button
            v-has:esc_stock_delivery_retail_fixed_price_bill_price_approval
            type="primary"
            size="mini"
            @click="create()"
          >
            生成报价审批
          </el-button>
          <el-button
            v-has:esc_stock_delivery_retail_fixed_price_bill_enable
            type="primary"
            size="mini"
            :disabled="btnDisabled('enable')"
            @click="modifyStatus('1')"
          >启用</el-button>
          <el-button
            v-has:esc_stock_delivery_retail_fixed_price_bill_disable
            type="danger"
            size="mini"
            :disabled="btnDisabled('disable')"
            @click="modifyStatus('0')"
          >禁用</el-button>
          <importBtn
            v-has:esc_stock_delivery_retail_fixed_price_approval_import
            class="ml-10"
            action="/api/esc/stock/delivery/retail/fixed_price_approval/import"
            action-success-url="/esc/stock/delivery/retail/fixed_price_approval/importSuccessData"
            btn-text="导入今日报价"
            success-mark="retailFixedPriceApprovalIds"
            @success="successImport"
          />
          <export-btn
            v-has:esc_stock_delivery_retail_fixed_price_bill_export
            class="ml-10"
            file-name="零售定价管理"
            api-url="/esc/stock/delivery/retail/fixed_price_bill/export"
            :post-params="searchInfo"
            :export-config="exportConfig"
            btn-txt="导出清单"
            :isload-detail="true"
            :detail-params="detailParams"
          />
        </div>
      </div>
      <!-- 零售定价管理 -->
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :child-column-defs="childColumnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        :show-header-select="false"
        table-selection="multiple"
        row-key="sId"
        child-row-key="sId"
        children-list-key="_detail"
        is-subtable
        :show-child-select="false"
        @selectedChange="selectedChange"
        @childCellValueChanged="childCellValueChanged"
      />
    </div>
    <!-- 选择复制对象 -->
    <copySelect
      v-if="dialogVisible"
      :visible="dialogVisible"
      :table-select-data="tableSelectData"
      @close="onClose"
      @success="success"
    />
  </div>
</template>

<script>
import Vue from 'vue'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import importBtn from '@/components/importBtn'

import {
  fixedPriceBillPage,
  fixedPriceBillCreate,
  fixedPriceBillEnableOrDisable,
  fixedPriceBillDetailSave,
  pricingApprovalDetail
} from '@/api/retailPricing/index.js'
import {
  getGoodsDetailList
} from '@/api/contract'
import { deepClone } from '@/utils/common'
import { SteelFormat } from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
import { Middleware } from 'cndinfo-ui'
import { getDictList, getCnDitc } from '@/utils/dict'
// import { handleDict } from '@/utils/common'
import exportBtn from '@/components/exportBtnV2'
import copySelect from './copySelect.vue'
export default {
  name: 'PricingManage',
  components: { steelTradeAggrid, exportBtn, importBtn, copySelect },
  data() {
    return {
      searchInfo: null,
      options: {
        'dev.common.sheet.status': [],
        'sys.en-dis': []
      },
      formItems: [
        {
          label: this.$t('grid.others.warehouse'),
          value: 'sWarehouseId',
          type: 'cndInputDialogItem',
          defaultUrl: '/esc/warehouse/info/dialog/v2/page-dialog',
          option: { valueKey: 'sPath' }
        },
        {
          label: this.$t('grid.others.supplier'),
          value: 'sSupplierId',
          type: 'cndInputDialogItem',
          dialogType: 'customer',
          defaultUrl: '/esc/customer/page',
          option: { valueKey: 'sPath' }
        },
        {
          label: this.$t('grid.others.department'),
          value: 'sDeptIds',
          dialogType: 'depart',
          type: 'cndInputDialogItem',
          multiple: true,
          reserveKeyword: false
        },
        {
          label: '品名',
          value: 'sGoodsDetailId',
          type: 'elCascader',
          dict: [],
          props: {
            value: 'id',
            label: 'name',
            children: 'children'
          },
          placeholder: '请选择品名'
        },
        {
          label: '材质',
          value: 'sMaterial',
          type: 'cndInputDialog',
          defaultUrl: '/esc/goods/ext/attribute',
          otherOptions: {
            isMatchQuery: '1',
            sArtNameId: '120111111111112',
            sGoodsDetailId: ''
          },
          option: {
            value: 'sName'
          }
        },
        {
          label: '规格',
          value: 'sSpecifications',
          type: 'cndInputDialog',
          defaultUrl: '/esc/goods/ext/attribute',
          otherOptions: {
            isMatchQuery: '1',
            sArtNameId: '120111111111113',
            sGoodsDetailId: ''
          },
          option: {
            value: 'sName'
          }
        },
        {
          label: '定价状态',
          value: 'sIsEnable',
          type: 'elSelect',
          dict: 'sys.en-dis'
        },
        {
          label: '经营单位',
          value: 'sManagementId',
          type: 'cndInputDialog',
          dialogType: 'escOrg',
          itemType: 'occultation'
        },
        {
          label: this.$t('grid.title.createdAt'),
          value: ['sCreateTime', 'vCreateTimeTo'],
          placeholder: [this.$t('grid.others.pleaseSelectTheDate'), this.$t('grid.others.pleaseSelectTheDate')],
          unlinkPanels: true,
          type: 'elDatePicker'
        }
      ],

      columnDefs: [

        {
          headerName: this.$t('grid.others.department'),
          field: 'sDeptNames'
        },
        {
          headerName: this.$t('grid.others.item'),
          field: 'sGoodsDetailName'
        },
        {
          headerName: this.$t('grid.others.warehouse'),
          field: 'vWarehouseName'
        },
        {
          headerName: '销售区域',
          field: 'sAreaName'
        },
        {
          headerName: this.$t('grid.others.supplier'),
          field: 'vSupplierName'
        },
        {
          headerName: this.$t('grid.title.status'),
          field: 'sIsEnable',
          valueGetter: (params) => {
            return getCnDitc(params, this.options['sys.en-dis'], 'sIsEnable')
          }
        },
        {
          headerName: '当前基价',
          field: 'sBasicPrice',
          cellStyle: { textAlign: 'right' },
          valueGetter: params => {
            return SteelFormat.formatPrice(params.data.sBasicPrice)
          }
        },
        {
          headerName: '报价单状态',
          field: 'sPriceApprovalSheetStatus',
          valueGetter: (params) => {
            return getCnDitc(params, this.options['dev.common.sheet.status'], 'sPriceApprovalSheetStatus')
          }
        },
        {
          headerName: '价格生效时间',
          field: 'sEffectiveTime',
          minWidth: 150,
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sEffectiveTime)
          }
        },
        {
          headerName: '价格失效时间',
          field: 'sExpirationTime',
          minWidth: 150,
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sExpirationTime)
          }
        },
        {
          headerName: this.$t('grid.title.createdBy'),
          field: 'vCreatorName'
        },
        {
          headerName: this.$t('grid.title.createdAt'),
          field: 'sCreateTime',
          minWidth: 150,
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
          }
        },
        {
          headerName: this.$t('grid.title.modifiedBy'),
          field: 'vModifierName'
        },
        {
          headerName: this.$t('grid.title.modifiedAt'),
          field: 'sModifyTime',
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sModifyTime)
          }
        },
        {
          headerName: '经营单位',
          field: 'sManagementName'
        }
      ],
      childColumnDefs: [
        {
          headerName: this.$t('grid.others.item'),
          field: 'sGoodsDetailName'
        },
        {
          headerName: '材质',
          field: 'sMaterial'
        },
        {
          headerName: '规格',
          field: 'sSpecifications'
        },
        {
          headerName: '对比基价',
          field: 'sContrastBasicPrice',
          editable: true,
          cellStyle: { textAlign: 'right' },
          cellEditorFramework: Vue.extend(
            Middleware.createComponent(
              'CndInputNumber',
              {
                mark: 'sContrastBasicPrice',
                type: 'number',
                decimalDigit: 2,
                negative: true
              }
              // {
              //   blur: ({ event, rowData, middleware }) => {
              //     // this.$refs.aggrid.gridApi.refreshCells(rowData)
              //   }
              // }
            )
          ),
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.data.sContrastBasicPrice)
          }
        }
      ],
      rowData: [],
      tableSelectData: [],
      parentIndex: null,
      oldSearchValue: {},
      dialogVisible: false
    }
  },
  computed: {
    btnDisabled() {
      const allEnabled = this.tableSelectData.length && this.tableSelectData.every(item => item.sIsEnable === '1')
      const allDisabled = this.tableSelectData.length && this.tableSelectData.every(item => item.sIsEnable === '0')
      return function(v) {
        if (v === 'enable') {
          return !allDisabled
        } else if (v === 'disable') {
          return !allEnabled
        } else {
          return true
        }
      }
    },
    detailParams() {
      const sIds = []
      const selectIds = []
      this.rowData?.forEach(item => {
        sIds.push(item.sId)
        if (item && item._selected) {
          selectIds.push(item.sId)
        }
      })
      return {
        sIds,
        selectIds
      }
    }
  },
  created() {
    getGoodsDetailList({})
      .then(res => {
        this.formItems.find(el => el.value === 'sGoodsDetailId').dict = res.data
      })
  },
  mounted() {
    getDictList(this.options)
    this.onSearch(false)
  },
  methods: {
    onSearchValue(v) {
      const oldGoodsDetailId = this.setGoodsDetailId(this.oldSearchValue)
      const newGoodsDetailId = this.setGoodsDetailId(v)
      if (!newGoodsDetailId || (oldGoodsDetailId && newGoodsDetailId !== oldGoodsDetailId)) {
        this.resetSearchValues(v)
      }
      this.updateFormItemsGoodsDetailId(newGoodsDetailId)
      this.oldSearchValue = deepClone(v)
    },
    resetSearchValues(v) {
      v.sMaterial = ''
      v._previewsMaterial = ''
      v.sSpecifications = ''
      v._previewsSpecifications = ''
    },
    updateFormItemsGoodsDetailId(goodsDetailId) {
      if (!goodsDetailId) return
      ['sMaterial', 'sSpecifications'].forEach(value => {
        const index = this.formItems.findIndex(item => item.value === value)
        if (index !== -1) {
          this.formItems[index].otherOptions.sGoodsDetailId = goodsDetailId
        }
      })
    },
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      delete searchInfo['undefined']
      searchInfo.sGoodsDetailId = this.setGoodsDetailId(searchInfo)
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    setGoodsDetailId(v) {
      const { sGoodsDetailId } = v
      if (sGoodsDetailId?.length > 0) {
        return sGoodsDetailId[sGoodsDetailId.length - 1]
      } else {
        return undefined
      }
    },
    selectedChange(rowData) {
      this.tableSelectData = rowData.filter(item => item._selected)
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        fixedPriceBillPage(this.searchInfo, pagination).then(res => {
          this.rowData = res.data.page.content.map(item => {
            return {
              ...item,
              _selected: false,
              _selectedKeys: [],
              _detail: item.escRetailFixedPriceBillDetailVoList || []
            }
          })
          setTimeout(() => {
            if (this.parentIndex !== null) {
              this.$refs.aggrid.gridApi.getDisplayedRowAtIndex(this.parentIndex).setExpanded(true)
            }
          }, 0)
          resolve(res.data.page)
        }).catch(() => {
          this.rowData = []
          reject(0)
        })
      })
    },
    handleCopyClick() {
      if (!this.tableSelectData.length || this.tableSelectData.length > 1) {
        return this.$message.warning(this.$t('grid.others.pleaseCheckOneData'))
      }
      this.dialogVisible = true
    },
    onClose() {
      this.dialogVisible = false
    },
    create() {
      this.$refs.aggrid.stopChildEditing()
      if (!this.tableSelectData.length) {
        return this.$message.warning(this.$t('grid.others.pleaseSelectData'))
      }
      this.$confirm('确认生成报价审批？', this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'warning'
      }).then(() => {
        fixedPriceBillCreate(this.tableSelectData.map(item => item.sId)).then(res => {
          pricingApprovalDetail(res.data.retailFixedPriceApprovalId).then(v => {
            this.$router.push({
              path: `/pricingApprovalDetail/${v.data.sId}`,
              query: {
                id: v.data.sId,
                status: v.data.sSheetStatus,
                type: 'edit',
                name: `零售定价审批单【${v.data.sCode}】`,
                activeId: localStorage.getItem('menuId')
              }
            })
          })

          this.$message.success(this.$t('grid.others.successfulOperation'))
          this.$refs.aggrid.loadTableData()
        })
      })
    },
    childCellValueChanged(params) {
      this.parentIndex = params.data.parentId
      fixedPriceBillDetailSave(params.data).then(res => {
        this.$refs.aggrid.loadTableData()
        this.$message.success(this.$t('grid.others.editSuccessfully'))
      }).catch(() => {
        this.$refs.aggrid.gridApi.redrawRows()
      })
    },
    modifyStatus(isEnable) {
      const message = isEnable === '1' ? '启用定价单需N8审批，是否启用？' : '是否禁用定价？'
      this.$confirm(message, this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'warning'
      }).then(() => {
        fixedPriceBillEnableOrDisable(isEnable, this.tableSelectData.map(item => item.sId)).then(() => {
          this.$refs.aggrid.reloadTableData()
          this.$message.success(this.$t('grid.others.successfulOperation'))
        })
      })
    },
    successImport(res) {
      const retailFixedPriceApprovalIds = res.data.retailFixedPriceApprovalIds
      if (retailFixedPriceApprovalIds) {
        pricingApprovalDetail(retailFixedPriceApprovalIds).then(res => {
          const { sId, sSheetStatus, sCode } = res.data
          this.$router.push({
            path: `/pricingApprovalDetail/${sId}`,
            query: {
              id: sId,
              status: sSheetStatus,
              type: 'edit',
              name: `零售定价审批单【${sCode}】`,
              activeId: localStorage.getItem('menuId')
            }
          })
        })
      }
    }
  }
}
</script>
