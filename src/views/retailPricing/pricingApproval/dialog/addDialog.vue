
<template>
  <cnd-dialog
    title="新增基价"
    append-to-body
    width="80%"
    height="520"
    :fullscreen="false"
    :visible.sync="dialogVisible"
    @close="handleClose"
  >
    <template v-if="dialogVisible" slot="content">
      <steel-search-form
        ref="searchForm"
        :form-items="formItems"
        @search="onSearch"
        @searchValue="onSearchValue"
      />
      <auto-wrap style="margin-top: 10px;">
        <steelTradeAggrid
          ref="aggrid"
          :column-defs="columnDefs"
          :child-column-defs="childColumnDefs"
          :row-data="rowData"
          :load-data="loadData"
          :auto-load-data="false"
          :show-header-select="false"
          table-selection="multiple"
          row-key="sId"
          child-row-key="sId"
          children-list-key="_detail"
          is-subtable
          :show-child-select="false"
          @selectedChange="selectedChange"
        />
      </auto-wrap>
    </template>
    <template slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">{{ $t('btns.cancel') }}</el-button>
      <el-button type="primary" size="mini" @click="handleSelect">{{ $t('btns.confirm') }}</el-button>
    </template>
  </cnd-dialog>
</template>

<script>
import Vue from 'vue'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import {
  fixedPriceBillPage,
  pricingApprovalDetailCreate
} from '@/api/retailPricing/index.js'
import {
  getGoodsDetailList
} from '@/api/contract'
import { SteelFormat } from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
import { Middleware } from 'cndinfo-ui'
import { getDictList, getCnDitc } from '@/utils/dict'
import { deepClone } from '@/utils/common'
export default {
  components: { steelTradeAggrid },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: null
    },
    info: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      searchInfo: null,
      options: {
        'dev.common.sheet.status': [],
        'sys.en-dis': []
      },
      formItems: [
        {
          label: this.$t('grid.others.warehouse'),
          value: 'sWarehouseId',
          type: 'cndInputDialogItem',
          defaultUrl: '/esc/warehouse/info/dialog/v2/page-dialog',
          option: { valueKey: 'sPath' }
        },
        {
          label: this.$t('grid.others.supplier'),
          value: 'sSupplierId',
          type: 'cndInputDialogItem',
          dialogType: 'customer',
          defaultUrl: '/esc/customer/page',
          option: { valueKey: 'sPath' }
        },
        {
          label: this.$t('grid.others.department'),
          value: 'sDeptIds',
          dialogType: 'depart',
          type: 'cndInputDialogItem',
          multiple: true,
          reserveKeyword: false,
          hidden: true
        },
        {
          label: '品名',
          value: 'sGoodsDetailId',
          type: 'elCascader',
          dict: [],
          props: {
            value: 'id',
            label: 'name',
            children: 'children'
          },
          placeholder: '请选择品名'
        },
        {
          label: '材质',
          value: 'sMaterial',
          type: 'cndInputDialog',
          defaultUrl: '/esc/goods/ext/attribute',
          otherOptions: {
            isMatchQuery: '1',
            sArtNameId: '120111111111112',
            sGoodsDetailId: ''
          },
          option: {
            value: 'sName'
          }
        },
        {
          label: '规格',
          value: 'sSpecifications',
          type: 'cndInputDialog',
          defaultUrl: '/esc/goods/ext/attribute',
          otherOptions: {
            isMatchQuery: '1',
            sArtNameId: '120111111111113',
            sGoodsDetailId: ''
          },
          option: {
            value: 'sName'
          }
        }
        // {
        //   label: '定价状态',
        //   value: 'sIsEnable',
        //   type: 'elSelect',
        //   dict: 'sys.en-dis'
        // },
        // {
        //   label: '经营单位',
        //   value: 'sManagementId',
        //   type: 'cndInputDialog',
        //   dialogType: 'escOrg',
        //   itemType: 'occultation'
        // }
      ],

      columnDefs: [
        // {
        //   headerName: this.$t('grid.others.department'),
        //   field: 'sDeptNames'
        // },
        {
          headerName: this.$t('grid.others.item'),
          field: 'sGoodsDetailName'
        },
        {
          headerName: this.$t('grid.others.warehouse'),
          field: 'vWarehouseName'
        },
        {
          headerName: this.$t('grid.others.supplier'),
          field: 'vSupplierName'
        },
        {
          headerName: '定价状态',
          field: 'sIsEnable',
          valueGetter: (params) => {
            return getCnDitc(params, this.options['sys.en-dis'], 'sIsEnable')
          }
        },
        {
          headerName: '当前基价',
          field: 'sBasicPrice',
          cellStyle: { textAlign: 'right' },
          valueGetter: params => {
            return SteelFormat.formatPrice(params.data.sBasicPrice)
          }
        },
        // {
        //   headerName: '报价单状态',
        //   field: 'sPriceApprovalSheetStatus',
        //   valueGetter: (params) => {
        //     return getCnDitc(params, this.options['dev.common.sheet.status'], 'sPriceApprovalSheetStatus')
        //   }
        // },
        // {
        //   headerName: '价格生效时间',
        //   field: 'sEffectiveTime',
        //   minWidth: 150,
        //   valueFormatter(params) {
        //     return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sEffectiveTime)
        //   }
        // },
        // {
        //   headerName: '价格失效时间',
        //   field: 'sExpirationTime',
        //   minWidth: 150,
        //   valueFormatter(params) {
        //     return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sExpirationTime)
        //   }
        // },
        // {
        //   headerName: this.$t('grid.title.createdBy'),
        //   field: 'vCreatorName'
        // },
        // {
        //   headerName: this.$t('grid.title.createdAt'),
        //   field: 'sCreateTime',
        //   minWidth: 150,
        //   valueFormatter(params) {
        //     return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
        //   }
        // },
        // {
        //   headerName: this.$t('grid.title.modifiedBy'),
        //   field: 'vModifierName'
        // },
        {
          headerName: this.$t('grid.title.modifiedAt'),
          field: 'sModifyTime',
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sModifyTime)
          }
        },
        {
          headerName: '经营单位',
          field: 'sManagementName'
        }
      ],
      childColumnDefs: [
        // {
        //   headerName: this.$t('grid.others.item'),
        //   field: 'sGoodsDetailName'
        // },
        {
          headerName: '材质',
          field: 'sMaterial'
        },
        {
          headerName: '规格',
          field: 'sSpecifications'
        },
        {
          headerName: '对比基价',
          field: 'sContrastBasicPrice',
          editable: false,
          cellStyle: { textAlign: 'right' },
          cellEditorFramework: Vue.extend(
            Middleware.createComponent(
              'CndInputNumber',
              {
                mark: 'sContrastBasicPrice',
                type: 'number',
                decimalDigit: 2
              }
              // {
              //   blur: ({ event, rowData, middleware }) => {
              //     // this.$refs.aggrid.gridApi.refreshCells(rowData)
              //   }
              // }
            )
          ),
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.data.sContrastBasicPrice)
          }
        }
      ],
      rowData: [],
      tableSelectData: [],
      parentIndex: null,
      oldSearchValue: {}
    }
  },
  watch: {
    dialogVisible(val) {
      if (val) {
        this.$nextTick(() => {
          this.loadDict()
          this.onSearch()
        })
      }
    }
  },
  methods: {
    loadDict() {
      getDictList(this.options)
      getGoodsDetailList({})
        .then(res => {
          this.formItems.find(el => el.value === 'sGoodsDetailId').dict = res.data
        })
    },
    onSearchValue(v) {
      const oldGoodsDetailId = this.setGoodsDetailId(this.oldSearchValue)
      const newGoodsDetailId = this.setGoodsDetailId(v)
      if (!newGoodsDetailId || (oldGoodsDetailId && newGoodsDetailId !== oldGoodsDetailId)) {
        this.resetSearchValues(v)
      }
      this.updateFormItemsGoodsDetailId(newGoodsDetailId)
      this.oldSearchValue = deepClone(v)
    },
    resetSearchValues(v) {
      v.sMaterial = ''
      v._previewsMaterial = ''
      v.sSpecifications = ''
      v._previewsSpecifications = ''
    },
    updateFormItemsGoodsDetailId(goodsDetailId) {
      if (!goodsDetailId) return
      ['sMaterial', 'sSpecifications'].forEach(value => {
        const index = this.formItems.findIndex(item => item.value === value)
        if (index !== -1) {
          this.formItems[index].otherOptions.sGoodsDetailId = goodsDetailId
        }
      })
    },
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      delete searchInfo['undefined']
      searchInfo.sGoodsDetailId = this.setGoodsDetailId(searchInfo)
      searchInfo.sDeptIds = this.info.escRetailFixedPriceApprovalDetailInfoVos.length > 0 ? this.info.escRetailFixedPriceApprovalDetailInfoVos[0].sDeptIds : []
      searchInfo.isExistApproval = '0'
      searchInfo.sIsEnable = '0'
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    setGoodsDetailId(v) {
      const { sGoodsDetailId } = v
      if (sGoodsDetailId?.length > 0) {
        return sGoodsDetailId[sGoodsDetailId.length - 1]
      } else {
        return undefined
      }
    },
    selectedChange(rowData) {
      this.tableSelectData = rowData.filter(item => item._selected)
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        fixedPriceBillPage(this.searchInfo, pagination).then(res => {
          this.rowData = res.data.page.content.map(item => {
            return {
              ...item,
              _selected: false,
              _selectedKeys: [],
              _detail: item.escRetailFixedPriceBillDetailVoList || []
            }
          })
          // setTimeout(() => {
          //   if (this.parentIndex !== null) {
          //     this.$refs.aggrid.gridApi.getDisplayedRowAtIndex(this.parentIndex).setExpanded(true)
          //   }
          // }, 0)
          resolve(res.data.page)
        }).catch(() => {
          reject([])
        })
      })
    },
    handleClose() {
      this.$emit('close')
    },
    handleSelect() {
      this.$refs.aggrid.stopChildEditing()
      if (!this.tableSelectData.length) {
        return this.$message.warning(this.$t('grid.others.pleaseSelectData'))
      }
      pricingApprovalDetailCreate(this.tableSelectData.map(item => item.sId), this.id).then(res => {
        this.$message.success(this.$t('grid.others.successfulOperation'))
        this.$emit('success')
        this.handleClose()
      })
    }
  }
}
</script>
