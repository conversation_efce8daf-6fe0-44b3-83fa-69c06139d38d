
<template>
  <div class="page-container">
    <div class="page-title">零售定价审批单</div>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form
        ref="searchForm"
        :form-items="formItems"
        @search="onSearch"
      />
      <div class="btn-group mt-10">
        <div class="text">
          零售定价审批单列表
        </div>
        <div>
          <!-- <importBtn
            v-has:esc_stock_delivery_retail_fixed_price_approval_import
            class="mr-10"
            action="/api/esc/stock/delivery/retail/fixed_price_approval/import"
            action-success-url="/esc/stock/delivery/retail/fixed_price_approval/importSuccessData"
            btn-text="导入今日报价"
            success-mark="retailFixedPriceApprovalIds"
            @success="successImport"
          /> -->
          <el-button
            v-has:esc_stock_delivery_retail_fixed_price_approval_removes
            type="danger"
            size="mini"
            :disabled="removeDisabled"
            @click="removes"
          >{{ $t('btns.delete') }}</el-button>
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        table-selection="multiple"
        row-key="sId"
        @rowDoubleClicked="rowDoubleClicked"
        @selectedChange="selectedChange"
      />
    </div>
  </div>
</template>

<script>
import { Moment } from 'cnd-utils'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import importBtn from '@/components/importBtn'
import { getDictList, getCnDitc } from '@/utils/dict'
import {
  pricingApprovalPage,
  pricingApprovalRemoves,
  pricingApprovalDetail
} from '@/api/retailPricing/index.js'
const statusDict = [{
  'sCodeValue': '10',
  'sCodeName': '准备'
}, {
  'sCodeValue': '15',
  'sCodeName': '驳回'
}, {
  'sCodeValue': '30',
  'sCodeName': '待审'
}, {
  'sCodeValue': '70',
  'sCodeName': '已审核'
}]
export default {
  name: 'PricingApproval',
  components: { steelTradeAggrid, importBtn },
  data() {
    return {
      removeDisabled: true,
      searchInfo: null,
      rowData: [],
      options: {
        'dev.common.sheet.status': []
      },
      formItems: [
        {
          label: '审批单号',
          value: 'sCode',
          type: 'elInput'
        },
        {
          label: '报价日期',
          value: ['sCreateTime', 'vCreateTimeTo'],
          placeholder: [this.$t('grid.others.pleaseSelectTheDate'), this.$t('grid.others.pleaseSelectTheDate')],
          unlinkPanels: true,
          type: 'elDatePicker'
        },
        {
          label: this.$t('grid.title.status'),
          value: 'sSheetStatus',
          type: 'elSelect',
          dict: statusDict,
          itemType: 'occultation',
          placeholder: this.$t('grid.others.pleaseSelectStatus')
        },
        {
          label: '经营单位',
          value: 'sManagementId',
          type: 'cndInputDialog',
          dialogType: 'escOrg'
        },
        {
          label: this.$t('grid.others.supplier'),
          value: 'sSupplierIds',
          type: 'cndInputDialogItem',
          dialogType: 'customer',
          defaultUrl: '/esc/customer/page',
          option: { valueKey: 'sPath' },
          multiple: true,
          placeholder: this.$t('grid.others.pleaseSelectSupplier')
        }
      ],
      columnDefs: [
        {
          headerName: '审批单号',
          field: 'sCode'
        },
        {
          field: 'sSheetStatus',
          headerName: this.$t('grid.title.status'),
          valueGetter: params => {
            return getCnDitc(params, this.options['dev.common.sheet.status'], 'sSheetStatus')
          }
        },
        {
          headerName: '报价日期',
          field: 'sQuotePriceDate',
          minWidth: 150,
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD', params.data.sQuotePriceDate)
          }
        },
        {
          headerName: this.$t('grid.others.warehouse'),
          field: 'vWarehouseNames',
          minWidth: 150
        },
        {
          headerName: '销售区域',
          field: 'sAreaNames',
          minWidth: 200
        },
        {
          headerName: this.$t('grid.others.supplier'),
          field: 'vSupplierNames',
          minWidth: 150
        },
        {
          headerName: this.$t('grid.title.createdBy'),
          field: 'vCreatorName'
        },
        {
          headerName: this.$t('grid.title.createdAt'),
          field: 'sCreateTime',
          minWidth: 150,
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
          }
        },
        {
          headerName: this.$t('grid.title.modifiedBy'),
          field: 'vModifierName'
        },
        {
          headerName: this.$t('grid.title.modifiedAt'),
          field: 'sModifyTime',
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sModifyTime)
          }
        },
        {
          headerName: '经营单位',
          field: 'sManagementName'
        }
      ]
    }
  },
  created() {
    getDictList(this.options)
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        pricingApprovalPage(
          this.searchInfo,
          pagination
        ).then(res => {
          this.rowData = res.data.page.content.map(item => {
            item._selected = false
            return item
          })
          resolve(res.data.page)
        }).catch(() => {
          reject([])
        })
      })
    },
    successImport(res) {
      const retailFixedPriceApprovalIds = res.data.retailFixedPriceApprovalIds
      if (retailFixedPriceApprovalIds) {
        pricingApprovalDetail(retailFixedPriceApprovalIds).then(res => {
          this.toDetailPage(res.data)
        })
      }
    },
    toDetailPage(data) {
      const { sId, sSheetStatus, sCode } = data
      this.$router.push({
        path: `/pricingApprovalDetail/${sId}`,
        query: {
          id: sId,
          status: sSheetStatus,
          type: 'edit',
          name: `零售定价审批单【${sCode}】`,
          activeId: localStorage.getItem('menuId')
        }
      })
    },
    rowDoubleClicked(params) {
      this.toDetailPage(params.data)
    },
    selectedChange(list) {
      this.removeDisabled = true
      this.$refs.aggrid.getSelectedData(res => {
        if (res.length > 0) {
          this.removeDisabled = !res.every(item => ['10', '15'].includes(item.sSheetStatus))
        }
      })
    },
    removes() {
      this.$refs.aggrid.getSelectedData(res => {
        this.$confirm(this.$t('tips.isItOkToDelete'), this.$t('grid.others.prompt'), {
          confirmButtonText: this.$t('btns.confirm'),
          cancelButtonText: this.$t('btns.cancel'),
          type: 'info'
        }).then(() => {
          pricingApprovalRemoves(res.map(item => item.sId))
            .then(() => {
              this.$message.success(this.$t('grid.tips.deletionSuccess'))
              this.$refs.aggrid.reloadTableData()
            })
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
