<template>
  <div class="page-container">
    <cnd-dialog :title="$t('grid.tabs.basicInformation')" :visible="true" @close="onClose">
      <template slot="leftBtn">
        <el-button
          v-has:esc_stock_delivery_retail_fixed_price_approval_submit
          type="primary"
          size="mini"
          :disabled="isBusinessDisabled('submit', sSheetStatus)"
          @click="submit"
        >{{ $t('grid.others.submit') }}</el-button>
        <el-button
          v-has:esc_stock_delivery_retail_fixed_price_approval_remove
          type="danger"
          size="mini"
          :disabled="isBusinessDisabled('save', sSheetStatus)"
          @click="remove"
        >
          {{ $t('btns.delete') }}
        </el-button>
        <el-button
          v-has:esc_stock_delivery_retail_fixed_price_approval_cancel_submit
          type="warning"
          size="mini"
          :disabled="isBusinessDisabled('withdraw', sSheetStatus)"
          @click="withdraw"
        >撤销申请</el-button>
        <el-button
          type="primary"
          size="mini"
          @click="visibleDialog.annex = true"
        >{{ $t('btns.attachmentManagement') }}</el-button>
      </template>
      <template slot="content">
        <el-form
          ref="form"
          class="el-form-w100"
          label-width="90px"
          inline
          :model="form"
          :rules="rules"
          size="small"
        >
          <cnd-form-card-list :active-panel="activeCollapseName">
            <cnd-form-card class="mb-10" :title="$t('grid.tabs.basicInformation')" name="1">
              <el-row>
                <cnd-form-item label="审批单号" prop="sCode">
                  <el-input v-model="form.sCode" disabled clearable />
                </cnd-form-item>
                <cnd-form-item label="报价日期" prop="sQuotePriceDate">
                  <el-date-picker
                    v-model="form.sQuotePriceDate"
                    format="yyyy-MM-dd"
                    type="date"
                    disabled
                  />
                </cnd-form-item>
              </el-row>
            </cnd-form-card>
            <cnd-form-card
              :padding-rule="false"
              class="mb-10"
              title="基础信息"
              name="2"
            >
              <div class="btn-group">
                <div />
                <div>
                  <el-button
                    v-has:esc_stock_delivery_retail_fixed_price_approval_detail_create
                    type="primary"
                    size="mini"
                    :disabled="isBusinessDisabled('save', sSheetStatus)"
                    @click="add"
                  >{{ $t('btns.add') }}</el-button>
                  <el-button
                    v-has:esc_stock_delivery_retail_fixed_price_approval_detail_removes
                    type="danger"
                    size="mini"
                    :disabled="removeDisabled || isBusinessDisabled('save', sSheetStatus)"
                    @click="removes"
                  >
                    {{ $t('btns.delete') }}
                  </el-button>
                </div>
              </div>
              <steelTradeAggrid
                ref="aggrid"
                :column-defs="columnDefs"
                :row-data="rowData"
                :load-data="loadData"
                :auto-load-data="false"
                :auto-height="true"
                :paginationinif="false"
                table-selection="multiple"
                row-key="sId"
                @selectedChange="selectedChange"
                @rowClicked="rowClicked"
                @cellValueChanged="cellValueChanged"
              />
            </cnd-form-card>
            <cnd-form-card
              :padding-rule="false"
              class="mb-10"
              title="对比基价信息"
              name="3"
            >
              <steelTradeAggrid
                ref="aggridV2"
                :column-defs="columnDefsV2"
                :row-data="rowDataV2"
                :load-data="loadDataV2"
                :auto-load-data="false"
                :auto-height="true"
                table-selection="multiple"
                row-key="sId"
                @selectedChange="selectedChangeV2"
                @cellValueChanged="cellValueChangedV2"
              />
            </cnd-form-card>
            <cnd-form-card class="mb-10" :title="$t('grid.tabs.systemInformation')" name="4">
              <el-row>
                <cnd-form-item :label="$t('grid.title.createdBy')" prop="vCreatorName">
                  <el-input v-model="form.vCreatorName" disabled clearable />
                </cnd-form-item>
                <cnd-form-item :label="$t('grid.title.createdAt')" prop="sCreateTime">
                  <el-date-picker
                    v-model="form.sCreateTime"
                    format="yyyy-MM-dd HH:mm:ss"
                    type="date"
                    disabled
                    :placeholder="$t('grid.title.createdAt')"
                  />
                </cnd-form-item>
                <cnd-form-item :label="$t('grid.title.modifiedBy')" prop="vModifierName">
                  <el-input v-model="form.vModifierName" disabled clearable />
                </cnd-form-item>
                <cnd-form-item :label="$t('grid.title.modifiedAt')" prop="sModifyTime">
                  <el-date-picker
                    v-model="form.sModifyTime"
                    format="yyyy-MM-dd HH:mm:ss"
                    type="date"
                    disabled
                    :placeholder="$t('grid.title.modifiedAt')"
                  />
                </cnd-form-item>
                <cnd-form-item :label="$t('grid.title.status')">
                  <el-select v-model="form.sSheetStatus" disabled>
                    <el-option
                      v-for="item in options['dev.common.sheet.status']"
                      :key="item.sCodeValue"
                      :label="item.sCodeName"
                      :value="item.sCodeValue"
                    />
                  </el-select>
                </cnd-form-item>
                <cnd-form-item :label="$t('grid.others.effectiveTime')" prop="sRatifyDate">
                  <el-date-picker v-model="form.sRatifyDate" type="date" format="yyyy-MM-dd HH:mm:ss" disabled clearable />
                </cnd-form-item>
              </el-row>
            </cnd-form-card>
          </cnd-form-card-list>
        </el-form>
      </template>
    </cnd-dialog>
    <AddDialog
      :id="id"
      :dialog-visible="dialogVisible"
      :info="form"
      @success="onSuccess"
      @close="dialogVisible = false"
    />
    <steel-annex-dialog
      :visible="visibleDialog.annex"
      append-to-body
      :biz-id="id"
      :disabled-btn="{ scan: isBusinessDisabled('save', form.sSheetStatus), del: isBusinessDisabled('save', form.sSheetStatus) }"
      @onSelect="visibleDialog.annex = false"
    />
  </div>
</template>

<script>
import {
  pricingApprovalDetail,
  pricingApprovalRemove,
  pricingApprovalDetailAppendPage,
  pricingApprovalDetailModify,
  pricingApprovalDetailRemoves,
  pricingApprovalDetailAppendModify,
  pricingApprovalDetailSubmit,
  pricingApprovalDetailCancelSubmit
} from '@/api/retailPricing/index.js'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import businessMixin from '@/utils/businessMixin'
import Vue from 'vue'
import { Middleware } from 'cndinfo-ui'
import { getDictList } from '@/utils/dict'
import { SteelFormat } from 'cnd-horizon-utils'
import AddDialog from '../dialog/addDialog'
export default {
  name: 'PricingApprovalDetail',
  components: {
    steelTradeAggrid,
    AddDialog
  },
  mixins: [businessMixin],
  data() {
    return {
      visibleDialog: {
        annex: false
      },
      dialogVisible: false,
      activeCollapseName: ['1', '2', '3', '4'],
      form: {},
      options: {
        'dev.common.sheet.status': []
      },
      rowData: [],
      rowDataV2: [],
      columnDefs: [
        {
          headerName: this.$t('grid.others.warehouse'),
          field: 'vWarehouseName'
        },
        {
          headerName: '销售区域',
          field: 'sAreaName'
        },
        {
          headerName: this.$t('grid.others.item'),
          field: 'sGoodsDetailName'
        },
        {
          headerName: '基价',
          field: 'sBasicPrice',
          editable: () => {
            return !this.isBusinessDisabled('save', this.sSheetStatus)
          },
          cellStyle: { textAlign: 'right' },
          cellEditorFramework: Vue.extend(
            Middleware.createComponent(
              'CndInputNumber',
              {
                mark: 'sBasicPrice',
                type: 'number',
                decimalDigit: 2
              },
              {
                blur: ({ event, rowData, middleware }) => {
                  this.$refs.aggrid.gridApi.refreshCells(rowData)
                }
              }
            )
          ),
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.data.sBasicPrice)
          }
        },
        {
          headerName: '参考规格',
          field: 'sReferenceSpecifications',
          editable: () => {
            return !this.isBusinessDisabled('save', this.sSheetStatus)
          }
        },
        {
          headerName: this.$t('grid.others.supplier'),
          field: 'vSupplierName'
        },
        {
          headerName: this.$t('grid.others.department'),
          field: 'sDeptNames'
        }
      ],
      columnDefsV2: [
        {
          headerName: this.$t('grid.others.item'),
          field: 'sGoodsDetailName'
        },
        {
          headerName: '材质',
          field: 'sMaterial'
        },
        {
          headerName: '规格',
          field: 'sSpecifications'
        },
        {
          headerName: '对比基价',
          field: 'sContrastBasicPrice',
          editable: () => {
            return !this.isBusinessDisabled('save', this.sSheetStatus)
          },
          cellStyle: { textAlign: 'right' },
          cellEditorFramework: Vue.extend(
            Middleware.createComponent(
              'CndInputNumber',
              {
                mark: 'sContrastBasicPrice',
                type: 'number',
                decimalDigit: 2,
                negative: true
              }
              // {
              //   blur: ({ event, rowData, middleware }) => {
              //     // this.$refs.aggrid.gridApi.refreshCells(rowData)
              //   }
              // }
            )
          ),
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.data.sContrastBasicPrice)
          }
        }
      ],
      selectId: null,
      removeDisabled: true
    }
  },
  computed: {
    id() {
      return this.$route.query.id
    },
    sSheetStatus() {
      return this.form.sSheetStatus
    }
  },
  mounted() {
    this.loadDict()
    this.loadDetail()
  },
  methods: {
    loadDict() {
      getDictList(this.options)
    },
    loadDetail() {
      if (this.id) {
        pricingApprovalDetail(this.id).then(res => {
          this.form = res.data
          this.$nextTick(() => {
            this.$refs.aggrid.loadTableData()
          })
        })
      }
    },
    loadData() {
      return new Promise((resolve, reject) => {
        this.rowData = this.form.escRetailFixedPriceApprovalDetailInfoVos.map((item, index) => {
          return {
            ...item,
            _selected: this.selectId ? this.selectId === item?.sId : index === 0
          }
        })
        console.log('rowData', this.rowData)
        this.rowClicked({ data: this.rowData[0] })
        resolve(this.rowData)
      })
    },
    rowClicked(params) {
      if (params && params._isEditCell) { return }
      const { data } = params
      if (!data) {
        this.selectId = null
        this.$refs.aggridV2.loadTableData()
      } else if (data && this.selectId !== data.sId) {
        this.selectId = data.sId
        this.$refs.aggridV2.loadTableData()
      }
    },
    loadDataV2(pagination) {
      return new Promise((resolve, reject) => {
        pricingApprovalDetailAppendPage(this.selectId, pagination).then(res => {
          this.rowDataV2 = res.data.page.content.map(item => {
            return {
              ...item,
              _selected: false
            }
          })
          resolve(res.data.page)
        }).catch(() => {
          reject([])
        })
      })
    },
    selectedChange() {
      this.removeDisabled = true
      this.$refs.aggrid.getSelectedData(res => {
        if (res.length > 0) {
          this.removeDisabled = false
        }
      })
    },
    cellValueChanged({ data }) {
      if (+data.sBasicPrice > 0) {
        pricingApprovalDetailModify(data).then(res => {
          this.$message.success('修改成功')
        }).finally(() => {
          this.loadDetail()
        })
      } else {
        this.$message.error('基价需大于0')
        this.loadDetail()
      }
    },
    cellValueChangedV2({ data }) {
      if (data.sContrastBasicPrice !== '' && data.sContrastBasicPrice !== null) {
        pricingApprovalDetailAppendModify(data).then(res => {
          this.$message.success('修改成功')
        }).finally(() => {
          this.$refs.aggridV2.loadTableData()
        })
      } else {
        this.$message.error('请输入对比基价')
        this.$refs.aggridV2.loadTableData()
      }
    },
    add() {
      this.dialogVisible = true
    },
    onSuccess() {
      this.loadDetail()
    },
    remove() {
      this.$confirm(this.$t('tips.isItOkToDelete'), this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'info'
      }).then(() => {
        pricingApprovalRemove(this.id).then(() => {
          this.$tabDelete()
        })
      })
    },
    removes() {
      this.$refs.aggrid && this.$refs.aggrid.gridApi.stopEditing()
      this.$refs.aggridV2 && this.$refs.aggridV2.gridApi.stopEditing()
      this.$refs.aggrid.getSelectedData(res => {
        pricingApprovalDetailRemoves(res.map(item => item.sId)).then(() => {
          this.$message.success(this.$t('grid.tips.deletionSuccess'))
          this.loadDetail()
        })
      })
    },
    withdraw() {
      this.$confirm(this.$t('grid.tips.whetherToRevokeTheApplication'), this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'info'
      }).then(() => {
        pricingApprovalDetailCancelSubmit(this.id).then(() => {
          this.$message.success(this.$t('tips.withdrawalSuccessTag'))
        }).finally(() => {
          this.loadDetail()
        })
      })
    },
    submit() {
      this.$refs.aggrid && this.$refs.aggrid.gridApi.stopEditing()
      this.$refs.aggridV2 && this.$refs.aggridV2.gridApi.stopEditing()
      pricingApprovalDetailSubmit(this.id).then(() => {
        this.$message.success(this.$t('tips.submitSuccess'))
        this.loadDetail()
      })
    }
  }
}
</script>
