
<template>
  <div class="page-container">
    <div class="page-title">零售定价部门配置</div>
    <div class="layout-content auto-page-title flexV">

      <steel-search-form
        ref="searchForm"
        :form-items="formItems"
        @search="onSearch"
      />
      <div class="btn-group mt-10">
        <div class="text">
          零售定价部门配置列表
        </div>
        <div>
          <el-button
            v-has:esc_stock_delivery_retail_fixed_price_unit_add
            type="primary"
            size="mini"
            @click="create"
          >{{ $t('btns.add') }}</el-button>
          <el-button
            v-has:esc_stock_delivery_retail_fixed_price_unit_removes
            type="danger"
            size="mini"
            :disabled="removeDisabled"
            @click="removes"
          >{{ $t('btns.delete') }}</el-button>
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        table-selection="multiple"
        row-key="sId"
        @rowDoubleClicked="rowDoubleClicked"
        @selectedChange="selectedChange"
      />
    </div>
  </div>
</template>

<script>
import { Moment } from 'cnd-utils'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import {
  pricingDepartmentPage,
  pricingDepartmentRemoves
} from '@/api/retailPricing/index.js'
export default {
  name: 'PricingDepartment',
  components: { steelTradeAggrid },
  data() {
    return {
      removeDisabled: true,
      dialogVisibleadd: false,
      searchInfo: null,
      rowData: [],
      formItems: [
        {
          label: this.$t('grid.others.department'),
          value: 'sDeptId',
          type: 'cndInputDialog',
          dialogType: 'depart'
        },
        {
          label: this.$t('grid.others.supplier'),
          value: 'sSupplierId',
          type: 'cndInputDialogItem',
          dialogType: 'customer',
          defaultUrl: '/esc/customer/page',
          option: { valueKey: 'sPath' }
        },
        {
          label: '经营单位',
          value: 'sManagementId',
          type: 'cndInputDialog',
          dialogType: 'escOrg'
        }
        // {
        //   label: this.$t('grid.title.createdBy'),
        //   value: 'sCreator',
        //   type: 'cndInputDialog',
        //   placeholder: this.$t('grid.others.pleaseSelectTheCreator'),
        //   dialogType: 'applicant'
        // },
        // {
        //   label: this.$t('grid.title.createdAt'),
        //   value: ['sCreateTime', 'vCreateTimeTo'],
        //   placeholder: [this.$t('grid.others.pleaseSelectTheDate'), this.$t('grid.others.pleaseSelectTheDate')],
        //   unlinkPanels: true,
        //   type: 'elDatePicker'
        // }
      ],
      columnDefs: [
        {
          headerName: this.$t('grid.others.supplier'),
          field: 'vSupplierName'
        },
        {
          headerName: this.$t('grid.others.department'),
          field: 'sDeptNames',
          width: 300
        },
        {
          headerName: this.$t('grid.title.createdBy'),
          field: 'vCreatorName'
        },
        {
          headerName: this.$t('grid.title.createdAt'),
          field: 'sCreateTime',
          minWidth: 150,
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
          }
        },
        {
          headerName: this.$t('grid.title.modifiedBy'),
          field: 'sModifierName'
        },
        {
          headerName: this.$t('grid.title.modifiedAt'),
          field: 'sModifyTime',
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sModifyTime)
          }
        },
        {
          headerName: '经营单位',
          field: 'sManagementName'
        }
      ]
    }
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        pricingDepartmentPage(
          this.searchInfo,
          pagination
        ).then(res => {
          this.rowData = res.data.page.content.map(item => {
            item._selected = false
            return item
          })
          resolve(res.data.page)
        }).catch(() => {
          reject([])
        })
      })
    },
    create() {
      this.$router.push({
        path: `/pricingDepartmentDetail/add`,
        query: {
          type: 'add',
          name: `新增零售定价部门`,
          activeId: localStorage.getItem('menuId')
        }
      })
    },
    toDetailPage(data) {
      const { sId, sSheetStatus, vSupplierName } = data
      this.$router.push({
        path: `/pricingDepartmentDetail/${sId}`,
        query: {
          id: sId,
          status: sSheetStatus,
          type: 'edit',
          name: `零售定价部门配置【${vSupplierName}】`,
          activeId: localStorage.getItem('menuId')
        }
      })
    },
    rowDoubleClicked(params) {
      this.toDetailPage(params.data)
    },
    selectedChange(list) {
      this.removeDisabled = true
      this.$refs.aggrid.getSelectedData(res => {
        if (res.length > 0) {
          this.removeDisabled = false
        }
      })
    },
    removes() {
      this.$refs.aggrid.getSelectedData(res => {
        this.$confirm(this.$t('tips.isItOkToDelete'), this.$t('grid.others.prompt'), {
          confirmButtonText: this.$t('btns.confirm'),
          cancelButtonText: this.$t('btns.cancel'),
          type: 'info'
        }).then(() => {
          pricingDepartmentRemoves(res.map(item => item.sId))
            .then(() => {
              this.$message.success(this.$t('grid.tips.deletionSuccess'))
              this.$refs.aggrid.reloadTableData()
            })
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
