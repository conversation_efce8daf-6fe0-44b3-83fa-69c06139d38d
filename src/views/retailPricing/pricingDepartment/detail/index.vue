<template>
  <div class="page-container">
    <cnd-dialog :title="$t('grid.tabs.basicInformation')" :visible="true" @close="onClose">
      <template slot="leftBtn">
        <el-button
          v-has:esc_stock_delivery_retail_fixed_price_unit_save
          type="primary"
          size="mini"
          @click="save"
        >{{ $t('btns.save') }}</el-button>
        <el-button
          v-if="$route.query.type !== 'add'"
          v-has:esc_stock_delivery_retail_fixed_price_unit_remove
          type="danger"
          size="mini"
          @click="remove"
        >
          {{ $t('btns.delete') }}
        </el-button>
      </template>
      <template slot="content">
        <el-form
          ref="form"
          class="el-form-w100"
          label-width="90px"
          inline
          :model="form"
          :rules="rules"
          size="small"
        >
          <cnd-form-card-list :active-panel="activeCollapseName">
            <cnd-form-card class="mb-10" :title="$t('grid.tabs.basicInformation')" name="1">
              <el-row>
                <cnd-form-item :label="$t('grid.others.supplier')" prop="vSupplierName">
                  <horizon-search-select-item
                    v-model="form.vSupplierName"
                    type="customer"
                    :default-url="`/esc/customer/page`"
                    :placeholder="$t('components.pleaseSelect')"
                    :disabled="!!id"
                    @change="handleChange($event, 'sSupplierId','vSupplierName')"
                  />
                </cnd-form-item>
                <cnd-form-item :label="$t('grid.others.department')" prop="_sDeptName" :error-msg="rules._sDeptName[0].message">
                  <horizon-search-select-item
                    v-model="form._sDeptName"
                    type="depart"
                    multiple
                    reserve-keyword
                    :first-call="true"
                    :option="{seledLabel:'sName',valueKey:'sId',label:'sPath'}"
                    :placeholder="$t('components.pleaseSelect')"
                    @change="handleChangeSelect"
                  />
                </cnd-form-item>
                <cnd-form-item
                  label="经营单位"
                  prop="sManagementId"
                  :error-msg="rules.sManagementId[0].message"
                >
                  <el-select v-model="form.sManagementId" :disabled="!!id">
                    <el-option
                      v-for="item in orgList"
                      :key="item.sId"
                      :label="item.sName"
                      :value="item.sId"
                    />
                  </el-select>
                </cnd-form-item>
              </el-row>
            </cnd-form-card>
            <cnd-form-card class="mb-10" :title="$t('grid.tabs.systemInformation')" name="2">
              <el-row>
                <cnd-form-item :label="$t('grid.title.createdBy')" prop="vCreatorName">
                  <el-input v-model="form.vCreatorName" disabled clearable />
                </cnd-form-item>
                <cnd-form-item :label="$t('grid.title.createdAt')" prop="sCreateTime">
                  <el-date-picker
                    v-model="form.sCreateTime"
                    format="yyyy-MM-dd HH:mm:ss"
                    type="date"
                    disabled
                    :placeholder="$t('grid.title.createdAt')"
                  />
                </cnd-form-item>
                <cnd-form-item :label="$t('grid.title.modifiedBy')" prop="sModifierName">
                  <el-input v-model="form.sModifierName" disabled clearable />
                </cnd-form-item>
                <cnd-form-item :label="$t('grid.title.modifiedAt')" prop="sModifyTime">
                  <el-date-picker
                    v-model="form.sModifyTime"
                    format="yyyy-MM-dd HH:mm:ss"
                    type="date"
                    disabled
                    :placeholder="$t('grid.title.modifiedAt')"
                  />
                </cnd-form-item>
              </el-row>
            </cnd-form-card>
          </cnd-form-card-list>
        </el-form>
      </template>
    </cnd-dialog>
  </div>
</template>

<script>
import {
  pricingDepartmentDetail,
  pricingDepartmentSave,
  pricingDepartmentRemove
} from '@/api/retailPricing/index.js'
import { getManagement } from '@/utils/auth'
import {
  getOrgDialog
} from '@/api/customerOutConfig'
export default {
  name: 'PricingDepartmentDetail',
  data() {
    return {
      activeCollapseName: ['1', '2', '3'],
      form: {},
      rules: {
        vSupplierName: [
          { required: true, message: this.$t('components.pleaseSelect'), trigger: 'change' }
        ],
        _sDeptName: [
          { required: true, message: this.$t('components.pleaseSelect'), trigger: 'change' }
        ],
        sManagementId: [
          { required: true, message: this.$t('components.pleaseSelect'), trigger: 'change' }
        ]
      },
      orgList: [],
      dialogVisible: {
        annex: false
      }
    }
  },
  computed: {
    id() {
      return this.$route.query.id
    }
  },
  mounted() {
    this.loadDict()
    this.loadDetail()
  },
  methods: {
    loadDict() {
      getOrgDialog({}).then(res => {
        this.orgList = res.data.content
      })
    },
    loadDetail() {
      if (this.id) {
        pricingDepartmentDetail(this.id).then(res => {
          this.form = {
            ...res.data,
            _sDeptName: res.data.escRetailFixedPriceUnitDetailList.map(item => {
              return {
                sId: item.sDeptId,
                sName: item.sDeptName
              }
            }) || []
          }
        })
      } else {
        if (this.$route.query.type === 'add') {
          this.form = {
            vSupplierName: '',
            sSupplierId: '',
            escRetailFixedPriceUnitDetailList: [],
            sManagementId: null,
            _sDeptName: []
          }
        }
      }
      if (!this.form.sManagementId) {
        const manage = getManagement('all')
        if (manage.length === 1) {
          this.form.sManagementId = manage[0].sId
        }
      }
    },
    save() {
      this.$refs.form.validate(valid => {
        if (valid) {
          if (this.id) {
            pricingDepartmentSave(this.form).then(res => {
              this.$message.success(this.$t('tips.saveSuccess'))
              this.loadDetail()
            })
          } else {
            pricingDepartmentSave(this.form).then(res => {
              this.$message.success(this.$t('tips.saveSuccess'))
              this.$tabDelete(
                `/egl/pricingDepartmentDetail/${res.data.sId}?id=${res.data.sId}&type=edit&name=零售定价部门配置【${res.data.vSupplierName}】&activeId=${localStorage.getItem('menuId')}&random=${Math.random()}`
              )
              this.loadDetail()
            })
          }
        }
      })
    },
    remove() {
      this.$confirm(this.$t('tips.isItOkToDelete'), this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'info'
      }).then(() => {
        pricingDepartmentRemove(this.id).then(() => {
          this.$tabDelete()
        })
      })
    },
    handleChangeSelect(val) {
      this.form.escRetailFixedPriceUnitDetailList = this.form._sDeptName.map(item => {
        return {
          sDeptName: item.sName,
          sDeptId: item.sId
        }
      })
    },
    handleChange(val, key, key2) {
      if (key2) {
        this.form[key2] = val ? val.sName : ''
      }
      this.form[key] = val?.sId ?? ''
    }
  }
}
</script>
