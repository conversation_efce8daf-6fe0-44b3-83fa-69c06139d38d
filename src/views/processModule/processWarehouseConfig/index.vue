<template>
  <div class="page-container">
    <p class="page-title">加工仓库配置表</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form ref="searchForm" :form-items="formItems" @search="onSearch" />
      <div class="btn-group mt-10" style="display: flex">
        <div class="text">
          加工仓库配置表
        </div>
        <div>
          <el-button
            v-has:esc_process_warehouse_add
            type="primary"
            size="mini"
            @click="create"
          >
            {{ $t('btns.add') }}
          </el-button>
          <el-button
            v-has:esc_process_warehouse_delete
            type="danger"
            size="mini"
            class="mr-10"
            :disabled="delDisable"
            @click="removes"
          >
            {{ $t('btns.delete') }}
          </el-button>
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        table-selection="multiple"
        @selectedChange="handleFooterCount"
        @rowDoubleClicked="onRowDoubleClicked"
      />
    </div>
  </div>
</template>

<script>
import { SteelFormat } from 'cnd-horizon-utils'

import steelTradeAggrid from '@/components/steelTradeAggrid'
import {
  getDictet,
  getCnDitc
} from '@/api/logistics/saleDelivery/saleorder'
import {
  warehouseConfigPage,
  warehouseConfigRemoves
} from '@/api/processModule/processWarehouseConfig.js'
export default {
  name: 'ProcessWarehouseConfig',
  components: { steelTradeAggrid },
  data() {
    return {
      searchInfo: null,
      formItems: [
        {
          label: '原料仓',
          value: 'sOriginWarehouseId',
          type: 'cndInputDialog',
          defaultUrl: '/esc/warehouse/config/warehouse'
        },
        {
          label: '仓库简码',
          value: 'sOriginWarehouseCode',
          type: 'elInput'
        },
        {
          label: '经营单位',
          value: 'sManagementId',
          type: 'cndInputDialog',
          dialogType: 'escOrg'
        },
        {
          label: '工艺',
          value: 'sProcessType',
          type: 'elSelect',
          dict: 'esc.process.type'
        }
      ],
      columnDefs: [
        {
          headerName: '加工厂',
          field: 'vSupplierName',
          width: 162
        },
        {
          headerName: '原料仓',
          field: 'vOriginWarehouseName',
          width: '200px'
        },
        {
          headerName: '原料仓库简码',
          field: 'vOriginWarehouseCode'
        },
        {
          headerName: '加工仓',
          field: 'vProcessWarehouseName',
          width: '200px'
        },
        {
          headerName: '成品仓',
          field: 'vWarehouseName',
          width: '200px'
        },
        {
          headerName: '工艺',
          field: 'sProcessType',
          valueGetter: params => {
            return getCnDitc(params, this.selectOps['esc.process.type'], 'sProcessType')
          }
        },
        {
          headerName: '成品率',
          field: 'sProcessRate',
          valueFormatter: params => {
            return SteelFormat.toPercent(+params.value)
          }
        },
        {
          field: 'vManagementName',
          headerName: '经营单位'
        }
      ],
      rowData: [],
      selectOps: {
        'esc.process.type': []
      }
    }
  },
  created() {
    getDictet([
      'esc.process.type'
    ]).then(result => {
      this.selectOps['esc.process.type'] = result.data[0].dicts
    }).catch(() => {
    })
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        warehouseConfigPage(this.searchInfo, pagination).then(res => {
          this.rowData = res.data.content.map(item => {
            item._selected = false
            item._selectedKeys = []
            return item
          })
          resolve(res.data)
        }).catch(() => {
          reject([])
        })
      })
    },
    handleFooterCount(rowData) {
      // console.log(rowData)
    },
    create() {
      this.$router.push({
        path: '/ProcessWarehouseDetail/add',
        query: {
          name: '新增加工仓库',
          type: 'add',
          activeId: localStorage.getItem('menuId')
        }
      })
    },
    removes() {
      this.$refs.aggrid.getSelectedData(res => {
        if (!res.length) {
          this.$message.warning(this.$t('grid.others.pleaseSelectData'))
          return
        }
        this.$confirm(this.$t('grid.tips.isTheDeletionConfirmed'), this.$t('grid.others.prompt'), {
          confirmButtonText: this.$t('grid.others.yes'),
          cancelButtonText: this.$t('btns.no'),
          type: 'warning'
        }).then(() => {
          warehouseConfigRemoves(
            res.map(item => item.sDetailId)
          ).then(() => {
            this.$message.success(this.$t('grid.tips.deletionSuccess'))
            this.$refs.aggrid.reloadTableData()
          })
        })
      })
    },
    onRowDoubleClicked(params) {
      this.$router.push({
        path: `/ProcessWarehouseDetail/${params.data.sId}`,
        query: {
          Id: params.data.sId,
          type: 'edit',
          name: `加工仓库【${params.data.vOriginWarehouseCode}】`,
          activeId: localStorage.getItem('menuId')
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
