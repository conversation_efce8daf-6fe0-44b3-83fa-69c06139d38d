<template>
  <div>
    <div class="btn-group">
      <div class="text">
        <!-- 加工工艺 -->
      </div>
      <div>
        <el-button
          v-has:esc_process_warehouse_detail_add
          type="primary"
          size="mini"
          @click="add"
        >
          {{ $t('btns.add') }}
        </el-button>
      </div>
    </div>
    <steelTradeAggrid
      ref="aggrid"
      :column-defs="columnDefs"
      :row-data="rowData"
      :heightinif="150"
      row-key="sId"
      :table-selection="null"
      :paginationinif="false"
      :auto-load-data="false"
      :load-data="loadData"
      :full-row-type="'parent'"
      @rowValueChanged="rowValueChanged"
    />
  </div>
</template>

<script>
import Vue from 'vue'
import { Middleware } from 'cndinfo-ui'
import { SteelFormat } from 'cnd-horizon-utils'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import businessMixin from '@/utils/businessMixin'
import {
  getCnDitc
} from '@/api/logistics/saleDelivery/saleorder'
// import {
//   warehouseConfigDetail,
//   warehouseConfigDetailRemove,
//   warehouseConfigDetailAdd,
//   warehouseConfigDetailModify
// } from '@/api/processModule/processWarehouseConfig.js'
export default {
  components: { steelTradeAggrid },
  mixins: [businessMixin],
  props: {
    id: {
      type: String,
      default: null
    },
    info: {
      type: Object,
      default: () => {}
    },
    processType: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dialogVisible: false,
      columnDefs: [
        {
          headerName: '工艺名',
          field: 'sProcessType',
          editable: true,
          width: '200',
          valueGetter: params => {
            return getCnDitc(params, this.processType, 'sProcessType')
          },
          cellEditorFramework: Vue.extend(Middleware.createComponent(
            'AgGridSelect',
            {
              mark: 'sProcessType',
              config: {
                label: 'sCodeName',
                value: 'sCodeValue'
              },
              filterable: false,
              remote: false,
              searchLimit: 0,
              placeholder: this.$t('grid.others.pleaseEnterTheSearch'),
              queryMethod: this.getProcessType
            },
            {
              getOption: (option, params, middleware) => {

              }
            }
          ))
        },
        {
          headerName: '成品率',
          field: 'sProcessRate',
          width: '200',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.toPercent(params.data.sProcessRate)
          },
          editable: true,
          cellEditorFramework: Vue.extend(Middleware.createComponent(
            'CndInputNumber',
            {
              mark: 'sProcessRate',
              type: 'percent',
              decimalDigit: 6,
              autoFocus: true,
              focusSelect: true
            }
          ))
        },
        {
          headerName: this.$t('grid.others.operation'),
          field: '',
          maxWidth: 100,
          cellStyle: { textAlign: 'lfet' },
          onCellClicked: (params) => {
            this.del(params)
          },
          cellRenderer: (params) => {
            const sHtml = '<span style=color:red;cursor:pointer>' + this.$t('btns.delete') + '</span>'
            return sHtml
          }
        }
      ],
      rowData: []
    }
  },
  computed: {
    sSheetStatus() {
      return this.info.sSheetStatus
    }
  },
  methods: {
    getProcessType() {
      return new Promise((resolve, reject) => {
        resolve(this.processType)
      })
    },
    load() {
      this.$refs.aggrid.reloadTableData()
    },
    getData() {
      return this.rowData
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        this.rowData = this.info.configDetailVos.map(item => {
          item._selected = false
          return item
        })
        resolve(this.info.configDetailVos)
      })
    },
    add() {
      const params = {
        sProcessType: '',
        sProcessRate: '0',
        sId: '',
        sWhouseFactoryConfigId: this.id
      }
      this.rowData.push(params)
      const rowIndex = this.rowData.length - 1
      this.resetRowChange(rowIndex, 'sProcessType')
    },
    resetRowChange(rowIndex, value) {
      this.$nextTick(() => {
        this.$refs.aggrid.gridApi.setFocusedCell(rowIndex, value)
        this.$refs.aggrid.gridApi.startEditingCell({
          rowIndex,
          colKey: value
        })
      })
    },
    rowValueChanged(params) {
      if (!params.data.sProcessType) {
        this.$message.error('工艺名不能为空')
        this.resetRowChange(params.rowIndex, 'sProcessType')
        return false
      }
      this.$refs.aggrid.refreshTable()
      // const api = params.data.sId ? warehouseConfigDetailModify : warehouseConfigDetailAdd
      // api(params.data).then(res => {
      //   this.$message.success(this.$t('grid.others.editSuccessfully'))
      //   this.$refs.aggrid.reloadTableData()
      // })
    },
    del(e) {
      if (e.data.sId) {
        this.rowData = this.rowData.filter(item => item.sId !== e.data.sId)
      } else {
        this.rowData.splice(e.rowIndex, 1)
      }
      this.$refs.aggrid.refreshTable()
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
