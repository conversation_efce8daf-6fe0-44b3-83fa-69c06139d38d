<template>
  <el-form
    ref="form"
    class="el-form-w100"
    label-width="100px"
    inline
    :model="form"
    :rules="rules"
    size="small"
  >
    <cnd-form-card-list :active-panel="activeCollapseName">
      <cnd-form-card :title="$t('grid.tabs.basicInformation')" name="1">
        <el-row>
          <cnd-form-item
            label="加工厂"
            prop="vSupplierName"
            :error-msg="rules.vSupplierName[0].message"
          >
            <horizon-search-select
              v-model="form.vSupplierName"
              type="customer"
              :placeholder="$t('components.pleaseSelect')"
              @change="handleChangeSelect($event, 'sSupplierId')"
            />
          </cnd-form-item>
          <cnd-form-item
            label="原料仓"
            prop="vOriginWarehouseName"
            :error-msg="rules.vOriginWarehouseName[0].message"
          >
            <horizon-search-select
              v-model="form.vOriginWarehouseName"
              :placeholder="$t('components.pleaseSelect')"
              :default-url="`/esc/warehouse/info/dialog/v2/page-dialog`"
              :other-options="{
                wfcFlag: true
              }"
              @change="handleChangeSelect($event, 'sOriginWarehouseId')"
            />
          </cnd-form-item>
          <cnd-form-item
            label="加工仓"
            prop="vProcessWarehouseName"
            :error-msg="rules.vProcessWarehouseName[0].message"
          >
            <horizon-search-select
              v-model="form.vProcessWarehouseName"
              :default-url="`/esc/warehouse/info/dialog/v2/page-dialog`"
              :placeholder="$t('components.pleaseSelect')"
              :other-options="{
                wfcFlag: true
              }"
              @change="handleChangeSelect($event, 'sProcessWarehouseId')"
            />
          </cnd-form-item>
          <cnd-form-item
            label="成品仓"
            prop="vWarehouseName"
            :error-msg="rules.vWarehouseName[0].message"
          >
            <horizon-search-select
              v-model="form.vWarehouseName"
              :default-url="`/esc/warehouse/info/dialog/v2/page-dialog`"
              :placeholder="$t('components.pleaseSelect')"
              :other-options="{
                wfcFlag: true
              }"
              @change="handleChangeSelect($event, 'sWarehouseId')"
            />
          </cnd-form-item>
          <cnd-form-item
            label="经营单位"
            prop="sManagementId"
            :error-msg="rules.sManagementId[0].message"
          >
            <el-select v-model="form.sManagementId">
              <el-option
                v-for="item in orgList"
                :key="item.sId"
                :label="item.sName"
                :value="item.sId"
              />
            </el-select>
          </cnd-form-item>
        </el-row>
      </cnd-form-card>
      <cnd-form-card class="goods-detail-card" title="加工工艺" name="2">
        <goodsDetail
          :id="id"
          ref="goods"
          :info="form"
          :process-type="processType"
        />
      </cnd-form-card>
      <cnd-form-card :title="$t('grid.tabs.systemInformation')" name="3">
        <el-row>
          <cnd-form-item :label="$t('grid.title.createdBy')" prop="vCreatorName">
            <el-input v-model="form.vCreatorName" disabled clearable />
          </cnd-form-item>
          <cnd-form-item :label="$t('grid.title.createdAt')" prop="sCreateTime">
            <el-date-picker
              v-model="form.sCreateTime"
              format="yyyy-MM-dd HH:mm:ss"
              type="date"
              disabled
              :placeholder="$t('grid.title.createdAt')"
            />
          </cnd-form-item>
          <cnd-form-item :label="$t('grid.title.modifiedBy')" prop="vModifierName">
            <el-input v-model="form.vModifierName" disabled clearable />
          </cnd-form-item>
          <cnd-form-item :label="$t('grid.title.modifiedAt')" prop="sModifyTime">
            <el-date-picker
              v-model="form.sModifyTime"
              format="yyyy-MM-dd HH:mm:ss"
              type="date"
              disabled
              :placeholder="$t('grid.title.modifiedAt')"
            />
          </cnd-form-item>
        </el-row>
      </cnd-form-card>
    </cnd-form-card-list>
  </el-form>
</template>

<script>
import {
  warehouseConfigAdd,
  warehouseConfigModify
} from '@/api/processModule/processWarehouseConfig.js'
import {
  getDictet
} from '@/api/logistics/saleDelivery/saleorder'
import goodsDetail from './goodsDetail'
export default {
  components: {
    goodsDetail
  },
  props: {
    id: {
      type: String,
      default: null
    },
    info: {
      type: Object,
      default: () => {}
    },
    orgList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      activeCollapseName: ['1', '2', '3'],
      rules: {
        vSupplierName: [
          { required: true, message: this.$t('components.pleaseSelect'), trigger: 'change' }
        ],
        vOriginWarehouseName: [
          { required: true, message: this.$t('components.pleaseSelect'), trigger: 'change' }
        ],
        vProcessWarehouseName: [
          { required: true, message: this.$t('components.pleaseSelect'), trigger: 'change' }
        ],
        vWarehouseName: [
          { required: true, message: this.$t('components.pleaseSelect'), trigger: 'change' }
        ],
        sManagementId: [
          { required: true, message: this.$t('components.pleaseSelect'), trigger: 'change' }
        ]
      },
      form: {},
      processType: []
    }
  },
  watch: {
    info: {
      immediate: true,
      handler(val) {
        this.form = val
        this.$nextTick(() => {
          setTimeout(() => {
            this.$refs['goods']?.load()
          }, 0)
        })
      }
    }
  },
  beforeCreate() {
    getDictet([
      'esc.process.type'
    ]).then(result => {
      this.processType = result.data[0].dicts
    }).catch(() => {
    })
  },

  methods: {
    save() {
      return new Promise((resolve, reject) => {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.$refs['goods'].$refs.aggrid.gridApi.stopEditing()
            this.form.configDetailVos = this.$refs['goods'].getData()
            const api = this.id ? warehouseConfigModify : warehouseConfigAdd
            api(this.form).then(res => {
              this.$message.success(this.$t('tips.saveSuccess'))
              if (!this.id) {
                this.$tabDelete(
                  `/egl/ProcessWarehouseDetail/${res.data.sId}?Id=${res.data.sId}&type=edit&name=加工仓库【${res.data.vOriginWarehouseCode}】&activeId=${localStorage.getItem('menuId')}&random=${Math.random()}`
                )
              } else {
                resolve()
              }
            }).catch(() => {
              return false
            })
          } else {
            return false
          }
        })
      })
    },
    handleChangeSelect(selected, val) {
      this.form[val] = selected ? selected.sId : undefined
    }
  }
}
</script>
<style lang="scss">
.goods-detail-card{
  .el-collapse-item {
    position: relative;
    .el-collapse-item__content{
      padding: 0;
    }
  }
}
</style>
