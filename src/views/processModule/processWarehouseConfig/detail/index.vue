<template>
  <div class="page-container">
    <cnd-dialog
      :title="$t('grid.tabs.basicInformation')"
      :visible="true"
      @close="onClose"
    >
      <template slot="leftBtn">
        <el-button
          v-has:esc_process_warehouse_detail_modify
          type="primary"
          size="mini"
          @click="save"
        >{{ $t('btns.save') }}</el-button>
      </template>
      <template slot="content">
        <baseInfo
          :id="id"
          ref="basic"
          :info="form"
          :org-list="orgList"
          @success="getDetail"
        />
        <!-- <el-tabs v-model="activeName" :before-leave="beforeLeave">
          <el-tab-pane :label="$t('grid.tabs.basicInformation')" name="basic">
            <baseInfo
              :id="id"
              ref="basic"
              :info="form"
              :org-list="orgList"
              @success="getDetail"
            />
          </el-tab-pane>
          <el-tab-pane label="加工工艺" name="goods">
            <goodsDetail
              :id="id"
              ref="goods"
              :info="form"
            />
          </el-tab-pane>
        </el-tabs> -->
      </template>
    </cnd-dialog>
  </div>
</template>

<script>
import baseInfo from './basicInfo'
import goodsDetail from './goodsDetail'
import {
  warehouseConfigGet
} from '@/api/processModule/processWarehouseConfig.js'
import {
  getOrgDialog
} from '@/api/customerOutConfig'
export default {
  name: 'ProcessWarehouseDetail',
  components: {
    baseInfo,
    goodsDetail
  },
  data() {
    return {
      id: null,
      activeName: 'basic',
      oldForm: null,
      form: {},
      orgList: []
    }
  },
  created() {
    this.id = this.$route.query.Id
    this.loadDict()
    this.loadDetail()
  },
  methods: {
    loadDict() {
      getOrgDialog({}).then(res => {
        this.orgList = res.data.content
      })
    },
    loadDetail() {
      if (this.id) {
        warehouseConfigGet({
          sId: this.id
        }).then(res => {
          this.form = res.data
          this.oldForm = JSON.stringify(res.data)
        })
      } else {
        this.form = {
          vSupplierName: null,
          sSupplierId: null,
          vOriginWarehouseName: null,
          sOriginWarehouseId: null,
          vProcessWarehouseName: null,
          sProcessWarehouseId: null,
          vWarehouseName: null,
          sWarehouseId: null,
          sManagementId: null,
          configDetailVos: []
        }
      }
    },
    // beforeLeave(activeName, oldActiveName) {
    //   const blFlag = new Promise((resolve, reject) => {
    //     if (activeName === 'basic') {
    //       this.loadDetail()
    //       resolve()
    //     }
    //     if (oldActiveName === 'basic' && this.oldForm !== JSON.stringify(this.form)) {
    //       this.$refs.basic.save().then(res => {
    //         this.loadDetail()
    //         resolve()
    //       })
    //     } else {
    //       resolve()
    //     }
    //   })
    //   return blFlag
    // },
    save() {
      this.$refs.basic.save().then(res => {
        this.loadDetail()
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
