<template>
  <div>
    <div class="btn-group">
      <div class="text">
        计划成品数量
      </div>
      <div>
        <el-button
          v-has:esc_process_arrival_project_product_add
          type="primary"
          size="mini"
          :disabled="btnDisabled"
          @click="dialogVisible= true"
        >
          {{ $t('btns.add') }}
        </el-button>
        <el-button
          v-has:esc_process_arrival_project_product_delete
          type="danger"
          size="mini"
          :disabled="btnDisabled"
          @click="remove"
        >
          {{ $t('btns.delete') }}
        </el-button>
        <el-button
          v-has:esc_process_arrival_project_product_export
          type="primary"
          size="mini"
          :disabled="disabled"
          @click="exportAll"
        >
          导出剩余计划原材料
        </el-button>
      </div>
    </div>
    <steelTradeAggrid
      ref="aggrid"
      :column-defs="columnDefs"
      :row-data="rowData"
      :heightinif="150"
      row-key="sId"
      table-selection="single"
      :paginationinif="false"
      :load-data="loadData"
      @selectedChange="selectedChange"
    />
    <planfinishedDialog
      :id="id"
      :info="info"
      :goods-detail-id="goodsDetailId"
      :dialog-visible="dialogVisible"
      @success="successDialog"
      @close="closeDialog"
    />
  </div>
</template>

<script>
// var Decimal = window.Decimal
import steelTradeAggrid from '@/components/steelTradeAggrid'
import businessMixin from '@/utils/businessMixin'
import { SteelFormat } from 'cnd-horizon-utils'
import {
  processPlanList,
  processPlanRemoves
} from '@/api/processModule/finishedProductArrival.js'
import { exportAll } from '@/api/export'
import planfinishedDialog from '../dialog/planfinishedDialog.vue'
export default {
  components: { steelTradeAggrid, planfinishedDialog },
  mixins: [businessMixin],
  props: {
    id: {
      type: String,
      default: null
    },
    info: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dialogVisible: false,
      columnDefs: [
        {
          headerName: this.$t('grid.others.itemNumberTag'),
          field: 'sProjectCode'
        },
        {
          headerName: this.$t('grid.title.purchaseContractNumber'),
          field: 'sContractCode'
        },
        {
          headerName: this.$t('grid.title.salesContractNumber'),
          field: 'sSaleContractCode'
        },
        {
          headerName: '加工任务单号',
          field: 'sTaskAllotCode'
        },
        {
          headerName: this.$t('grid.others.item'),
          field: 'sGoodsDetailName'
        },
        {
          headerName: '计划成品数量',
          field: 'sPlanQtx',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: '计划成品件数',
          field: 'sPlanQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value)
          }
        }
      ],
      rowData: [],
      goodsDetailId: null,
      selectData: null
    }
  },
  computed: {
    sSheetStatus() {
      return this.info?.sSheetStatus
    },
    btnDisabled() {
      const { sSheetStatus, sIsExtBalance, sIsEditEnable } = this.info
      return this.isBusinessDisabled('save', sSheetStatus) || (sIsExtBalance === '1' && sIsEditEnable === '0')
    },
    disabled() {
      return !this.rowData.length || this.btnDisabled
    }

  },
  methods: {
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        processPlanList(
          { sUpId: this.id }
        ).then(res => {
          this.rowData = res.data.map((item, index) => {
            item._selected = index === 0
            return item
          })
          this.goodsDetailId = res.data.map(e => e.sGoodsDetailId)?.toString()
          resolve(res.data)
        }).catch(() => {
          reject([])
        })
      })
    },
    remove() {
      this.$refs.aggrid.getSelectedData(res => {
        if (!res) {
          this.$message.warning(this.$t('grid.others.pleaseSelectData'))
          return false
        }
        processPlanRemoves([res.sId], this.id).then(e => {
          this.$message({
            message: this.$t('tips.deletedSuccessfully'),
            type: 'success'
          })
          this.$emit('update')
          this.$refs.aggrid.reloadTableData()
        })
      })
    },
    successDialog() {
      this.$emit('update')
      this.$refs.aggrid.reloadTableData()
    },
    closeDialog() {
      this.dialogVisible = false
    },
    selectedChange(list) {
      this.$refs.aggrid.getSelectedData(res => {
        this.selectData = res
        this.$emit('refresh', res)
      })
    },
    exportAll() {
      exportAll(`/esc/process/arrival/detail/export/${this.rowData[0].sId}`, {}).then(res => {
        try {
          const enc = new TextDecoder('utf-8')
          const jsonString = enc.decode(new Uint8Array(res))
          res = JSON.parse(jsonString)
          this.$message.error(res.message)
        } catch (err) {
          const link = document.createElement('a')
          const blob = new Blob([res], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
          link.style.display = 'none'
          link.href = URL.createObjectURL(blob)
          link.download = `剩余计划原材料`
          document.body.appendChild(link)
          link.click()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
