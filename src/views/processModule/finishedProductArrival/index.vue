<template>
  <div class="page-container">
    <p class="page-title">成品到货单管理</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form
        ref="searchForm"
        :form-items="formItems"
        @search="onSearch"
      />
      <div class="btn-group mt-10">
        <div class="text">
          成品到货单列表
        </div>
        <div>
          <el-button
            v-has:esc_process_arrival_page_delete
            type="danger"
            size="mini"
            :disabled="disRemove"
            @click="deleteData"
          >{{ $t('btns.delete') }}</el-button>
          <export-btn
            class="ml-10"
            file-name="成品到货单"
            api-url="/esc/process/arrival/export"
            child-row-key="sId"
            children-list-key="detailList"
            :post-params="searchInfo"
            :export-config="exportConfig"
            :get-grid-api="getGridApi"
            :isload-detail="true"
            :detail-params="detailParams"
          />
          <el-dropdown v-has:esc_process_arrival_page_import split-button size="mini" type="primary" class="ml-10">
            {{ $t('grid.others.importMatch') }}
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item>
                <importBtn
                  type="one"
                  btn-text="直接导入"
                  action="/api/esc/process/arrival/import"
                  action-success-url="/esc/process/arrival/importSuccessData"
                  success-mark="processArrivalIds"
                  @success="successImport"
                />
              </el-dropdown-item>
              <el-dropdown-item>
                <importBtn
                  type="two"
                  is-sync
                  :action="`/api/esc/process/arrival/asy/import`"
                  btn-text="后台导入"
                />
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
      <!-- 表格 -->
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :child-column-defs="childColumnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :load-detail="loadDetail"
        child-row-key="sId"
        children-list-key="detailList"
        :header-total="headerCount"
        :footer-total="footerCount"
        :auto-load-data="false"
        :show-header-select="false"
        table-selection="multiple"
        is-subtable
        @selectedChange="handleFooterCount"
        @rowDoubleClicked="rowDoubleClicked"
      />
    </div>
  </div>
</template>

<script>
import {
  processArrivalPage,
  processArrivalRemoves,
  processArrivalChildPage,
  processArrivalGet
} from '@/api/processModule/finishedProductArrival.js'
import { handleDict } from '@/utils/common'
import exportBtn from '@/components/exportBtnV2'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import {
  getDictet,
  getCnDitc
} from '@/api/logistics/saleDelivery/saleorder'
import importBtn from '@/components/importBtn'

import { SteelFormat } from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
import { statusDict } from '@/utils/dict'

export default {
  name: 'FinishedProductArrivalManage',
  components: {
    steelTradeAggrid,
    exportBtn,
    importBtn
  },
  data() {
    return {
      disRemove: true,
      options: {
        'dev.common.sheet.status': '',
        'base.yes-no': ''
      },
      columnDefs: [
        {
          headerName: '成品到货单号',
          field: 'sCode'
        },
        {
          headerName: this.$t('grid.title.status'),
          field: 'sSheetStatus',
          valueGetter: (params) => {
            return getCnDitc(params, this.options['dev.common.sheet.status'], 'sSheetStatus')
          }
        },
        {
          headerName: this.$t('grid.others.quantityNumberOfPieces'),
          field: 'vSumQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            const data = params.data
            return `${SteelFormat.formatThousandthSign(data.vSumQty, 4)}/${SteelFormat.formatThousandthSign(data.vSumPkgQty)}`
          }
        },
        {
          headerName: this.$t('grid.title.amount'),
          field: 'sAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: '加工厂',
          field: 'vSupplierName',
          width: 162
        },
        {
          headerName: this.$t('grid.others.warehouse'),
          field: 'vWarehouseName'
        },
        {
          headerName: this.$t('grid.title.company'),
          field: 'vCompanyName'
        },
        {
          headerName: '是否二次加工',
          field: 'sExtend5',
          valueGetter: (params) => {
            return getCnDitc(params, this.options['base.yes-no'], 'sExtend5')
          }
        },
        {
          headerName: this.$t('grid.title.createdBy'),
          field: 'vCreatorName'
        },
        {
          headerName: this.$t('grid.title.createdAt'),
          field: 'sCreateTime',
          minWidth: 150,
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.value)
          }
        },
        {
          field: 'vManagementName',
          headerName: '经营单位'
        }
      ],
      childColumnDefs: [
        {
          headerName: this.$t('grid.others.itemNumberTag'),
          field: 'sProjectCode'
        },
        {
          headerName: this.$t('grid.title.purchaseContractNumber'),
          field: 'sPurContractCode'
        },
        {
          headerName: this.$t('grid.title.salesContractNumber'),
          field: 'sSaleContractCode'
        },
        {
          headerName: this.$t('grid.others.item'),
          field: 'vArtName'
        },
        {
          headerName: this.$t('grid.others.steelCoilNumber'),
          field: 'sSteelNo'
        },
        {
          headerName: this.$t('grid.others.quantityNumberOfPieces'),
          field: 'sQtx',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            const data = params.data
            return `${SteelFormat.formatThousandthSign(data.sQtx, 4)}/${SteelFormat.formatThousandthSign(data.sQty)}`
          }
        },
        {
          headerName: this.$t('grid.title.unitPrice'),
          field: 'sPrice',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: this.$t('grid.title.amount'),
          field: 'sTaxAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: this.$t('grid.title.accountingGroup'),
          field: 'vCheckGroupName'
        },
        {
          headerName: this.$t('grid.title.personnel'),
          field: 'vStaffName'
        },
        {
          headerName: '加工任务号',
          field: 'sTaskAllotCode'
        }
      ],
      rowData: [],
      headerCount: null,
      footerCount: null,
      searchInfo: null,
      formItems: [
        {
          label: '成品到货单号',
          value: 'sCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.title.purchaseContractNumber'),
          value: 'sPurContractCode',
          type: 'elInput'
        },
        {
          label: '加工厂',
          value: 'sSupplierId',
          type: 'cndInputDialogItem',
          dialogType: 'customer',
          defaultUrl: '/esc/customer/page',
          option: { valueKey: 'sPath' },
          customerType: ''
        },
        {
          label: this.$t('grid.others.warehouse'),
          value: 'sWarehouseId',
          type: 'cndInputDialogItem',
          defaultUrl: '/esc/warehouse/info/dialog/v2/page-dialog',
          option: { valueKey: 'sPath' }
        },
        {
          label: '加工任务号',
          value: 'sUpCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.title.company'),
          value: 'sCompanyId',
          type: 'cndInputDialog',
          dialogType: 'company',
          placeholder: this.$t('grid.others.pleaseSelectCompany')
        },
        {
          label: this.$t('grid.title.status'),
          value: 'sSheetStatus',
          type: 'elSelect',
          dict: statusDict
        },
        {
          label: this.$t('grid.title.accountingGroup'),
          value: 'sCheckGroupId',
          type: 'cndInputDialog',
          dialogType: 'cost'
        },
        {
          label: this.$t('grid.title.personnel'),
          value: 'sStaffId',
          type: 'cndInputDialogItem',
          option: { valueKey: 'sPath' },
          dialogType: 'staff',
          placeholder: this.$t('grid.others.pleaseSelectPersonnel')
        },
        {
          label: this.$t('grid.title.createdBy'),
          value: 'sCreator',
          type: 'cndInputDialog',
          dialogType: 'applicant',
          itemType: 'occultation',
          placeholder: this.$t('grid.others.pleaseSelectTheCreator')
        },
        {
          label: this.$t('grid.title.createdAt'),
          value: ['sCreateTime', 'vCreateTimeTo'],
          placeholder: [this.$t('grid.others.pleaseSelectTheDate'), this.$t('grid.others.pleaseSelectTheDate')],
          type: 'elDatePicker',
          unlinkPanels: true,
          itemType: 'occultation'
        },
        {
          label: '经营单位',
          value: 'sManagementId',
          type: 'cndInputDialog',
          dialogType: 'escOrg',
          itemType: 'occultation'
        },
        {
          label: this.$t('grid.others.itemNumberTag'),
          value: 'sProjectCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.others.steelCoilNumber'),
          value: 'sSteelNo',
          type: 'elInput'
        },
        {
          label: '是否二次加工',
          value: 'sExtend5',
          type: 'elSelect',
          dict: 'base.yes-no'
        }
      ],
      exportConfig: [
        {
          label: this.$t('grid.others.itemNumberTag'),
          value: 'sProjectCode'
        },
        {
          label: this.$t('grid.title.purchaseContractNumber'),
          value: 'sPurContractCode'
        },
        {
          label: this.$t('grid.title.salesContractNumber'),
          value: 'sSaleContractCode'
        },
        { label: this.$t('grid.others.item'), value: 'vArtName' },
        {
          label: this.$t('grid.others.steelCoilNumber'),
          value: 'sSteelNo'
        },
        {
          label: this.$t('grid.title.quantity'),
          value: 'sQtx',
          setValue: (value) => {
            return Number((+value).toFixed(4))
          }
        },
        {
          label: this.$t('grid.others.numberOfPiecesTag'),
          value: 'sQty'
          // setValue: (value) => {
          //   return SteelFormat.formatThousandthSign(value)
          // }
        },
        {
          label: this.$t('grid.title.unitPrice'),
          value: 'sPrice',
          setValue: value => { return Number((+value).toFixed(2)) }
        },
        {
          label: this.$t('grid.title.amount'),
          value: 'sTaxAmt',
          setValue: value => { return Number((+value).toFixed(2)) }
        },
        {
          label: '等级',
          value: 'sLevel'
        },
        {
          label: this.$t('grid.title.accountingGroup'),
          value: 'vCheckGroupName'
        },
        {
          label: this.$t('grid.title.personnel'),
          value: 'vStaffName'
        },
        {
          label: '加工任务号',
          value: 'sTaskAllotCode'
        },
        { label: '成品到货单号', value: 'sCode' },
        { label: this.$t('grid.title.status'), value: 'sSheetStatus',
          setValue: (value) => {
            return handleDict(value, this.options['dev.common.sheet.status'])
          }
        },
        { label: '加工厂', value: 'vSupplierName' },
        { label: this.$t('grid.others.warehouse'), value: 'vWarehouseName' },
        { label: this.$t('grid.title.company'), value: 'vCompanyName' },
        { label: '是否二次加工', value: 'sExtend5',
          setValue: (value) => {
            return handleDict(value, this.options['base.yes-no'])
          }
        },
        { label: this.$t('grid.title.createdBy'), value: 'vCreatorName' },
        { label: this.$t('grid.title.createdAt'), value: 'sCreateTime',
          minWidth: 150,
          setValue: (value) => {
            return Moment.time('YYYY-MM-DD HH:mm:ss', value)
          }
        },
        { label: '经营单位', value: 'vManagementName' }
      ],
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      }
    }
  },
  computed: {
    detailParams() {
      const sIds = []
      this.rowData.forEach(item => {
        sIds.push(item.sId)
      })
      return {
        sIds: sIds.toString()
      }
    }
  },
  created() {
    getDictet([
      'dev.common.sheet.status', 'base.yes-no', 'stock.null.type', 'stock.delivery.type'
    ]).then(result => {
      this.options['dev.common.sheet.status'] = result.data[0].dicts
      this.options['base.yes-no'] = result.data[1].dicts
    }).catch(() => {
    })
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    setCount(vCount = 0, vSumAmt = 0, vSumNetAmt = 0, vAmtcount = 0, flag) {
      this[flag] = [
        { count: vCount, key: 'count' },
        { title: this.$t('grid.title.quantity'), count: SteelFormat.formatThousandthSign(vSumAmt, 4), unit: this.$t('grid.others.ton') },
        { title: this.$t('grid.others.numberOfPiecesTag'), count: SteelFormat.formatThousandthSign(vSumNetAmt), unit: this.$t('grid.others.pieces') },
        { title: this.$t('grid.title.amount'), count: SteelFormat.formatPrice(vAmtcount), unit: this.$t('grid.others.yuan') }
      ]
    },
    handleFooterCount(rowData) {
      const details = rowData.filter(item => item._selected)
      this.disRemove = true
      this.$refs.aggrid.getSelectedData(res => {
        if (res.masterData && res.masterData.length > 0) {
          if (res.childData && res.childData.length === 0) {
            this.disRemove = !details.every(val => val.sSheetStatus === '10')
          }
        }
      }, 'delete')
      this.$refs.aggrid.getSelectedData(res => {
        const vCount = res.length
        let sQtxcount = 0
        let sQtycount = 0
        let vAmtcount = 0
        res.forEach(el => {
          sQtxcount += Number(el.sQtx || 0)
          sQtycount += Number(el.sQty || 0)
          vAmtcount += Number(el.sTaxAmt || 0)
        })
        this.setCount(vCount, sQtxcount, sQtycount, vAmtcount, 'footerCount')
      }, 'margeChild')
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        processArrivalPage(
          this.searchInfo,
          pagination
        ).then((res) => {
          this.rowData = res.data.page.content.map((item) => {
            item._selected = false
            item._selectedKeys = []
            item.detailList = []
            return item
          })
          const { vCount, vSumAmt, vSumQtx, vSumQty } = res.data
          this.setCount(vCount, vSumQtx, vSumQty, vSumAmt, 'headerCount')
          resolve(res.data.page)
        }).catch(() => {
          reject(0)
        })
      })
    },
    loadDetail(data) {
      return new Promise((resolve, reject) => {
        processArrivalChildPage(data.sId, {
          sSteelNo: this.searchInfo.sSteelNo
        }).then(res => {
          resolve(res.data)
        }).catch(() => {
          reject([])
        })
      })
    },

    deleteData() {
      this.$refs.aggrid.getSelectedData(selData => {
        this.$confirm(this.$t('grid.tips.isTheDeletionConfirmed'), this.$t('grid.others.prompt'), {
          confirmButtonText: this.$t('btns.confirm'),
          cancelButtonText: this.$t('btns.cancel'),
          type: 'warning'
        })
          .then(() => {
            processArrivalRemoves(selData.masterData.map(i => i.sId)).then(res => {
              if (res.code === '0000') {
                this.$message.success(this.$t('tips.deletedSuccessfully'))
                this.$refs.aggrid.reloadTableData()
              } else {
                this.$message.error(res.message)
              }
            })
          })
          .catch(() => { })
      }, 'delete')
    },
    toDetailPage(data) {
      const { sId, sSheetStatus, sCode } = data
      this.$router.push({
        path: `/FinishedProductArrivalDetail/${sId}`,
        query: {
          Id: sId,
          status: sSheetStatus,
          type: 'edit',
          name: `成品到货单【${sCode}】`,
          activeId: localStorage.getItem('menuId')
        }
      })
    },
    rowDoubleClicked(params) {
      this.toDetailPage(params.data)
    },
    successImport(res) {
      processArrivalGet(res.data.processArrivalIds).then(res => {
        if (res.data.sId) {
          this.toDetailPage(res.data)
        } else {
          this.$message.error(this.$t('grid.others.recordDoesNotExist'))
        }
      })
    }
  }
}

</script>

