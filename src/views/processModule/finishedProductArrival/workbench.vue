<template>
  <div class="page-container">
    <div class="page-title">成品到货工作台</div>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form
        ref="searchForm"
        :form-items="formItems"
        @search="onSearch"
      />
      <div class="btn-group mt-10">
        <div class="text">
          成品到货工作台列表
        </div>
        <div>
          <el-button
            v-has:esc_process_product_arrival_wb_tobecreated
            type="primary"
            size="mini"
            @click="addToCart"
          >
            加入待到货单
          </el-button>
          <el-button
            v-has:esc_process_product_arrival_wb_create
            type="primary"
            size="mini"
            @click="create"
          >
            生成到货单
          </el-button>
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        row-key="sTaskAllotId"
        :column-defs="columnDefs"
        :child-column-defs="childColumnDefs"
        child-row-key="sId"
        children-list-key="details"
        :row-data="rowData"
        :load-data="loadData"
        :load-detail="loadDetail"
        :auto-load-data="false"
        table-selection="multiple"
        :header-total="headerTotal"
        :footer-total="footerTotal"
        full-row-type="parent"
        is-subtable
        :show-child-select="false"
        open-after-filter
        @selectedChange="selectedChange"
        @rowValueChanged="rowValueChangeds"
      />

      <cndShoppingCart
        ref="shoppingCart"
        v-has:esc_process_product_arrival_wb_tobecreated
        title="待生成到货单"
        submit-text="生成到货单"
        row-key="sId"
        button-title="待生成到货单"
        full-row-type="parent"
        :is-subtable="false"
        :columns="shoppingCartColumn"
        :merge-config="shoppingCartMerge"
        :count-list="shoppingCartCountList"
        :cur-config="curConfig"
        :validate-key="[
          'sProjectType',
          'sSupplierId',
          'sCompanyId',
          'sWarehouseId',
          'sManagementId',
        ]"
        :error-dict="{
          'sSupplierId':'加工厂',
        }"
        :row-value-changed="rowValueChangeds"
        @submit="checkCreate"
      />
    </div>
  </div>
</template>

<script>
var Decimal = window.Decimal
// import Vue from 'vue'
// import { Middleware } from 'cndinfo-ui'
import { SteelFormat } from 'cnd-horizon-utils'
import {
  getDictet, getCnDitc
} from '@/api/logistics/saleDelivery/saleorder'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import {
  processArivalWbPage,
  processArivalWbPageDetail,
  processArivalWbCreate
} from '@/api/processModule/finishedProductArrival.js'
import curMixin from '@/utils/curMixin'
import importBtn from '@/components/importBtn'
import cndShoppingCart from '@/components/cndShoppingCart/index'
export default {
  name: 'FinishedProductArrivalWorkbench',
  components: { steelTradeAggrid, importBtn, cndShoppingCart },
  mixins: [curMixin],

  data() {
    return {
      searchInfo: null,
      delDisable: true,
      rowData: [],
      headerTotal: null,
      footerTotal: null,
      options: {
        'esc.process.type': []
      },
      curConfig: {
        vCurQty: 'vConfirmQtx',
        vCurPkgQty: 'vConfirmQty',
        vLeftQty: 'sFinishQtx',
        vLeftPkgQty: 'sFinishQty'
      },
      formItems: [
        {
          label: '加工任务单号',
          value: 'sTaskAllotCode',
          type: 'elInput'
        },
        {
          label: '加工厂',
          value: 'sSupplierId',
          type: 'cndInputDialogItem',
          dialogType: 'customer',
          defaultUrl: '/esc/customer/page',
          option: { valueKey: 'sPath' },
          customerType: ''
        },
        {
          label: this.$t('grid.title.company'),
          value: 'sCompanyId',
          type: 'cndInputDialog',
          dialogType: 'company'
        },
        {
          label: this.$t('grid.title.purchaseContractNumber'),
          value: 'sContractCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.others.itemNumberTag'),
          value: 'sProjectCode',
          type: 'elInput'
        },
        // {
        //   label: this.$t('grid.title.salesContractNumber'),
        //   value: 'sSaleContractCode',
        //   type: 'elInput'
        // },
        {
          label: this.$t('grid.title.personnel'),
          value: 'sStaffId',
          type: 'cndInputDialogItem',
          option: { valueKey: 'sPath' },
          dialogType: 'staff'
        },
        {
          label: this.$t('grid.others.accountingFilter'),
          value: 'vIsCor',
          default: '0',
          type: 'elSelect',
          dict: 'dev.common.verify.finish.type'
        },
        {
          label: '经营单位',
          value: 'sManagementId',
          type: 'cndInputDialog',
          dialogType: 'escOrg'
        },
        {
          label: this.$t('grid.title.accountingGroup'),
          value: 'sCheckGroupId',
          type: 'cndInputDialog',
          dialogType: 'cost'
        },
        {
          label: this.$t('grid.others.steelCoilNumber'),
          value: 'sSteelNo',
          type: 'elInput'
        },
        {
          label: '成品仓库',
          value: 'sWarehouseId',
          type: 'cndInputDialog',
          dialogType: 'warehouse',
          defaultUrl: '/esc/warehouse/config/finished/page-dialog'
        },
        {
          label: '成品品名',
          value: 'sGoodsDetailId',
          type: 'cndInputDialogItem',
          option: { valueKey: 'sName' },
          defaultUrl: '/esc/goods/ext/goodsDesc'
        }
      ],
      columnDefs: [
        {
          headerName: this.$t('grid.others.itemNumberTag'),
          field: 'sProjectCode'
        },
        {
          headerName: this.$t('grid.title.purchaseContractNumber'),
          field: 'sPurContractCode'
        },
        {
          headerName: '加工任务单号',
          field: 'sTaskAllotCode'
        },
        {
          headerName: '加工厂',
          field: 'vSupplierName',
          width: 162
        },
        {
          headerName: '成品仓库',
          field: 'vWarehouseName',
          width: 162
        },
        {
          headerName: this.$t('grid.title.company'),
          field: 'vCompanyName'
        },
        {
          headerName: '工艺',
          field: 'sProcessType',
          valueGetter: (params) => {
            return getCnDitc(params, this.options['esc.process.type'], 'sProcessType')
          }
        },
        {
          headerName: '成品',
          field: 'vArtName',
          width: 106
        },
        {
          headerName: '加工数量/件数',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return `${SteelFormat.formatThousandthSign(params.data.sPurArrivalQtx, 4)}/${params.data.sPurArrivalQty}`
          }
        },
        {
          headerName: '完成数量/件数',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return `${SteelFormat.formatThousandthSign(+params.data.sFinishQtx, 4)}/${+params.data.sFinishQty}`
          }
        },
        // {
        //   headerName: this.$t('grid.others.quantityConfirmedThisTime'),
        //   field: 'vConfirmQtx',
        //   editable: true,
        //   cellEditorFramework: Vue.extend(
        //     Middleware.createComponent(
        //       'CndInputNumber',
        //       {
        //         mark: 'vConfirmQtx',
        //         type: 'number',
        //         decimalDigit: 4,
        //         autoFocus: true,
        //         focusSelect: true
        //       },
        //       {
        //         blur: ({ event, rowData, middleware }) => {
        //           if (+event.target.value > +rowData.data.sPurArrivalQtx) {
        //             this.$message.warning('此次确认数量不能大于加工数量')
        //             middleware.rendered.vConfirmQtx.setValue(+rowData.data.sPurArrivalQtx)
        //           }
        //         }
        //       }
        //     )
        //   ),
        //   cellStyle: { textAlign: 'right' },
        //   valueFormatter: params => {
        //     return SteelFormat.formatThousandthSign(params.value, 4)
        //   }
        // },
        // {
        //   headerName: this.$t('grid.others.theNumberOfConfirmedPieces'),
        //   field: 'vConfirmQty',
        //   editable: true,
        //   cellEditorFramework: Vue.extend(
        //     Middleware.createComponent(
        //       'CndInputNumber',
        //       {
        //         mark: 'vConfirmQty',
        //         type: 'number',
        //         decimalDigit: 0,
        //         focusSelect: true
        //       },
        //       {
        //         blur: ({ event, rowData, middleware }) => {
        //           // this.setCurPkgQty(event, rowData, middleware)
        //         }
        //       }
        //     )
        //   ),
        //   cellStyle: { textAlign: 'right' }
        // },
        {
          headerName: this.$t('grid.title.accountingGroup'),
          field: 'vCheckGroupName'
        },
        {
          headerName: this.$t('grid.title.personnel'),
          field: 'vStaffName'
        },
        {
          headerName: '经营单位',
          field: 'vManaDepartmentName'
        }
      ],
      childColumnDefs: [
        // {
        //   headerName: this.$t('grid.title.salesContractNumber'),
        //   field: 'sSaleContractCode'
        // },
        {
          headerName: '原料品名',
          field: 'vArtName',
          width: 238
        },
        {
          headerName: this.$t('grid.others.steelCoilNumber'),
          field: 'sExtend4'
        },
        {
          headerName: this.$t('grid.others.carriageNumber'),
          field: 'sExtend5'
        },
        {
          headerName: '加工数量/件数',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return `${SteelFormat.formatThousandthSign(params.data.sPurArrivalQtx, 4)}/${params.data.sPurArrivalQty}`
          }
        },
        {
          headerName: '剩余数量/件数',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return `${SteelFormat.formatThousandthSign(params.data.sFinishQtx, 4)}/${params.data.sFinishQty}`
          }
        }
      ],
      shoppingCartColumn: [
        {
          headerName: '加工任务单号',
          field: 'sTaskAllotCode'
        },
        {
          headerName: this.$t('grid.title.purchaseContractNumber'),
          field: 'sPurContractCode'
        },
        // {
        //   headerName: this.$t('grid.title.salesContractNumber'),
        //   field: 'sSaleContractCode'
        // },
        {
          headerName: '成品品名',
          field: 'vArtName'
        },
        // {
        //   headerName: this.$t('grid.others.quantityConfirmedThisTime'),
        //   field: 'vConfirmQtx',
        //   editable: true,
        //   cellEditorFramework: Vue.extend(
        //     Middleware.createComponent(
        //       'CndInputNumber',
        //       {
        //         mark: 'vConfirmQtx',
        //         type: 'number',
        //         decimalDigit: 4,
        //         autoFocus: true,
        //         focusSelect: true
        //       },
        //       {
        //         blur: ({ event, rowData, middleware }) => {
        //           if (+event.target.value > +rowData.data.sPurArrivalQtx) {
        //             this.$message.warning('此次确认数量不能大于加工数量')
        //             middleware.rendered.vConfirmQtx.setValue(+rowData.data.sPurArrivalQtx)
        //           }
        //         }
        //       }
        //     )
        //   ),
        //   cellStyle: { textAlign: 'right' },
        //   valueFormatter: params => {
        //     return SteelFormat.formatThousandthSign(params.value, 4)
        //   }
        // },
        // {
        //   headerName: this.$t('grid.others.theNumberOfConfirmedPieces'),
        //   field: 'vConfirmQty',
        //   editable: true,
        //   cellEditorFramework: Vue.extend(
        //     Middleware.createComponent(
        //       'CndInputNumber',
        //       {
        //         mark: 'vConfirmQty',
        //         type: 'number',
        //         decimalDigit: 0,
        //         focusSelect: true
        //       },
        //       {
        //         blur: ({ event, rowData, middleware }) => {

        //         }
        //       }
        //     )
        //   ),
        //   cellStyle: { textAlign: 'right' }
        // },
        {
          headerName: this.$t('grid.title.company'),
          field: 'vCompanyName'
        },
        {
          headerName: this.$t('grid.title.accountingGroup'),
          field: 'vCheckGroupName'
        },
        {
          headerName: this.$t('grid.title.personnel'),
          field: 'vStaffName'
        }
      ],
      shoppingCartMerge: [{
        field: 'vCurQty',
        mode: 'add',
        max: 'sFinishQtx'
      }, {
        field: 'vCurPkgQty',
        mode: 'add',
        max: 'sFinishQty'
      }],
      shoppingCartCountList: [],
      aggridSelectedData: []
    }
  },
  created() {
    getDictet([
      'esc.process.type'
    ]).then(result => {
      this.options['esc.process.type'] = result.data[0].dicts
    }).catch(() => {
    })
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      delete searchInfo['undefined']
      // const { sGoodsDetailId } = searchInfo
      // if (sGoodsDetailId?.length > 0) {
      //   searchInfo.sGoodsDetailId = sGoodsDetailId[sGoodsDetailId.length - 1]
      // } else {
      //   searchInfo.sGoodsDetailId = undefined
      // }
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    setTotal(vCount = 0, vSumLeftQtx = 0, vSumLeftQty = 0, flag) {
      this[flag] = [
        { count: vCount, key: 'count' },
        { title: this.$t('grid.others.remainingQuantity'), count: SteelFormat.formatThousandthSign(vSumLeftQtx, 4), unit: this.$t('grid.others.ton') },
        { title: this.$t('grid.others.remainingPieces'), count: vSumLeftQty, unit: this.$t('grid.others.pieces') }
      ]
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        processArivalWbPage(
          this.searchInfo,
          pagination
        ).then(res => {
          this.rowData = res.data.page.content.map(item => {
            item._selected = false
            item._selectedKeys = []
            item.details = []
            return item
          })
          // const { vCount, vSumQtx, vSumQty } = res.data
          // this.setTotal(vCount, vSumQtx, vSumQty, 'headerTotal')
          resolve(res.data.page)
        }).catch(() => {
          reject([])
        })
      })
    },
    loadDetail(data) {
      return new Promise((resolve, reject) => {
        const { sPurContractCode, sProjectCode, sOriGoodDetailId, sTaskAllotId, sPurContractId } = data
        processArivalWbPageDetail(
          {
            sContractCode: sPurContractCode,
            sPurContractId: sPurContractId,
            sProjectCode,
            sGoodsDetailId: sOriGoodDetailId,
            sTaskAllotId,
            sSteelNo: this.searchInfo.sSteelNo
          }).then(res => {
          resolve(res.data)
        }).catch(() => {
          reject([])
        })
      })
    },
    toDetailPage(data) {
      const { sId, sSheetStatus, sCode } = data
      this.$router.push({
        path: `/FinishedProductArrivalDetail/${sId}`,
        query: {
          Id: sId,
          status: sSheetStatus,
          type: 'edit',
          name: `成品到货单【${sCode}】`,
          activeId: localStorage.getItem('menuId')
        }
      })
    },
    rowValueChangeds(rowData) {
      const { vConfirmQtx, sPurArrivalQtx } = rowData.data
      if (+vConfirmQtx > +sPurArrivalQtx) {
        this.$message.closeAll()
        this.$message.warning('此次确认数量不能大于加工数量')
      }
    },
    selectedChange(_, selectedData) {
      this.aggridSelectedData = selectedData
      const vCount = selectedData.length
      this.$refs.aggrid.getSelectedData(res => {
        // const vCount = res.length
        let vSumQtx = 0
        let vSumQty = 0
        res.forEach(el => {
          vSumQtx = +new Decimal(vSumQtx).add(+el.sFinishQtx)
          vSumQty = +new Decimal(vSumQty).add(+el.sFinishQty)
        })
        this.footerTotal = [
          { count: vCount, key: 'count' },
          { title: this.$t('grid.others.remainingQuantity'), count: SteelFormat.formatThousandthSign(vSumQtx, 4), unit: this.$t('grid.others.ton') },
          { title: this.$t('grid.others.remainingPieces'), count: vSumQty, unit: this.$t('grid.others.pieces') }
        ]
      }, 'margeChild')
    },
    create() {
      this.$refs.aggrid.gridApi.stopEditing()
      if (!this.aggridSelectedData.length) {
        this.$message.warning(this.$t('grid.others.pleaseSelectData'))
        return false
      }
      this.checkCreate(this.aggridSelectedData)
    },
    checkCreate(list) {
      this.$confirm(this.$t('此操作将生成成品到货单, 是否继续?'), this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'warning'
      }).then(() => {
        processArivalWbCreate(list).then(res => {
          this.$message.success(this.$t('grid.tips.generateSuccess'))
          this.toDetailPage(res.data)
        })
      }).catch(() => { })
    },
    addToCart() {
      this.$refs.aggrid.gridApi.stopEditing()
      if (!this.aggridSelectedData.length) {
        this.$message.warning(this.$t('grid.others.pleaseSelectData'))
        return false
      }
      this.$refs.shoppingCart.addToCart(this.aggridSelectedData)
      this.$refs.aggrid.clearSelection()
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
