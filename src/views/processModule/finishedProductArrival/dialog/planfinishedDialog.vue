
<template>
  <cnd-dialog
    title="新增计划成品数量"
    append-to-body
    width="80%"
    height="520"
    :fullscreen="false"
    :visible.sync="dialogVisible"
    @close="handleClose"
  >
    <template v-if="dialogVisible" slot="content">
      <steel-search-form ref="searchForm" :form-items="formItems" @search="onSearch" />
      <auto-wrap>
        <steelTradeAggrid
          ref="aggrid"
          class="mt-10"
          :column-defs="columnDefs"
          :row-data="rowData"
          :load-data="loadData"
          :auto-load-data="false"
          table-selection="multiple"
          row-key="sId"
          full-row-type="parent"
          @selectedChange="selectedChange"
          @rowValueChanged="rowValueChanged"
        />
      </auto-wrap>
    </template>
    <template slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">{{ $t('btns.cancel') }}</el-button>
      <el-button type="primary" size="mini" @click="handleSelect">{{ $t('btns.confirm') }}</el-button>
    </template>
  </cnd-dialog>
</template>

<script>
// import Vue from 'vue'
// import { Middleware } from 'cndinfo-ui'
import { SteelFormat } from 'cnd-horizon-utils'
// import { Moment } from 'cnd-utils'
import {
  processPlanDialogList,
  processPlanDialogConfirm
} from '@/api/processModule/finishedProductArrival.js'

import {
  getDictet, getCnDitc
} from '@/api/logistics/saleDelivery/saleorder'
import steelTradeAggrid from '@/components/steelTradeAggrid'

export default {
  components: { steelTradeAggrid },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: null
    },
    goodsDetailId: {
      type: String,
      default: null
    },
    info: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      searchInfo: null,
      curConfig: {
        vCurQty: 'vConfirmQtx',
        vCurPkgQty: 'vConfirmQty',
        vLeftQty: 'sFinishQtx',
        vLeftPkgQty: 'sFinishQty'
      },
      formItems: [
        {
          label: '加工任务单号',
          value: 'sTaskAllotCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.title.purchaseContractNumber'),
          value: 'sContractCode',
          type: 'elInput'
        },
        {
          label: '成品品名',
          value: 'sGoodsDetailId',
          type: 'cndInputDialogItem',
          option: { valueKey: 'sName' },
          defaultUrl: '/esc/goods/ext/goodsDesc'
        }
      ],
      columnDefs: [
        {
          headerName: this.$t('grid.others.itemNumberTag'),
          field: 'sProjectCode'
        },
        {
          headerName: this.$t('grid.title.purchaseContractNumber'),
          field: 'sContractCode'
        },
        {
          headerName: '加工任务单号',
          field: 'sTaskAllotCode'
        },
        {
          headerName: '加工厂',
          field: 'vSupplierName',
          width: 162
        },
        {
          headerName: this.$t('grid.title.company'),
          field: 'vCompanyName'
        },
        {
          headerName: '工艺',
          field: 'sProcessType',
          valueGetter: (params) => {
            return getCnDitc(params, this.options['esc.process.type'], 'sProcessType')
          }
        },
        {
          headerName: '成品',
          field: 'sGoodsDetailName'
        },
        {
          headerName: '加工数量/件数',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return `${SteelFormat.formatThousandthSign(+params.data.sPurArrivalQtx, 4)}/${+params.data.sPurArrivalQty}`
          }
        },
        {
          headerName: '完成数量/件数',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return `${SteelFormat.formatThousandthSign(+params.data.sFinishQtx, 4)}/${+params.data.sFinishQty}`
          }
        },
        // {
        //   headerName: this.$t('grid.others.quantityConfirmedThisTime'),
        //   field: 'vConfirmQtx',
        //   editable: true,
        //   cellEditorFramework: Vue.extend(
        //     Middleware.createComponent(
        //       'CndInputNumber',
        //       {
        //         mark: 'vConfirmQtx',
        //         type: 'number',
        //         decimalDigit: 4,
        //         autoFocus: true,
        //         focusSelect: true
        //       },
        //       {
        //         blur: ({ event, rowData, middleware }) => {
        //           if (+event.target.value > +rowData.data.sPurArrivalQtx) {
        //             this.$message.warning('此次确认数量不能大于加工数量')
        //             middleware.rendered.vConfirmQtx.setValue(+rowData.data.sPurArrivalQtx)
        //           }
        //         }
        //       }
        //     )
        //   ),
        //   cellStyle: { textAlign: 'right' },
        //   valueFormatter: params => {
        //     return SteelFormat.formatThousandthSign(params.value, 4)
        //   }
        // },
        // {
        //   headerName: this.$t('grid.others.theNumberOfConfirmedPieces'),
        //   field: 'vConfirmQty',
        //   editable: true,
        //   cellEditorFramework: Vue.extend(
        //     Middleware.createComponent(
        //       'CndInputNumber',
        //       {
        //         mark: 'vConfirmQty',
        //         type: 'number',
        //         decimalDigit: 0,
        //         focusSelect: true
        //       },
        //       {
        //         blur: ({ event, rowData, middleware }) => {
        //         }
        //       }
        //     )
        //   ),
        //   cellStyle: { textAlign: 'right' }
        // },
        {
          headerName: this.$t('grid.title.accountingGroup'),
          field: 'vCheckGroupName'
        },
        {
          headerName: this.$t('grid.title.personnel'),
          field: 'vStaffName'
        },
        {
          headerName: '经营单位',
          field: 'vManaDepartmentName'
        }
      ],
      rowData: [],
      options: {
        'esc.process.type': []
      }
    }
  },
  watch: {
    dialogVisible(val) {
      if (val) {
        this.$nextTick(() => {
          this.onSearch()
        })
      }
    }
  },
  created() {
    getDictet([
      'esc.process.type'
    ]).then(result => {
      this.options['esc.process.type'] = result.data[0].dicts
    }).catch(() => {
    })
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      searchInfo.sSupplierId = this.info.sSupplierId
      searchInfo.sCompanyId = this.info.sCompanyId
      searchInfo.sWarehouseId = this.info.sWarehouseId
      searchInfo.sConNoticePurArrivalId = this.info.sId
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        processPlanDialogList(this.searchInfo).then(res => {
          this.rowData = res.data.map(item => {
            item._selected = false
            item.sUpId = this.id
            return item
          })
          resolve(res.data)
        }).catch(() => {
          reject([])
        })
      })
    },
    handleClose() {
      this.$emit('close')
    },
    rowValueChanged(rowData) {
      const { vConfirmQtx, sPurArrivalQtx } = rowData.data
      if (+vConfirmQtx > +sPurArrivalQtx) {
        this.$message.closeAll()
        this.$message.warning('此次确认数量不能大于加工数量')
      }
    },
    handleSelect() {
      this.$refs.aggrid.gridApi.stopEditing()
      this.$refs.aggrid.getSelectedData(res => {
        if (!res.length) {
          this.$message.warning(this.$t('grid.others.pleaseSelectData'))
          return false
        }
        processPlanDialogConfirm(res).then(res => {
          this.$message.success('操作成功')
          this.$emit('success')
          this.handleClose()
        })
      })
    }
  }
}
</script>
