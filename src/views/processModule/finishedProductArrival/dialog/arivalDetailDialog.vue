<template>
  <cnd-dialog
    v-if="dialogVisible"
    title="新增成品明细"
    append-to-body
    width="650px"
    height="360"
    :fullscreen="false"
    :visible.sync="dialogVisible"
    @close="handleClose"
  >
    <template slot="content">
      <div class="grid-search">
        <span class="grid-search-left">
          加工任务号：{{ info && info.sCode }}
        </span>
        <div class="grid-search-right">
          <el-input
            v-model="searchParam"
            style="width: 220px; margin-right: 10px"
            size="mini"
            clearable
            @input="changeValue"
            @keyup.native.stop.enter="$refs.aggrid.loadTableData()"
            @clear="$refs.aggrid.loadTableData()"
          />
          <el-button
            type="primary"
            size="mini"
            @click="$refs.aggrid.loadTableData()"
          >{{ $t('grid.others.search') }}</el-button>
        </div>
      </div>
      <auto-wrap class="mt-10">
        <steelTradeAggrid
          ref="aggrid"
          :column-defs="columnDefs"
          :row-data="rowData"
          :load-data="loadData"
          table-selection="multiple"
          row-key="sId"
        />
      </auto-wrap>
    </template>
    <template slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">{{
        $t('btns.cancel')
      }}</el-button>
      <el-button type="primary" size="mini" @click="handleSelect">{{
        $t('btns.confirm')
      }}</el-button>
    </template>
  </cnd-dialog>

</template>

<script>
import steelTradeAggrid from '@/components/steelTradeAggrid'

import {
  processArivalDetailArtno,
  processArivalDetailAdd
} from '@/api/processModule/finishedProductArrival'
import { debounce } from '@/utils/common'
export default {
  components: { steelTradeAggrid },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: null
    },
    info: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      searchParam: '',
      columnDefs: [
        {
          headerName: this.$t('grid.others.item'),
          field: 'sName',
          width: 550
        }
      ],
      rowData: []
    }
  },
  methods: {
    changeValue() {
      debounce(() => {
        this.$refs.aggrid && this.$refs.aggrid.loadTableData()
      }, 500)()
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        const searchInfo = {
          likeParam: this.searchParam
        }
        processArivalDetailArtno(
          searchInfo,
          {
            ...pagination,
            id: this.info?.sId
          }
        )
          .then((res) => {
            this.rowData = res.data.content.map((item) => {
              return {
                ...item,
                _selected: false
              }
            })
            resolve(res.data)
          })
          .catch(() => {
            reject([])
          })
      })
    },

    handleSelect() {
      const list = []
      this.rowData.map((item) => {
        if (item._selected === true) {
          list.push(item)
        }
      })
      if (list.length > 0) {
        processArivalDetailAdd({
          processArrivalId: this.id,
          noticePlanId: this.info.sId,
          artnoIds: list.map(e => e.sId)
        }).then((res) => {
          this.$message.success(this.$t('tips.addedSuccessfully'))
          this.$emit('success')
          this.handleClose()
        })
      } else {
        this.$message.error(this.$t('grid.others.pleaseSelectData'))
      }
    },
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
.grid-search {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  &-left {
    color: #000;
  }
  &-right {
    display: flex;
  }
}
</style>
