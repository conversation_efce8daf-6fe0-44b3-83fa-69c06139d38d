
<template>
  <cnd-dialog
    title="新增原料明细"
    append-to-body
    width="80%"
    height="520"
    :fullscreen="false"
    :visible.sync="dialogVisible"
    @close="handleClose"
  >
    <template v-if="dialogVisible" slot="content">
      <steel-search-form ref="searchForm" :form-items="formItems" @search="onSearch" />
      <auto-wrap>
        <steelTradeAggrid
          ref="aggrid"
          class="mt-10"
          :column-defs="columnDefs"
          :row-data="rowData"
          :load-data="loadData"
          :auto-load-data="false"
          :paginationinif="false"
          full-row-type="parent"
          table-selection="multiple"
          row-key="sId"
          @selectedChange="selectedChange"
          @rowValueChanged="rowValueChanged"
        />
      </auto-wrap>
    </template>
    <template slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">{{ $t('btns.cancel') }}</el-button>
      <el-button type="primary" size="mini" @click="handleSelect">{{ $t('btns.confirm') }}</el-button>
    </template>
  </cnd-dialog>
</template>

<script>
import Vue from 'vue'
import { Middleware } from 'cndinfo-ui'
import { SteelFormat } from 'cnd-horizon-utils'
import {
  processArivalPickMaterial,
  processArivalPickMaterialAdd
} from '@/api/processModule/finishedProductArrival'

import steelTradeAggrid from '@/components/steelTradeAggrid'

export default {
  components: { steelTradeAggrid },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: null
    },
    info: {
      type: Object,
      default: () => {}
    },
    arivalData: {
      type: Object,
      default: () => {}
    },
    planData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      searchInfo: null,
      formItems: [
        // {
        //   label: '加工任务单号',
        //   value: 'sTakAllotCode',
        //   type: 'elInput'
        // },
        {
          label: this.$t('grid.title.purchaseContractNumber'),
          value: 'sPurContractCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.others.steelCoilNumber'),
          value: 'sSteelNo',
          type: 'elInput'
        }

      ],
      columnDefs: [
        // {
        //   headerName: '加工任务单号',
        //   field: 'sCode'
        // },
        {
          headerName: this.$t('grid.title.purchaseContractNumber'),
          field: 'sPurContractCode'
        },
        {
          headerName: this.$t('grid.title.salesContractNumber'),
          field: 'sSaleContractCode'
        },
        {
          headerName: '加工厂',
          field: 'vSupplierName',
          width: 162
        },
        {
          headerName: '成品品名',
          field: 'vArtName'
        },
        {
          headerName: '原材料品名',
          field: 'vMaterialArtName'
        },
        {
          headerName: this.$t('grid.others.remainingQuantityNumberOfPieces'),
          field: 'sLeftQtx',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            const data = params.data
            return `${SteelFormat.formatThousandthSign(data.sLeftQtx, 4)}/${SteelFormat.formatThousandthSign(data.sLeftQty)}`
          }
        },
        {
          headerName: this.$t('grid.title.quantity'),
          field: 'sQtx',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          },
          editable: true,
          cellEditorFramework: Vue.extend(
            Middleware.createComponent(
              'CndInputNumber',
              {
                mark: 'sQtx',
                type: 'number',
                decimalDigit: 4,
                autoFocus: true,
                focusSelect: true
              },
              {
                blur: ({ event, rowData, middleware }) => {
                  const sQty = middleware.rendered.sQty.unformatValue
                  const sQtx = event.target.value
                  const data = {
                    sQty,
                    sQtx,
                    sLeftQty: rowData.data.sLeftQty,
                    sLeftQtx: rowData.data.sLeftQtx
                  }
                  this.setQtx(data, 'blur', middleware)
                }
              }
            )
          )
        },
        {
          headerName: this.$t('grid.others.numberOfPiecesTag'),
          field: 'sQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value)
          },
          editable: true,
          cellEditorFramework: Vue.extend(
            Middleware.createComponent(
              'CndInputNumber',
              {
                mark: 'sQty',
                type: 'number',
                decimalDigit: 0,
                focusSelect: true
              },
              {}
            )
          )
        },
        {
          headerName: this.$t('grid.others.carriageNumber'),
          field: 'sVesselNo'
        },
        {
          headerName: this.$t('grid.others.steelCoilNumber'),
          field: 'sSteelNo'
        },
        {
          headerName: this.$t('grid.title.company'),
          field: 'vCompanyName'
        },
        {
          headerName: this.$t('grid.title.accountingGroup'),
          field: 'vCheckGroupName'
        },
        {
          headerName: this.$t('grid.title.personnel'),
          field: 'vStaffName'
        }
      ],
      rowData: []
    }
  },
  watch: {
    dialogVisible(val) {
      if (val) {
        this.$nextTick(() => {
          this.onSearch()
        })
      }
    }
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    loadData() {
      return new Promise((resolve, reject) => {
        processArivalPickMaterial(
          this.searchInfo,
          {
            sProcessTaskId: this.id,
            noticePlanId: this.planData.sId
          }).then(res => {
          this.rowData = res.data.map(item => {
            item._selected = false
            return item
          })
          resolve(res.data)
        }).catch(() => {
          reject([])
        })
      })
    },
    setQtx(data, type, middleware) {
      const { sQty, sQtx, sLeftQty, sLeftQtx } = data
      const handleCurQtyMsg = () => {
        if (+sQty < +sLeftQty) {
          this.$message.closeAll()
          this.$message({ message: this.$t('grid.others.ifTheRemainingQuanteUsedUpKey'), type: 'warning' })
        }
      }
      if (+sQtx > +sLeftQtx) {
        if (type === 'change') {
          this.$message.warning('数量不能超过剩余数量')
          data.sQtx = data.sLeftQtx
          this.$refs.aggrid.refreshTable()
        }
        if (middleware) {
          middleware.rendered.sQtx.setValue(+sLeftQtx)
        }
        handleCurQtyMsg()
      } else if (+sQtx === +sLeftQtx) {
        handleCurQtyMsg()
      }
    },
    rowValueChanged(params) {
      this.setQtx(params.data, 'change')
    },
    handleClose() {
      this.$emit('close')
    },
    handleSelect() {
      this.$refs.aggrid.gridApi.stopEditing()
      this.$refs.aggrid.getSelectedData(res => {
        if (!res.length) {
          this.$message.warning(this.$t('grid.others.pleaseSelectData'))
          return false
        }
        processArivalPickMaterialAdd(
          res,
          {
            sProcessArrivalDetailId: this.arivalData.sId
          }).then(res => {
          this.$message.success('操作成功')
          this.$emit('success')
          this.handleClose()
        })
      })
    }
  }
}
</script>
