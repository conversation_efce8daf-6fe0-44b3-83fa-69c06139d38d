<template>
  <cnd-dialog
    res-if="visible"
    :title="title"
    :visible="visible"
    :show-close="true"
    :fullscreen="false"
    :append-to-body="appendToBody"
    width="600px"
    height="400px"
    @close="handleClose"
  >
    <template slot="content">
      <div>
        <el-input
          v-model="steelNo"
          style="width:100%;margin-bottom:10px"
          placeholder="请输入钢卷号搜索"
          size="mini"
          clearable
          prefix-icon="el-icon-search"
          @input="changeValue"
          @keyup.native.stop.enter="$refs.aggrid.loadTableData()"
          @clear="$refs.aggrid.loadTableData()"
        />
      </div>
      <steelTradeAggrid
        ref="aggrid"
        key="id"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        table-selection="multiple"
        @selectedChange="handleFooterCount"
      />
    </template>
    <template slot="footer">
      <el-button
        type="primary"
        size="mini"
        @click="downloadAll"
      >批量下载</el-button>
    </template>
  </cnd-dialog>
</template>

<script>
// 导入图标库
import 'cnd-icon'
import { Moment } from 'cnd-utils'
import { postList, downFilePost } from '@/api/contract'
import { convertRes2Blob, debounce } from '@/utils/common'
import steelTradeAggrid from '@/components/steelTradeAggrid'
export default {
  components: { steelTradeAggrid },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    appendToBody: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: '下载质保书'
    }
  },
  data() {
    return {
      rowData: [],
      selectedData: [],
      steelNo: '',
      columnDefs: [
        {
          field: 'fileName',
          headerName: '质保书',
          width: 206
        },
        {
          headerName: '生成日期',
          field: 'createTime',
          cellStyle: { textAlign: 'right' },
          minWidth: 150,
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.createTime)
          }
        },
        {
          headerName: this.$t('grid.others.operation'),
          width: 100,
          cellStyle: { textAlign: 'center' },
          onCellClicked: (params) => {
            this.downloadFile([params.data.id], params.data.fileName)
          },
          cellRenderer: (params) => {
            const sHtml = '<span style=color:#66B1FF;cursor:pointer>' + '下载' + '</span>'
            return sHtml
          }
        }
      ]
    }
  },
  computed: {
  },
  watch: {
    visible(nv, ov) {
      if (nv) {
        this.onSearch()
      } else {
        this.rowData = []
        this.selectedData = []
      }
    }
  },
  methods: {
    changeValue() {
      debounce(() => {
        this.$refs.aggrid && this.$refs.aggrid.loadTableData()
      }, 500)()
    },
    onSearch() {
      this.$nextTick(() => {
        this.$refs.aggrid.loadTableData()
      })
    },
    downloadAll() {
      if (!this.selectedData.length) {
        return this.$message.error('请选择要下载的文件')
      }
      this.downloadFile(this.selectedData.map(e => e.id))
    },
    downloadFile(ids, fileName = '质保书') {
      downFilePost(ids, '/esc/steel/factory/logistics/list/batchDownload').then(res => {
        if (res.type === 'application/json') {
          const fileReader = new FileReader()
          fileReader.readAsText(res, 'utf-8')
          fileReader.onload = () => {
            const result = JSON.parse(fileReader.result)
            if (!result.data && result.message === 'ok') {
              this.$message.error('暂无文件')
            } else {
              this.$message.error(result.message)
            }
          }
        } else {
          const fileType = ids.length > 1 ? 'application/zip' : undefined
          convertRes2Blob(res, fileName, fileType)
          this.$nextTick(() => {
            this.$message.success(this.$t('tips.downloadSuccessful'))
          })
        }
      })
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        postList({
          steelNo: this.steelNo,
          serviceType: '1'
        }, this.formatUrl(pagination, '/esc/steel/factory/logistics/list/getFilePage/{page}/{limit}')).then(res => {
          this.rowData = res.data.content.map((item) => {
            item._selected = false
            return item
          })
          resolve(res.data.totalElements)
        }).catch(() => {
          reject(0)
        })
      })
    },
    handleFooterCount() {
      this.$refs.aggrid.getSelectedData(res => {
        this.selectedData = res
      })
    },
    // 关闭/取消
    handleClose(value = []) {
      this.$emit('onSelect', value)
    },
    // 格式化获取url
    formatUrl(params = {}, url) {
      let urlStr = ''
      if (url.indexOf('{') > -1) {
        const urlArr = url.split('/')
        urlArr.forEach((item, index) => {
          if (index !== 0) {
            let temp
            if (item.indexOf('{') > -1) {
              temp = `/${params[item.substring(1, item.length - 1)]}`
            } else {
              temp = `/${item}`
            }
            urlStr += temp
          }
        })
      } else {
        urlStr = url
      }
      return urlStr
    }
  }
}
</script>
<style lang="scss">
</style>
