<template>
  <div class="page-container">
    <p class="page-title">钢厂发运信息</p>
    <div class="layout-content auto-page-title">
      <steel-search-form ref="searchForm" :form-items="formItems" @search="onSearch" @searchValue="onSearchValue" />
      <div class="btn-group mt-10">
        <div class="text">
          钢厂发运信息{{ $t('grid.others.list') }}
        </div>
        <div>
          <el-button
            v-has:esc_steel_factory_logistics_list_batchDownload
            type="primary"
            size="mini"
            @click="visible = true"
          >下载质保书</el-button>
          <export-btn
            v-has:esc_steel_factory_logistics_export
            class="ml-10"
            file-name="钢厂发运信息明细"
            api-url="/esc/steel/factory/logistics/export"
            :post-params="searchInfo"
            :export-config="exportConfig"
            :get-grid-api="getGridApi"
            :isload-detail="true"
            btn-txt="导出信息明细"
            :detail-params="detailParams"
          />
          <export-btn
            v-has:esc_steel_factory_logistics_information_export
            class="ml-10"
            file-name="钢厂发运物流明细"
            api-url="/esc/steel/factory/logistics/information/export"
            :post-params="searchInfo"
            :export-config="exportConfig"
            :get-grid-api="getGridApi"
            :isload-detail="true"
            btn-txt="导出物流明细"
            :detail-params="detailParams"
          />
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        table-selection="multiple"
        :heightinif="200"
        @rowClicked="rowClicked"
        @selectedChange="handleFooterCount"
        @rowDoubleClicked="onRowDoubleClicked"
      />
      <el-tabs
        v-model="activeName"
        style="height:auto"
        class="mt-10"
      >
        <el-tab-pane
          label="发运信息明细"
          name="shipmentCommodity"
        >
          <steelTradeAggrid
            v-if="activeName ==='shipmentCommodity'"
            ref="detailAggrid"
            :auto-height="true"
            :load-data="loadDetail"
            :column-defs="childColumnDefs"
            :row-data="rowDataChild"
            :header-total="headerCount"
            :footer-total="footerCount"
            table-selection="multiple"
            @selectedChange="handleChildFooterCount"
          />
        </el-tab-pane>
        <el-tab-pane
          label="发运物流明细"
          name="shippingLogistics"
        >
          <steelTradeAggrid
            v-if="activeName ==='shippingLogistics'"
            ref="detailAggrid"
            :auto-height="true"
            :load-data="loadDetail"
            :column-defs="childColumnDefsV2"
            :row-data="rowDataChild"
            :header-total="headerCount"
            :footer-total="footerCount"
            table-selection="multiple"
            @selectedChange="handleChildFooterCount"
          />
        </el-tab-pane>
      </el-tabs>
    </div>
    <AnnexDialog
      :visible="visible"
      append-to-body
      @onSelect="visible = false"
    />
  </div>
</template>

<script>
var Decimal = window.Decimal
import { Moment } from 'cnd-utils'
import { SteelFormat } from 'cnd-horizon-utils'
// import { handleDict } from '@/utils/common'
import { getCnDitc } from '@/utils/dict'

import {
  getDictet
} from '@/api/logistics/saleDelivery/saleorder'
import {
  factLogisticPage,
  factLogisticPageChild,
  informationPage
} from '@/api/steelInfoManage/shippingInfo.js'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import AnnexDialog from './AnnexDialog'
import exportBtn from '@/components/exportBtnV2'
import mixins from '../mixins'
export default {
  name: 'ShippingInfo',
  components: { steelTradeAggrid, exportBtn, AnnexDialog },
  mixins: [mixins],
  data() {
    const childrenColumns = [
      {
        headerName: '钢厂销售合同号',
        field: 'sFactoryScontCode'
      },
      {
        headerName: '车船号',
        field: 'sVehicleNo'
      },
      {
        headerName: '起运地(码头)',
        field: 'sLoadName'
      },
      {
        headerName: '目的地(码头)',
        field: 'sDestName'
      },
      {
        headerName: '品名',
        field: 'sProductName'
      },
      {
        headerName: '规格',
        field: 'sSpec'
      },
      {
        headerName: '厚度',
        field: 'sHigh'
      },
      {
        headerName: '宽度',
        field: 'sWidth'
      },
      {
        headerName: '长度',
        field: 'sLength'
      },
      {
        headerName: '发货数量',
        field: 'sContractQty',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatThousandthSign(params.value, 4)
        }
      },
      {
        headerName: '发货件数',
        field: 'sQty',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatThousandthSign(params.value)
        }
      },
      {
        headerName: '钢卷号',
        field: 'sMetalNo'
      },
      {
        headerName: '收货地',
        field: 'sConsignAddr'
      },
      {
        headerName: '收货单位',
        field: 'sConsignCorp'
      },
      // {
      //   headerName: '物流状态',
      //   field: 'sLogisticsStatus',
      //   valueGetter: (params) => {
      //     return getCnDitc(params, this.options['dev.common.sheet.status'], 'sLogisticsStatus')
      //   }
      // },
      {
        headerName: '发运时间',
        field: 'sDispatchTime',
        cellStyle: { textAlign: 'right' },
        minWidth: 150,
        valueFormatter(params) {
          return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sDispatchTime)
        }
      },
      {
        headerName: this.$t('grid.title.createdAt'),
        field: 'sCreateTime',
        cellStyle: { textAlign: 'right' },
        minWidth: 150,
        valueFormatter(params) {
          return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
        }
      }
    ]
    const statusDict = [
      {
        'sCodeValue': '70',
        'sCodeName': '执行'
      },
      {
        'sCodeValue': '75',
        'sCodeName': '完结'
      },
      {
        'sCodeValue': '90',
        'sCodeName': '作废'

      }
    ]
    return {
      visible: false,
      activeName: 'shipmentCommodity',
      disRemove: true,
      options: {
        'dev.common.sheet.status': []
      },
      headerCount: null,
      footerCount: null,
      searchInfo: null,
      formItems: [
        {
          label: this.$t('grid.title.purchaseContractNumber'),
          value: 'sPurContractCode',
          type: 'elInput'
        },
        {
          label: '系列',
          value: 'sUpSourceType',
          type: 'elSelect',
          required: true,
          allHide: true,
          dict: 'steel.factory.field.config'
        },
        {
          label: this.$t('grid.others.supplier'),
          value: 'sSupplierId',
          type: 'cndInputDialogItem',
          defaultUrl: '/customer/series/pageSteel',
          otherOptions: {
            sUpSourceType: ''
          },
          option: { valueKey: 'sPath' }
        },
        // {
        //   label: this.$t('grid.others.customer'),
        //   value: 'sCustomerId',
        //   type: 'cndInputDialogItem',
        //   dialogType: 'customer',
        //   defaultUrl: '/esc/customer/page',
        //   option: { valueKey: 'sPath' }
        // },
        {
          label: this.$t('grid.others.itemNumberTag'),
          value: 'sProjectCode',
          type: 'elInput'
        },
        {
          label: '钢厂' + this.$t('grid.title.salesContractNumber'),
          value: 'sFactoryScontCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.title.accountingGroup'),
          value: 'sCheckGroupId',
          type: 'cndInputDialog',
          dialogType: 'cost',
          placeholder: this.$t('grid.others.pleaseSelectAccountingGroup')
        },
        // {
        //   label: this.$t('grid.others.department'),
        //   value: 'sDepartmentId',
        //   type: 'cndInputDialog',
        //   dialogType: 'depart',
        //   placeholder: this.$t('grid.others.pleaseSelectTheDepartment')
        // },
        {
          label: this.$t('grid.title.personnel'),
          value: 'sStaffId',
          type: 'cndInputDialogItem',
          option: { valueKey: 'sPath' },
          dialogType: 'staff',
          placeholder: this.$t('grid.others.pleaseSelectPersonnel')
        },
        {
          label: this.$t('grid.title.status'),
          value: 'sSheetStatus',
          type: 'elSelect',
          dict: statusDict,
          itemType: 'occultation',
          placeholder: this.$t('grid.others.pleaseSelectStatus')
        },
        {
          label: this.$t('grid.title.company'),
          value: 'sCompanyId',
          type: 'cndInputDialog',
          dialogType: 'company',
          placeholder: this.$t('grid.others.pleaseSelectCompany')
        },
        {
          label: this.$t('grid.title.createdAt'),
          value: ['sCreateTime', 'sCreateTimeTo'],
          placeholder: [this.$t('grid.others.pleaseSelectTheDate'), this.$t('grid.others.pleaseSelectTheDate')],
          unlinkPanels: true,
          type: 'elDatePicker'
        },
        {
          label: '经营单位',
          value: 'sManagementId',
          type: 'cndInputDialog',
          dialogType: 'escOrg',
          itemType: 'occultation'
        },
        {
          label: this.$t('grid.title.createdBy'),
          value: 'sCreator',
          type: 'cndInputDialog',
          placeholder: this.$t('grid.others.pleaseSelectTheCreator'),
          dialogType: 'applicant'
        }

      ],
      columnDefs: [
        {
          field: 'sPurContractCode',
          headerName: this.$t('grid.title.purchaseContractNumber')
        },
        {
          headerName: this.$t('grid.others.supplier'),
          field: 'sSupplierName'
        },
        // {
        //   headerName: this.$t('grid.others.customer'),
        //   field: 'sCustomerName'
        // },
        {
          headerName: this.$t('grid.others.item'),
          field: 'sGoodsDesc'
        },
        {
          headerName: this.$t('grid.others.itemNumberTag'),
          field: 'sProjectCode'
        },
        {
          headerName: this.$t('grid.title.company'),
          field: 'vCompanyName'
        },
        {
          headerName: this.$t('grid.title.accountingGroup'),
          field: 'vCheckGroupName'
        },
        {
          headerName: this.$t('grid.title.personnel'),
          field: 'vStaffName'
        },
        {
          headerName: this.$t('grid.title.status'),
          field: 'sSheetStatus',
          valueGetter: (params) => {
            return getCnDitc(params, this.options['dev.common.sheet.status'], 'sSheetStatus')
          }
        },
        {
          headerName: this.$t('grid.title.createdBy'),
          field: 'sCreatorName'
        },
        {
          field: 'vManagementName',
          headerName: '经营单位'
        },
        {
          field: 'sRemark',
          headerName: this.$t('grid.title.remarks')
        }
      ],
      childColumnDefs: childrenColumns,
      childrenColumns,
      childColumnDefsV2: [],
      rowData: [],
      selectId: null,
      rowDataChild: [],
      exportConfig: [
        { label: this.$t('grid.title.purchaseContractNumber'), value: 'sPurContractCode' },
        { label: '钢厂销售合同号', value: 'sFactoryScontCode' },
        { label: '车船号', value: 'sVehicleNo' },
        { label: '起运地(码头)', value: 'sLoadName' },
        { label: '目的地(码头)', value: 'sDestName' },
        { label: '品名', value: 'sProductName' },
        { label: '规格', value: 'sSpec' },
        { label: '厚度', value: 'sHigh' },
        { label: '宽度', value: 'sWidth' },
        { label: '长度', value: 'sLength' },
        { label: '发货数量', value: 'sContractQty',
          setValue: (value) => { return Number((+value).toFixed(4)) }
        },
        { label: '发货件数', value: 'sQty' },
        { label: '钢卷号', value: 'sMetalNo' },
        { label: '收货地', value: 'sConsignAddr' },
        // { label: '物流状态', value: 'sLogisticsStatus' },
        { label: '发运时间', value: 'sDispatchTime',
          setValue: (value) => {
            return Moment.time('YYYY-MM-DD HH:mm:ss', value)
          }
        }
        // { label: this.$t('grid.title.createdAt'), value: 'sCreateTime',
        //   setValue: (value) => {
        //     return Moment.time('YYYY-MM-DD HH:mm:ss', value)
        //   }
        // },
        // { label: this.$t('grid.title.purchaseContractNumber'), value: 'sPurContractCode' },
        // { label: this.$t('grid.others.supplier'), value: 'sSupplierName' },
        // { label: this.$t('grid.others.customer'), value: 'sCustomerName' },
        // { label: this.$t('grid.others.item'), value: 'sGoodsDesc' },
        // { label: this.$t('grid.others.itemNumberTag'), value: 'sProjectCode' },
        // { label: this.$t('grid.title.company'), value: 'vCompanyName' },
        // { label: this.$t('grid.title.accountingGroup'), value: 'vCheckGroupName' },
        // { label: this.$t('grid.title.personnel'), value: 'vStaffName' },
        // { label: this.$t('grid.title.status'), value: 'sSheetStatus',
        //   setValue: value => {
        //     return handleDict(value, this.options['dev.common.sheet.status'])
        //   }
        // },
        // { label: this.$t('grid.title.createdBy'), value: 'sCreatorName' },
        // { label: '经营单位', value: 'vManagementName' },
        // { label: this.$t('grid.title.remarks'), value: 'sRemark' }
      ],
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      activeObj: {
        'shipmentCommodity': {
          code: '03',
          column: childrenColumns,
          api: factLogisticPageChild
        },
        'shippingLogistics': {
          code: '05',
          column: [],
          api: informationPage
        }
      }
    }
  },
  computed: {
    detailParams() {
      const sIds = []
      const selectIds = []
      this.rowData.forEach(item => {
        sIds.push(item.sId)
        if (item && item._selected) {
          selectIds.push(item.sId)
        }
      })
      return {
        sIds: sIds.toString(),
        selectIds: selectIds.toString()
      }
    }
  },
  watch: {
    activeName(val) {
      this.$nextTick(() => {
        this.getChildHeader()
      })
    }
  },
  beforeCreate() {
    getDictet([
      'dev.common.sheet.status'
    ]).then(result => {
      this.options['dev.common.sheet.status'] = result.data[0].dicts.map(item => {
        if (item.sCodeValue === '70') {
          item.sCodeName = '执行'
        }
        return item
      })
    }).catch(() => {
    })
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      const { sUpSourceType } = searchInfo
      if (load && sUpSourceType) {
        this.selectId = null
        this.$refs.aggrid.loadTableData()
        this.getChildHeader()
      } else {
        this.selectId = null
        this.rowData = []
        this.rowDataChild = []
        this.headerCount = null
        this.footerCount = null
      }
    },
    getChildHeader() {
      const headerConfig = {
        sUpSourceType: this.searchInfo.sUpSourceType,
        sMenuCode: this.activeObj[this.activeName]['code'],
        aggridRefs: this.$refs.detailAggrid,
        defaultColumns: this.activeObj[this.activeName]['column']
      }
      this.getHeader(headerConfig)
    },
    // 点击 表格
    onRowDoubleClicked(params) {
    },
    setCount(vCount = 0, vSumContractQty = 0, vSumsQty = 0, flag) {
      this[flag] = [
        { count: vCount, key: 'count' },
        { title: '发货数量', count: SteelFormat.formatThousandthSign(vSumContractQty, 4), unit: this.$t('grid.others.ton') },
        { title: '发货件数', count: SteelFormat.formatThousandthSign(vSumsQty), unit: this.$t('grid.others.pieces') }

      ]
    },
    handleChildFooterCount(rowData) {
      this.$refs.detailAggrid.getSelectedData(res => {
        const vCount = res.length
        let sContractQty = 0; let sQty = 0
        res.forEach(el => {
          sContractQty = +new Decimal(sContractQty).add(+el.sContractQty)
          sQty = +new Decimal(sQty).add(+el.sQty)
        })
        this.setCount(vCount, sContractQty, sQty, 'footerCount')
      })
    },
    rowClicked({ data }) {
      if (!data) {
        this.selectId = null
        this.$refs.detailAggrid.loadTableData()
      } else if (data && this.selectId !== data.sId) {
        this.selectId = data.sId
        this.$refs.detailAggrid.loadTableData()
      }
    },
    // 主表
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        factLogisticPage(this.$refs.searchForm.getSearchData(), {
          ...pagination
        }).then(res => {
          this.rowData = res.data.content.map((item, index) => {
            if (this.selectId) {
              item._selected = this.selectId === item?.sId
            } else {
              item._selected = index === 0
            }
            item._selectedKeys = []
            return item
          })
          resolve(res.data.totalElements)
          this.rowClicked({ data: res.data.content[0] })
        }).catch(() => {
          reject(0)
        })
      })
    },
    loadDetail(pagination) {
      return new Promise((resolve, reject) => {
        this.activeObj[this.activeName]['api'](this.selectId, pagination,
          {
            sUpSourceType: this.searchInfo.sUpSourceType,
            sMenuCode: this.activeObj[this.activeName]['code']
          }).then(res => {
          this.rowDataChild = res.data.page.content.map(item => {
            item._selected = false
            item._selectedKeys = []
            return item
          })
          const { vCount, vSumContractQty, vSumsQty } = res.data
          this.setCount(vCount, vSumContractQty, vSumsQty, 'headerCount')
          resolve(res.data.page)
        }).catch(() => {
          reject([])
        })
      })
    }
  }
}
</script>
