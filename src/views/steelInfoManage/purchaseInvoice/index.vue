<template>
  <div class="page-container">
    <p class="page-title">钢厂采购发票</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form ref="searchForm" :form-items="formItems" @search="onSearch" @searchValue="onSearchValue" />
      <div class="btn-group mt-10">
        <div class="text">
          钢厂采购发票{{ $t('grid.others.list') }}
        </div>
        <div>
          <export-btn
            v-has:esc_steel_factory_invoice_pur_export
            file-name="钢厂采购发票"
            api-url="/esc/steel/factory/invoice/pur/export"
            :post-params="searchInfo"
            :export-config="exportConfig"
            :get-grid-api="getGridApi"
            row-key="sId"
            :isload-detail="true"
            :detail-params="detailParams"
          />
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        table-selection="multiple"
        @rowClicked="rowClicked"
        @selectedChange="handleFooterCount"
        @rowDoubleClicked="onRowDoubleClicked"
      />
      <div class="btn-group mt-10">
        <div class="text">
          钢厂采购发票明细
        </div>
      </div>
      <steelTradeAggrid
        ref="detailAggrid"
        :load-data="loadDetail"
        :column-defs="childColumnDefs"
        :row-data="rowDataChild"
        :header-total="headerCount"
        :footer-total="footerCount"
        :auto-load-data="false"
        table-selection="multiple"
        @selectedChange="handleChildFooterCount"
      />
    </div>
  </div>
</template>

<script>
var Decimal = window.Decimal
import { Moment } from 'cnd-utils'
import { SteelFormat } from 'cnd-horizon-utils'

import { handleDict } from '@/utils/common'
import {
  getDictet,
  getCnDitc
} from '@/api/logistics/saleDelivery/saleorder'
import {
  factInvoicePage,
  factInvoicePageChild
} from '@/api/steelInfoManage/purchaseInvoice.js'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import exportBtn from '@/components/exportBtnV2'
import mixins from '../mixins'
export default {
  name: 'PurchaseInvoice',
  components: { steelTradeAggrid, exportBtn },
  mixins: [mixins],
  data() {
    const childrenColumns = [
      {
        headerName: '建发采购合同号',
        field: 'sPurContractCode'
      },
      {
        headerName: '钢厂销售合同号',
        field: 'sFactoryScontCode'
      },
      // {
      //   headerName: '湘钢销售订单号',
      //   field: 'sSaleContLineNo'
      // },
      {
        headerName: '品名',
        field: 'vGoodsDesc'
      },
      {
        headerName: '牌号',
        field: 'sShopNo'
      },
      {
        headerName: '重量',
        field: 'sWeight',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatThousandthSign(params.value, 4)
        }
      },
      {
        field: 'sTaxPrice',
        headerName: '单价',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatPrice(params.value)
        }
      },
      {
        field: 'sTaxAmt',
        headerName: '开票金额',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatPrice(params.value)
        }
      },
      {
        headerName: '车船号',
        field: 'sVehicleNo'
      }
    ]
    return {
      disRemove: true,
      options: {
        'invoice.subtype': []
      },
      headerCount: null,
      footerCount: null,
      searchInfo: null,
      formItems: [
        {
          label: this.$t('grid.others.customer'),
          value: 'sCustomerId',
          type: 'cndInputDialogItem',
          dialogType: 'customer',
          defaultUrl: '/esc/customer/page',
          option: { valueKey: 'sPath' }
        },
        {
          label: '系列',
          value: 'sUpSourceType',
          type: 'elSelect',
          required: true,
          allHide: true,
          dict: 'steel.factory.field.config'
        },
        {
          label: this.$t('grid.others.supplier'),
          value: 'sSupplierId',
          type: 'cndInputDialogItem',
          defaultUrl: '/customer/series/pageSteel',
          otherOptions: {
            sUpSourceType: ''
          },
          option: { valueKey: 'sPath' }
        },
        {
          label: '发票代码',
          value: 'sInvCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.title.purchaseContractNumber'),
          value: 'sPurContractCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.others.itemNumberTag'),
          value: 'sProjectCode',
          type: 'elInput'
        },
        {
          label: '发票类型',
          value: 'sInvSubClass',
          type: 'elSelect',
          dict: 'invoice.subtype',
          itemType: 'occultation',
          placeholder: this.$t('grid.others.pleaseSelectStatus')
        },
        {
          label: '销售发票号',
          value: 'sInvoiceSaleCode',
          type: 'elInput'
        },
        {
          label: '经营单位',
          value: 'sManagementId',
          type: 'cndInputDialog',
          dialogType: 'escOrg',
          itemType: 'occultation'
        },
        // {
        //   label: this.$t('grid.title.company'),
        //   value: 'sCompanyId',
        //   type: 'cndInputDialog',
        //   dialogType: 'company',
        //   placeholder: this.$t('grid.others.pleaseSelectCompany')
        // },
        {
          label: this.$t('grid.title.createdAt'),
          value: ['sCreateTime', 'vCreateTimeTo'],
          placeholder: [this.$t('grid.others.pleaseSelectTheDate'), this.$t('grid.others.pleaseSelectTheDate')],
          unlinkPanels: true,
          type: 'elDatePicker'
        }
        // {
        //   label: this.$t('grid.title.createdBy'),
        //   value: 'sCreator',
        //   type: 'cndInputDialog',
        //   placeholder: this.$t('grid.others.pleaseSelectTheCreator'),
        //   dialogType: 'applicant'
        // }

      ],
      columnDefs: [
        {
          field: 'sInvCompany',
          headerName: '开票单位'
        },
        {
          headerName: '发票类型',
          field: 'sInvSubClass',
          valueGetter: (params) => {
            return getCnDitc(params, this.options['invoice.subtype'], 'sInvSubClass')
          }
        },
        {
          headerName: '发票代码',
          field: 'sInvCode'
        },
        {
          headerName: '销售发票号',
          field: 'sInvoiceSaleCode'
        },
        {
          headerName: '开票日期',
          field: 'sInvDate',
          cellStyle: { textAlign: 'right' },
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD', params.data.sInvDate)
          }
        },
        {
          headerName: '销售发票说明',
          field: 'sInvDesc'
        },
        {
          headerName: '税率',
          field: 'sVatRate',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return +params.value + '%'
          }
        },
        {
          field: 'sTaxAmt',
          headerName: '发票价税合计',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          field: 'sNetAmt',
          headerName: '发票不含税金额',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          field: 'sVatAmt',
          headerName: '发票税额',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: this.$t('grid.title.company'),
          field: 'vCompanyName'
        },
        {
          headerName: this.$t('grid.title.createdAt'),
          field: 'sCreateTime',
          cellStyle: { textAlign: 'right' },
          minWidth: 150,
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
          }
        }
        // {
        //   field: 'vManagementName',
        //   headerName: '经营单位'
        // },
      ],
      childColumnDefs: childrenColumns,
      childrenColumns,
      rowData: [],
      rowDataChild: [],
      selectId: '',
      dialogVisibleadd: false,
      selectedId: null,
      exportConfig: [
        { label: '发票代码', value: 'sInvCode' },
        { label: '销售发票号', value: 'sInvoiceSaleCode' },
        { label: '开票日期', value: 'sInvDate',
          setValue: (value) => {
            return Moment.time('YYYY-MM-DD', value)
          }
        },
        { label: '发票类型', value: 'sInvSubClass',
          setValue: value => {
            return handleDict(value, this.options['invoice.subtype'])
          }
        },
        { label: '建发采购合同号', value: 'sPurContractCode' },
        { label: '钢厂销售合同号', value: 'sFactoryScontCode' },
        // { label: '湘钢销售订单号', value: 'sSaleContLineNo' },
        { label: '品名', value: 'vGoodsDesc' },
        { label: '牌号', value: 'sShopNo' },
        { label: '重量', value: 'sWeight' },
        { label: '单价', value: 'sTaxPrice',
          setValue: value => { return +(+value).toFixed(2) }
        },
        { label: '开票金额', value: 'sTaxAmt',
          setValue: value => { return +(+value).toFixed(2) }
        },
        { label: '车船号', value: 'sVehicleNo' }

        // { label: this.$t('grid.title.createdAt'), value: 'sCreateTime',
        //   setValue: (value) => {
        //     return Moment.time('YYYY-MM-DD HH:mm:ss', value)
        //   }
        // },
        // { label: this.$t('grid.title.purchaseContractNumber'), value: 'sPurContractCode' },
        // { label: this.$t('grid.others.supplier'), value: 'sSupplierName' },
        // { label: this.$t('grid.others.customer'), value: 'sCustomerName' },
        // { label: this.$t('grid.others.item'), value: 'sGoodsDesc' },
        // { label: this.$t('grid.others.itemNumberTag'), value: 'sProjectCode' },
        // { label: this.$t('grid.title.company'), value: 'vCompanyName' },
        // { label: this.$t('grid.title.accountingGroup'), value: 'vCheckGroupName' },
        // { label: this.$t('grid.title.personnel'), value: 'vStaffName' },
        // { label: this.$t('grid.title.status'), value: 'sSheetStatus',
        //   setValue: value => {
        //     return handleDict(value, this.options['invoice.subtype'])
        //   }
        // },
        // { label: this.$t('grid.title.createdBy'), value: 'sCreatorName' },
        // { label: '经营单位', value: 'vManagementName' },
        // { label: this.$t('grid.title.remarks'), value: 'sRemark' }
      ],
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      }
    }
  },
  computed: {
    detailParams() {
      const sIds = []
      const selectIds = []
      this.rowData.forEach(item => {
        sIds.push(item.sId)
        if (item && item._selected) {
          selectIds.push(item.sId)
        }
      })
      return {
        sIds: sIds.toString(),
        selectIds: selectIds.toString()
      }
    }
  },
  beforeCreate() {
    getDictet([
      'invoice.subtype'
    ]).then(result => {
      this.options['invoice.subtype'] = result.data[0].dicts.map(item => {
        if (item.sCodeValue === '70') {
          item.sCodeName = '执行'
        }
        return item
      })
    }).catch(() => {
    })
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      const { sUpSourceType } = searchInfo
      if (load && sUpSourceType) {
        this.selectId = null
        this.$refs.aggrid.loadTableData()
        const headerConfig = {
          sUpSourceType: this.searchInfo.sUpSourceType,
          sMenuCode: '04',
          aggridRefs: this.$refs.detailAggrid,
          defaultColumns: this.childrenColumns
        }
        this.getHeader(headerConfig)
      } else {
        this.selectId = null
        this.rowData = []
        this.rowDataChild = []
        this.headerCount = null
        this.footerCount = null
      }
    },
    setCount(vCount = 0, vSumQty = 0, vSumTaxAmt = 0, flag) {
      this[flag] = [
        { count: vCount, key: 'count' },
        { title: '开票数量', count: SteelFormat.formatThousandthSign(vSumQty, 4), unit: this.$t('grid.others.ton') },
        { title: '含税金额', count: SteelFormat.formatPrice(vSumTaxAmt), unit: this.$t('grid.others.yuan') }
      ]
    },
    // 点击 表格
    onRowDoubleClicked(params) {
    },
    rowClicked({ data }) {
      if (!data) {
        this.selectId = null
        this.$refs.detailAggrid.loadTableData()
      } else if (data && this.selectId !== data.sId) {
        this.selectId = data.sId
        this.$refs.detailAggrid.loadTableData()
      }
    },
    handleFooterCount() {
      // this.$refs.aggrid.getSelectedData(res => {
      // })
    },
    handleChildFooterCount(rowData) {
      this.$refs.detailAggrid.getSelectedData(res => {
        const vCount = res.length
        let sWeight = 0; let sTaxAmt = 0
        res.forEach(el => {
          sWeight = +new Decimal(sWeight).add(+el.sWeight)
          sTaxAmt = +new Decimal(sTaxAmt).add(+el.sTaxAmt)
        })
        this.setCount(vCount, sWeight, sTaxAmt, 'footerCount')
      })
    },
    // 主表
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        factInvoicePage(this.$refs.searchForm.getSearchData(), {
          ...pagination
        }).then(res => {
          this.rowData = res.data.page.content.map((item, index) => {
            if (this.selectId) {
              item._selected = this.selectId === item?.sId
            } else {
              item._selected = index === 0
            }
            item._selectedKeys = []
            return item
          })
          resolve(res.data.page.totalElements)
          this.rowClicked({ data: res.data.page.content[0] })
        }).catch(() => {
          reject(0)
        })
      })
    },
    loadDetail(pagination) {
      return new Promise((resolve, reject) => {
        factInvoicePageChild(this.selectId, pagination).then(res => {
          this.rowDataChild = res.data.page.content.map(item => {
            item._selected = false
            item._selectedKeys = []
            return item
          })
          const { vCount, vSumQty, vSumTaxAmt } = res.data
          this.setCount(vCount, vSumQty, vSumTaxAmt, 'headerCount')
          resolve(res.data.page)
        }).catch(() => {
          reject(0)
        })
      })
    }
  }
}
</script>
