
<template>
  <cnd-dialog
    title="新增钢厂订货明细"
    append-to-body
    width="80%"
    height="620"
    :fullscreen="false"
    :visible.sync="dialogVisible"
    @close="handleClose"
  >
    <template v-if="dialogVisible" slot="content">
      <steel-search-form ref="searchForm" :form-items="formItems" @search="onSearch" />
      <auto-wrap class="mt-10">
        <steelTradeAggrid
          ref="aggrid"
          :column-defs="columnDefs"
          :child-column-defs="childColumnDefs"
          :row-data="rowData"
          :load-data="loadData"
          :load-detail="loadDetail"
          table-selection="multiple"
          row-key="sId"
          child-row-key="_Id"
          children-list-key="details"
          :header-total="headerTotal"
          :footer-total="footerTotal"
          is-subtable
          full-row-type="child"
          @selectedChange="handleFooterCount"
          @childRowValueChanged="rowValueChanged"
        />
      </auto-wrap>
    </template>
    <template slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">{{ $t('btns.cancel') }}</el-button>
      <el-button type="primary" size="mini" @click="handleSelect">{{ $t('btns.confirm') }}</el-button>
    </template>
  </cnd-dialog>
</template>

<script>
var Decimal = window.Decimal
// import Vue from 'vue'
// import { Middleware } from 'cndinfo-ui'
import { SteelFormat } from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
import {
  factContractWbPage,
  factContractWbPageSum,
  factContractWbChild,
  factContractDetailAdd
} from '@/api/steelInfoManage/steelContract.js'
import steelTradeAggrid from '@/components/steelTradeAggrid'
export default {
  components: { steelTradeAggrid },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: null
    },
    info: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      searchInfo: null,
      formItems: [
        {
          label: '钢厂销售合同号',
          value: 'sFactoryScontCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.title.createdAt'),
          value: ['startTime', 'sEndTime'],
          placeholder: [this.$t('grid.others.startDate'), this.$t('grid.others.endDate')],
          unlinkPanels: true,
          type: 'elDatePicker'
        },
        {
          label: '主表id',
          value: 'sRelId',
          type: 'elInput',
          default: this.info.sId,
          disabled: true,
          hidden: true
        },
        {
          label: this.$t('grid.others.accountingFilter'),
          value: 'isWriteOff',
          type: 'elSelect',
          dict: 'dev.common.verify.finish.type',
          itemType: 'occultation',
          default: '0',
          hidden: true
        }
      ],
      columnDefs: [
        {
          headerName: '钢厂销售合同号',
          field: 'sFactoryScontCode'
        },
        {
          headerName: '建发采购合同号',
          field: 'sPurContractCode'
        },
        {
          headerName: this.$t('grid.others.supplier'), // 供应商
          field: 'sSupplierName'
        },
        {
          headerName: this.$t('grid.title.company'),
          field: 'sCompanyName'
        },
        {
          headerName: this.$t('grid.title.createdAt'),
          field: 'sExtend10',
          cellStyle: { textAlign: 'right' },
          minWidth: 150,
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sExtend10)
          }
        }
      ],
      childColumnDefs: [
        {
          headerName: '钢厂品类',
          field: 'sFactoryGoods'
        },
        {
          headerName: '品名',
          field: 'sArtName'
        },
        {
          headerName: '订货重量(吨)',
          field: 'sContractQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: '订货件数',
          field: 'sQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value)
          }
        },
        // {
        //   headerName: this.$t('grid.others.quantityConfirmedThisTime'),
        //   field: 'confirmQtx',
        //   editable: true,
        //   cellEditorFramework: Vue.extend(
        //     Middleware.createComponent(
        //       'CndInputNumber',
        //       {
        //         mark: 'confirmQtx',
        //         type: 'number',
        //         decimalDigit: 4,
        //         autoFocus: true,
        //         focusSelect: true
        //       },
        //       {
        //         blur: ({ event, rowData, middleware }) => {
        //           // _this.setCurQty(event, rowData, middleware)
        //         }
        //       }
        //     )
        //   ),
        //   cellStyle: { textAlign: 'right' },
        //   valueFormatter: params => {
        //     return SteelFormat.formatThousandthSign(params.value, 4)
        //   }
        // },
        // {
        //   headerName: this.$t('grid.others.theNumberOfConfirmedPieces'),
        //   field: 'confirmQty',
        //   editable: true,
        //   cellEditorFramework: Vue.extend(
        //     Middleware.createComponent(
        //       'CndInputNumber',
        //       {
        //         mark: 'confirmQty',
        //         type: 'number',
        //         decimalDigit: 0,
        //         focusSelect: true
        //       },
        //       {
        //         blur: ({ event, rowData, middleware }) => {
        //           // _this.setCurPkgQty(event, rowData, middleware)
        //         }
        //       }
        //     )
        //   ),
        //   cellStyle: { textAlign: 'right' }
        // },
        // {
        //   headerName: '剩余数量/件数',
        //   cellStyle: { textAlign: 'right' },
        //   valueFormatter: params => {
        //     return `${SteelFormat.formatThousandthSign(params.data.sLeftQtx, 4)}/${SteelFormat.formatThousandthSign(params.data.sLeftQty)}`
        //   }
        // },
        {
          headerName: '件重',
          field: 'sWeight',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: '规格',
          field: 'sSpec'
        },
        {
          headerName: '厚度',
          field: 'sHigh'
        },
        {
          headerName: '宽度',
          field: 'sWidth'
        },
        {
          headerName: '长度',
          field: 'sLength'
        },
        {
          field: 'sTaxPrice',
          headerName: this.$t('grid.title.unitPriceWithTax'),
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: '分段号',
          field: 'sSubsection'
        },
        {
          headerName: '切边形态',
          field: 'sSideShape'
        },
        {
          headerName: '探伤要求',
          field: 'sInspection'
        },
        {
          headerName: '交期时间',
          field: 'sDeliveryDate',
          cellStyle: { textAlign: 'right' },
          minWidth: 150,
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sDeliveryDate)
          }
        },
        {
          headerName: '交货允差上限',
          field: 'sDelvUpLimit',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.toPercent(params.value)
          }
        },
        {
          headerName: '交货允差下限',
          field: 'sDelvDownLimit',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.toPercent(params.value)
          }
        },
        {
          headerName: '备注',
          field: 'sRemark'
        }
      ],
      rowData: [],
      headerTotal: null,
      footerTotal: null
    }
  },
  watch: {
    dialogVisible(val) {
      if (val) {
        this.$nextTick(() => {
          this.onSearch()
        })
      }
    }
  },
  methods: {
    onSearch() {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      this.$refs.aggrid.loadTableData()
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        factContractWbPage(this.$refs.searchForm.getSearchData(), pagination).then(res => {
          this.rowData = res.data.content.map(item => {
            item._selected = false
            item._selectedKeys = []
            item.details = []
            return item
          })
          factContractWbPageSum(this.$refs.searchForm.getSearchData(), pagination).then(v => {
            const { dataCount, sumsQtx, sumsQty } = v.data
            this.setTotal(dataCount, sumsQtx, sumsQty, 'headerTotal')
          })
          resolve(res.data.content)
        }).catch(() => {
          reject([])
        })
      })
    },
    loadDetail(params) {
      return new Promise((resolve, reject) => {
        factContractWbChild(params.sId, this.searchInfo).then(res => {
          const data = res.data.map(item => {
            item._Id = params.sId
            item.confirmQtx = item.sLeftQtx
            item.confirmQty = item.sLeftQty
            return item
          })
          resolve(data)
        }).catch(() => {
          reject([])
        })
      })
    },
    setTotal(vCount = 0, sumsQtx = 0, sumsQty = 0, flag) {
      this[flag] = [
        { count: vCount, key: 'count' },
        { title: '订货件数', count: SteelFormat.formatThousandthSign(sumsQty), unit: this.$t('grid.others.pieces') },
        { title: '订货重量', count: SteelFormat.formatThousandthSign(sumsQtx, 4), unit: this.$t('grid.others.ton') }
      ]
    },
    handleFooterCount(rowData) {
      this.$refs.aggrid.getSelectedData(res => {
        const vCount = res.length
        let sContractQty = new Decimal(0)
        let sQty = new Decimal(0)
        res.forEach(el => {
          sContractQty = sContractQty.add(el.sContractQty)
          sQty = sQty.add(el.sQty)
        })
        this.setTotal(vCount, +sContractQty, +sQty, 'footerTotal')
      }, 'margeChild')
    },
    rowValueChanged(params) {
      console.log('params: ', params)
      const { data, rowIndex } = params
      this.rowData[data.parentId].details[rowIndex] = data
    },
    handleClose() {
      this.$emit('close')
    },
    handleSelect() {
      // this.$refs.aggrid.stopChildEditing()
      this.$refs.aggrid.getSelectedData(selectedList => {
        if (!selectedList.length) {
          this.$message.warning(this.$t('grid.others.pleaseSelectAData'))
          return false
        }
        factContractDetailAdd(this.id, selectedList).then(res => {
          this.$message.success('操作成功')
          this.handleClose()
        })
      }, 'margeChild')
    }
  }
}
</script>
