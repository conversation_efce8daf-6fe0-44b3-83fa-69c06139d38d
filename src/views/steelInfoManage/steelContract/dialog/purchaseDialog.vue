
<template>
  <cnd-dialog
    title="匹配采购合同"
    append-to-body
    width="80%"
    height="620"
    :fullscreen="false"
    :visible.sync="dialogVisible"
    @close="handleClose"
  >
    <template v-if="dialogVisible" slot="content">
      <steel-search-form ref="searchForm" :form-items="formItems" @search="onSearch" />
      <auto-wrap class="mt-10">
        <steelTradeAggrid
          ref="aggrid"
          :column-defs="columnDefs"
          :row-data="rowData"
          :load-data="loadData"
          table-selection="single"
          row-key="sId"
          @selectedChange="handleFooterCount"
        />
      </auto-wrap>
    </template>
    <template slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">{{ $t('btns.cancel') }}</el-button>
      <el-button type="primary" size="mini" @click="handleSelect">{{ $t('btns.confirm') }}</el-button>
    </template>
  </cnd-dialog>
</template>

<script>
import { SteelFormat } from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
import {
  factContractPurDialog,
  factContractMatch
} from '@/api/steelInfoManage/steelContract.js'
import steelTradeAggrid from '@/components/steelTradeAggrid'
export default {
  components: { steelTradeAggrid },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: null
    },
    info: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      searchInfo: null,
      formItems: [
        {
          label: this.$t('grid.title.purchaseContractNumber'),
          value: 'sCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.others.itemNumberTag'),
          value: 'sProjectCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.others.customer'),
          value: 'sCustomerId',
          type: 'cndInputDialogItem',
          dialogType: 'customer',
          defaultUrl: '/esc/customer/page',
          option: { valueKey: 'sPath' }
          // default: this.info.sCustomerId,
          // disabled: true,
          // hidden: true
        },
        {
          label: '主表id',
          value: 'sRelId',
          type: 'elInput',
          default: this.info.sId,
          disabled: true,
          hidden: true
        },
        // {
        //   label: this.$t('grid.title.company'),
        //   value: 'sCompanyId',
        //   type: 'cndInputDialog',
        //   dialogType: 'company',
        //   default: this.info.sCompanyId,
        //   disabled: true,
        //   hidden: true,
        //   placeholder: this.$t('grid.others.pleaseSelectCompany')
        // },
        // {
        //   label: this.$t('grid.others.department'),
        //   value: 'sDepartmentId',
        //   type: 'cndInputDialog',
        //   dialogType: 'depart',
        //   default: this.info.sDepartmentId,
        //   disabled: true,
        //   hidden: true,
        //   placeholder: this.$t('grid.others.pleaseSelectTheDepartment')
        // },
        {
          label: this.$t('grid.title.createdAt'),
          value: ['sCreateTime', 'vCreateTimeTo'],
          placeholder: [this.$t('grid.others.pleaseSelectTheDate'), this.$t('grid.others.pleaseSelectTheDate')],
          unlinkPanels: true,
          type: 'elDatePicker'
        },
        {
          label: '执行时间',
          value: ['sRatifyDate', 'vRatifyDateTo'],
          placeholder: [this.$t('grid.others.startDate'), this.$t('grid.others.endDate')],
          unlinkPanels: true,
          type: 'elDatePicker',
          itemType: 'occultation'
        }
      ],
      columnDefs: [
        {
          headerName: this.$t('grid.title.purchaseContractNumber'),
          field: 'sCode'
        },
        {
          headerName: this.$t('grid.others.customer'),
          field: 'vCustomerName'
        },
        {
          headerName: this.$t('grid.others.item'),
          field: 'vGoodsDesc'
        },
        {
          headerName: this.$t('grid.others.supplier'),
          field: 'vSupplierName'
        },
        {
          headerName: this.$t('grid.title.company'),
          field: 'vCompanyName'
        },
        {
          headerName: '合同数量',
          field: 'vSumQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        // {
        //   headerName: '剩余数量',
        //   field: 'sContractQty',
        //   cellStyle: { textAlign: 'right' },
        //   valueFormatter: params => {
        //     return SteelFormat.formatThousandthSign(params.value, 4)
        //   }
        // },
        {
          headerName: '金额',
          field: 'sOriginalAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: this.$t('grid.others.itemNumberTag'),
          field: 'sProjectCode'
        },
        {
          headerName: this.$t('grid.title.createdAt'),
          field: 'sCreateTime',
          cellStyle: { textAlign: 'right' },
          minWidth: 150,
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
          }
        },
        {
          headerName: '执行时间',
          field: 'sCreateTime',
          cellStyle: { textAlign: 'right' },
          minWidth: 150,
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
          }
        }
      ],
      rowData: []
    }
  },
  watch: {
    dialogVisible(val) {
      if (val) {
        this.$nextTick(() => {
          this.onSearch()
        })
      }
    }
  },
  methods: {
    onSearch() {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      this.$refs.aggrid.loadTableData()
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        factContractPurDialog(this.$refs.searchForm.getSearchData(), pagination).then(res => {
          this.rowData = res.data.contractList.content.map(item => {
            item._selected = false
            return item
          })
          resolve(res.data.contractList)
        }).catch(() => {
          reject([])
        })
      })
    },
    handleFooterCount(rowData) {
      this.$refs.aggrid.getSelectedData(res => {
        console.log('handleFooterCount: ', res)
      })
    },
    rowValueChanged(params) {
      console.log('params: ', params)
    },
    handleClose() {
      this.$emit('close')
    },
    handleSelect() {
      this.$refs.aggrid.stopChildEditing()
      this.$refs.aggrid.getSelectedData(selectedList => {
        if (!selectedList) {
          this.$message.warning(this.$t('grid.others.pleaseSelectAData'))
          return false
        }
        factContractMatch({
          sId: this.info.sId,
          sPurContractId: selectedList.sId
        }).then(res => {
          this.$message.success('操作成功')
          this.handleClose()
        })
      })
    }
  }
}
</script>
