<template>
  <div class="page-container">
    <p class="page-title">钢厂合同管理</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form ref="searchForm" :form-items="formItems" @search="onSearch" @searchValue="onSearchValue" />
      <div class="btn-group mt-10">
        <div class="text">
          钢厂合同{{ $t('grid.others.list') }}
        </div>
        <div>
          <export-btn
            file-name="钢厂合同管理"
            api-url="/esc/steel/factory/contract/export"
            :post-params="searchInfo"
            :export-config="exportConfig"
            :get-grid-api="getGridApi"
            :isload-detail="true"
            :detail-params="detailParams"
          />
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        :show-header-select="false"
        table-selection="multiple"
        @rowClicked="rowClicked"
        @selectedChange="handleFooterCount"
        @rowDoubleClicked="onRowDoubleClicked"
      />
      <div class="btn-group mt-10">
        <div class="text">
          钢厂合同明细
        </div>
      </div>
      <steelTradeAggrid
        ref="detailAggrid"
        :load-data="loadDetail"
        :column-defs="childColumnDefs"
        :row-data="rowDataChild"
        :header-total="headerCount"
        :footer-total="footerCount"
        :auto-load-data="false"
        table-selection="multiple"
        @selectedChange="handleChildFooterCount"
      />
    </div>
  </div>
</template>

<script>
var Decimal = window.Decimal
import { SteelFormat } from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
// import { handleDict } from '@/utils/common'
import {
  getDictet,
  getCnDitc
} from '@/api/logistics/saleDelivery/saleorder'
import {
  factoryContractPage,
  // factoryContractPageSum,
  factoryContractChild,
  factContractbasics
} from '@/api/steelInfoManage/steelContract.js'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import exportBtn from '@/components/exportBtnV2'
import mixins from '../mixins'

export default {
  name: 'SteelContractManage',
  components: { steelTradeAggrid, exportBtn },
  mixins: [mixins],
  data() {
    const childrenColumns = [
      {
        headerName: '钢厂销售合同号',
        field: 'sFactoryScontCode'
      },
      {
        headerName: '钢厂品类',
        field: 'sFactoryGoods'
      },
      {
        headerName: '品名',
        field: 'sArtName'
      },
      {
        headerName: '订货重量(吨)',
        field: 'sContractQty',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatThousandthSign(params.value, 4)
        }
      },
      {
        headerName: '订货件数',
        field: 'sQty',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatThousandthSign(params.value)
        }
      },
      {
        headerName: '件重',
        field: 'sWeight',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatThousandthSign(params.value, 4)
        }
      },
      {
        headerName: '规格',
        field: 'sSpec'
      },
      {
        headerName: '厚度',
        field: 'sHigh'
      },
      {
        headerName: '宽度',
        field: 'sWidth'
      },
      {
        headerName: '长度',
        field: 'sLength'
      },
      {
        field: 'sTaxPrice',
        headerName: this.$t('grid.title.unitPriceWithTax'),
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatPrice(params.value)
        }
      },
      {
        headerName: '分段号',
        field: 'sSubsection'
      },
      {
        headerName: '切边形态',
        field: 'sSideShape'
      },
      {
        headerName: '探伤要求',
        field: 'sInspection'
      },
      {
        headerName: '交期时间',
        field: 'sDeliveryDate',
        cellStyle: { textAlign: 'right' },
        minWidth: 150,
        valueFormatter(params) {
          return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sDeliveryDate)
        }
      },
      {
        headerName: '交货允差上限',
        field: 'sDelvUpLimit',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return +params.value + '%'
        }
      },
      {
        headerName: '交货允差下限',
        field: 'sDelvDownLimit',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          // return SteelFormat.toPercent(params.value)
          return +params.value + '%'
        }
      },
      {
        headerName: '备注',
        field: 'sRemark'
      }
    ]
    const statusDict = [
      {
        'sCodeValue': '10',
        'sCodeName': '准备',
        'sSort': 11,
        'sFilter': null,
        'sIsEnabled': '1',
        'sLanguage': 'zh_CN'
      }, {
        'sCodeValue': '70',
        'sCodeName': '执行',
        'sSort': 44,
        'sFilter': null,
        'sIsEnabled': '1',
        'sLanguage': 'zh_CN'
      },
      {
        'sCodeValue': '90',
        'sCodeName': '作废',
        'sSort': 90,
        'sFilter': null,
        'sIsEnabled': '1',
        'sLanguage': 'zh_CN'

      }
    ]
    return {
      disRemove: true,
      options: {
        'dev.common.sheet.status': []
      },
      headerCount: null,
      footerCount: null,
      searchInfo: null,
      formItems: [
        {
          label: this.$t('grid.title.salesContractNumber'),
          value: 'sSaleContractCode',
          type: 'elInput'
        },
        // {
        //   label: this.$t('grid.others.supplier'),
        //   value: 'sSupplierId',
        //   type: 'cndInputDialogItem',
        //   dialogType: 'customer',
        //   defaultUrl: '/esc/customer/page',
        //   option: { valueKey: 'sPath' }
        // },
        {
          label: '系列',
          value: 'sUpSourceType',
          type: 'elSelect',
          required: true,
          allHide: true,
          dict: 'steel.factory.field.config'
        },
        {
          label: this.$t('grid.others.supplier'),
          value: 'sSupplierId',
          type: 'cndInputDialogItem',
          defaultUrl: '/customer/series/pageSteel',
          otherOptions: {
            sUpSourceType: ''
          },
          option: { valueKey: 'sPath' }
        },
        // {
        //   label: this.$t('grid.others.itemNumberTag'),
        //   value: 'sProjectCode',
        //   type: 'elInput'
        // },
        {
          label: this.$t('grid.title.purchaseContractNumber'),
          value: 'sPurContractCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.title.accountingGroup'),
          value: 'sCheckGroupId',
          type: 'cndInputDialog',
          dialogType: 'cost',
          placeholder: this.$t('grid.others.pleaseSelectAccountingGroup')
        },
        {
          label: this.$t('grid.others.department'),
          value: 'sDepartmentId',
          type: 'cndInputDialog',
          dialogType: 'depart',
          placeholder: this.$t('grid.others.pleaseSelectTheDepartment')
        },
        {
          label: this.$t('grid.title.personnel'),
          value: 'sStaffId',
          type: 'cndInputDialogItem',
          option: { valueKey: 'sPath' },
          dialogType: 'staff',
          placeholder: this.$t('grid.others.pleaseSelectPersonnel')
        },
        {
          label: this.$t('grid.title.status'),
          value: 'sSheetStatus',
          type: 'elSelect',
          dict: statusDict,
          itemType: 'occultation',
          placeholder: this.$t('grid.others.pleaseSelectStatus')
        },
        {
          label: this.$t('grid.title.createdAt'),
          value: ['sCreateStartTime', 'sCreateEndTime'],
          placeholder: [this.$t('grid.others.pleaseSelectTheDate'), this.$t('grid.others.pleaseSelectTheDate')],
          unlinkPanels: true,
          type: 'elDatePicker'
        },
        {
          label: '执行时间',
          value: ['sDrStartTime', 'sDrEndTime'],
          placeholder: [this.$t('grid.others.startDate'), this.$t('grid.others.endDate')],
          default: ['', ''],
          unlinkPanels: true,
          type: 'elDatePicker',
          itemType: 'occultation'
        },
        {
          label: this.$t('grid.title.createdBy'),
          value: 'sCreator',
          type: 'cndInputDialog',
          placeholder: this.$t('grid.others.pleaseSelectTheCreator'),
          dialogType: 'applicant'
        },
        {
          label: '经营单位',
          value: 'sManagementId',
          type: 'cndInputDialog',
          dialogType: 'escOrg',
          itemType: 'occultation'
        },
        {
          label: '钢厂销售合同号',
          value: 'sFactoryScontCode',
          type: 'elInput'
        }
      ],
      columnDefs: [
        {
          headerName: '匹配单号',
          field: 'sCode'
        },
        {
          field: 'sPurContractCode',
          headerName: this.$t('grid.title.purchaseContractNumber')
        },
        // {
        //   field: 'sSaleContractCode',
        //   headerName: this.$t('grid.title.salesContractNumber')
        // },
        {
          headerName: this.$t('grid.title.status'),
          field: 'sSheetStatus',
          valueGetter: (params) => {
            return getCnDitc(params, this.options['dev.common.sheet.status'], 'sSheetStatus')
          }
        },
        {
          headerName: this.$t('grid.others.supplier'),
          field: 'sSupplierName'
        },
        // {
        //   headerName: this.$t('grid.others.customer'),
        //   field: 'sCustomerName'
        // },
        {
          headerName: this.$t('grid.others.item'),
          field: 'sGoodsDetailName'
        },
        {
          headerName: '数量',
          field: 'sContractQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: '件数',
          field: 'sQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value)
          }
        },
        {
          headerName: this.$t('grid.title.accountingGroup'),
          field: 'sCheckGroupName'
        },
        {
          headerName: this.$t('grid.title.personnel'),
          field: 'sStaffName'
        },
        {
          headerName: this.$t('grid.title.createdBy'),
          field: 'sCreatorName'
        },
        {
          field: 'sManagementName',
          headerName: '经营单位'
        },
        {
          headerName: this.$t('grid.title.createdAt'),
          field: 'sCreateTime',
          cellStyle: { textAlign: 'right' },
          minWidth: 150,
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
          }
        },
        {
          headerName: '执行时间',
          field: 'sRatifyDate',
          cellStyle: { textAlign: 'right' },
          minWidth: 150,
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sRatifyDate)
          }
        }
      ],
      childColumnDefs: childrenColumns,
      childrenColumns,
      rowData: [],
      selectId: null,
      rowDataChild: [],
      dialogVisibleadd: false,
      // exportConfig: [
      //   { label: this.$t('grid.others.itemNumberTag'), value: 'sProjectCode' },
      //   { label: '采购合同号', value: 'sSaleContractCode' },
      //   { label: this.$t('grid.others.item'), value: 'vArtName' },
      //   { label: this.$t('grid.others.steelCoilNumber'), value: 'sExtend4' },
      //   { label: this.$t('grid.others.carriageNumber'), value: 'sExtend5' },
      //   { label: '转售数量', value: 'sQtx',
      //     setValue: (value) => {
      //       return Number((+value).toFixed(4))
      //     }
      //   },
      //   { label: '转售件数', value: 'sQty',
      //     setValue: (value) => {
      //       return Number((+value))
      //     }
      //   },
      //   { label: '转售金额', value: 'sTaxAmt',
      //     setValue: value => { return Number((+value).toFixed(2)) }
      //   },
      //   { label: this.$t('grid.title.accountingGroup'), value: 'vCheckGroupName' },
      //   { label: this.$t('grid.others.department'), value: 'vDepartmentName' },
      //   { label: this.$t('grid.title.personnel'), value: 'vStaffName' },
      //   { label: '原客户', value: 'vCustomerName' },
      //   { label: '原销售合同号', value: 'sSaleContractCode' },
      //   { label: '转售单号', value: 'sCode' },
      //   { label: this.$t('grid.title.status'), value: 'sSheetStatus',
      //     setValue: (value) => {
      //       return handleDict(value, this.options['dev.common.sheet.status'])
      //     }
      //   },
      //   { label: this.$t('grid.others.warehouse'), value: 'vWarehouseName' },
      //   { label: this.$t('grid.others.supplier'), value: 'vSupplierName' },
      //   { label: this.$t('grid.title.company'), value: 'vCompanyName' },
      //   // { label: '转售数量', value: 'vSumContractQty',
      //   //   setValue: (value) => {
      //   //     return Number((+value).toFixed(4))
      //   //   }
      //   // },
      //   // { label: '转售件数', value: 'vSumQty' },
      //   // { label: '转售金额', value: 'vSumAmt',
      //   //   setValue: value => { return Number((+value).toFixed(2)) }
      //   // },
      //   {
      //     label: this.$t('grid.title.createdBy'),
      //     value: 'vCreatorName'
      //   },
      //   { label: this.$t('grid.title.createdAt'), value: 'sCreateTime',
      //     minWidth: 150,
      //     setValue: (value) => {
      //       return Moment.time('YYYY-MM-DD HH:mm:ss', value)
      //     }
      //   },
      //   {
      //     label: '经营单位',
      //     value: 'vManagementName'
      //   }
      // ],
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      }
    }
  },
  computed: {
    detailParams() {
      const sIds = []
      const selectIds = []
      this.rowData.forEach(item => {
        sIds.push(item.sId)
        if (item && item._selected) {
          selectIds.push(item.sId)
        }
      })
      return {
        sIds: sIds.toString(),
        selectIds: selectIds.toString()
      }
    }
  },
  beforeCreate() {
    getDictet([
      'dev.common.sheet.status'
    ]).then(result => {
      this.options['dev.common.sheet.status'] = result.data[0].dicts.map(item => {
        if (item.sCodeValue === '70') {
          item.sCodeName = '执行'
        }
        return item
      })
    }).catch(() => {
    })
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      const { sUpSourceType } = searchInfo
      if (load && sUpSourceType) {
        this.selectId = null
        this.$refs.aggrid.loadTableData()
        const headerConfig = {
          sUpSourceType: this.searchInfo.sUpSourceType,
          sMenuCode: '01',
          aggridRefs: this.$refs.detailAggrid,
          defaultColumns: this.childrenColumns
        }
        this.getHeader(headerConfig)
      } else {
        this.selectId = null
        this.rowData = []
        this.rowDataChild = []
        this.headerCount = null
        this.footerCount = null
      }
    },

    setCount(vCount = 0, vSumQty = 0, vSumPkg = 0, flag) {
      this[flag] = [
        { count: vCount, key: 'count' },
        { title: '订货件数', count: SteelFormat.formatThousandthSign(vSumPkg), unit: this.$t('grid.others.pieces') },
        { title: '订货数量', count: SteelFormat.formatThousandthSign(vSumQty, 4), unit: this.$t('grid.others.ton') }
        // { title: this.$t('grid.others.shipmentAmount'), count: SteelFormat.formatPrice(vSumAmt), unit: this.$t('grid.others.yuan') }
      ]
    },
    hidebody(id) {
      this.dialogVisibleadd = false
      this.purInvoiceId = id
      // this.purVisible = true
    },
    // 点击 表格
    onRowDoubleClicked(params) {
      factContractbasics(params.data.sId).then((res) => {
        if (res.data) {
          this.$router.push({
            path: `/steelContractDetail/${params.data.sId}`,
            query: {
              Id: params.data.sId,
              status: params.data.sSheetStatus,
              type: 'edit',
              name: `匹配单【${params.data.sCode}】`,
              activeId: localStorage.getItem('menuId')
            }
          })
        } else {
          this.$message.error(this.$t('grid.others.recordDoesNotExist'))
        }
      })
    },

    onCloseAdd() {
      this.dialogVisibleadd = false
      this.$refs.aggrid.reloadTableData()
    },
    // handleStockDetailCount(list) {
    //   let vSumContractQty = 0; let vSumAmt = 0; let vSumQty = 0
    //   list.forEach(el => {
    //     vSumContractQty += el.sContractQty
    //     vSumAmt += el.sTaxAmt
    //     vSumQty += el.sQty
    //   })
    //   return [
    //     { count: list.length, key: 'count' },
    //     { title: this.$t('grid.others.numberOfShipments'), count: SteelFormat.formatThousandthSign(vSumContractQty, 4), unit: this.$t('grid.others.ton') },
    //     { title: this.$t('grid.others.numberOfPiecesShipped'), count: SteelFormat.formatThousandthSign(vSumQty), unit: this.$t('grid.others.pieces') },
    //     { title: this.$t('grid.others.shipmentAmount'), count: SteelFormat.formatPrice(vSumAmt), unit: this.$t('grid.others.yuan') }
    //   ]
    // },
    // 选中
    handleFooterCount(rowData) {

    },
    setTotal(vCount = 0, sumsQtx = 0, sumsQty = 0, flag) {
      this[flag] = [
        { count: vCount, key: 'count' },
        { title: '订货重量', count: SteelFormat.formatThousandthSign(sumsQtx, 4), unit: this.$t('grid.others.ton') },
        { title: '订货件数', count: SteelFormat.formatThousandthSign(sumsQty), unit: this.$t('grid.others.pieces') }
      ]
    },
    handleChildFooterCount(rowData) {
      this.$refs.detailAggrid.getSelectedData(res => {
        const vCount = res.length
        let sContractQty = new Decimal(0)
        let sQty = new Decimal(0)
        res.forEach(el => {
          sContractQty = sContractQty.add(el.sContractQty)
          sQty = sQty.add(el.sQty)
        })
        this.setTotal(vCount, +sContractQty, +sQty, 'footerCount')
      })
    },
    rowClicked({ data }) {
      if (!data) {
        this.selectId = null
        this.$refs.detailAggrid.loadTableData()
      } else if (data && this.selectId !== data.sId) {
        this.selectId = data.sId
        this.$refs.detailAggrid.loadTableData()
      }
    },
    // 主表
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        factoryContractPage(this.$refs.searchForm.getSearchData(), {
          ...pagination
        }).then(res => {
          this.rowData = res.data.content.map((item, index) => {
            if (this.selectId) {
              item._selected = this.selectId === item?.sId
            } else {
              item._selected = index === 0
            }
            item._selectedKeys = []
            return item
          })
          // factoryContractPageSum(
          //   this.$refs.searchForm.getSearchData(),
          //   { ...pagination }).then(v => {
          //   const { dataCount, sumsQtx, sumsQty } = v.data
          //   this.setTotal(dataCount, sumsQtx, sumsQty, 'headerCount')
          // })
          resolve(res.data)
          this.rowClicked({ data: res.data.content[0] })
        }).catch(() => {
          this.totalRowData = null
          reject(0)
        })
      })
    },
    loadDetail(pagination) {
      return new Promise((resolve, reject) => {
        factoryContractChild(this.selectId, pagination).then(res => {
          this.rowDataChild = res.data.page.content.map(item => {
            item._selected = false
            item._selectedKeys = []
            return item
          })
          const { vCount, vSumWeight, vSumQty } = res.data
          this.setTotal(vCount, vSumWeight, vSumQty, 'headerCount')
          resolve(res.data.page)
        }).catch(() => {
          reject([])
        })
      })
    }
  }
}
</script>
