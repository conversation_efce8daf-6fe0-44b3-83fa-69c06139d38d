<template>
  <div>
    <div class="auto-page-title">
      <div class="btn-group">
        <div class="text">
          钢厂订货明细
        </div>
        <div>
          <el-button
            v-has:esc_steel_factory_contract_detail_add
            type="primary"
            size="mini"
            :disabled="isBusinessDisabled('save', sSheetStatus)"
            @click="dialogVisible= true"
          >
            {{ $t('btns.add') }}
          </el-button>
          <el-button
            v-has:esc_steel_factory_contract_detail_delete
            type="danger"
            size="mini"
            :disabled="isBusinessDisabled('save', sSheetStatus)"
            @click="remove"
          >
            {{ $t('btns.delete') }}
          </el-button>
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :heightinif="300"
        row-key="sId"
        table-selection="multiple"
        :load-data="loadData"
        :paginationinif="false"
        @selectedChange="handleFooterTotal"
        @cellValueChanged="cellValueChanged"
      />
    </div>
    <goodDetailDialog
      :id="id"
      :dialog-visible="dialogVisible"
      :info="info"
      @close="closeDialog"
    />
  </div>
</template>

<script>
import { Moment } from 'cnd-utils'
import { SteelFormat } from 'cnd-horizon-utils'
import {
  factContractDetial,
  factContractDetailDelete
} from '@/api/steelInfoManage/steelContract.js'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import goodDetailDialog from '../dialog/goodDetailDialog.vue'
import businessMixin from '@/utils/businessMixin'
import { computeCellTotal } from '@/utils/common'
export default {
  components: { steelTradeAggrid, goodDetailDialog },
  mixins: [businessMixin],
  props: {
    id: {
      type: String,
      default: null
    },
    info: {
      type: Object,
      default: () => {}
    },
    updateFn: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      dialogVisible: false,
      columnDefs: [
        {
          headerName: '钢厂' + this.$t('grid.title.salesContractNumber'),
          field: 'sFactoryScontCode'
        },
        {
          headerName: '钢厂品类',
          field: 'sFactoryGoods'
        },
        {
          headerName: '品名',
          field: 'sArtName'
        },
        {
          headerName: '订货重量(吨)',
          field: 'sContractQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: '订货件数',
          field: 'sQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value)
          }
        },
        {
          headerName: '件重',
          field: 'sWeight',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return params.data._hiddenCheckbox ? '' : SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: '规格',
          field: 'sSpec'
        },
        {
          headerName: '厚度',
          field: 'sHigh'
        },
        {
          headerName: '宽度',
          field: 'sWidth'
        },
        {
          headerName: '长度',
          field: 'sLength'
        },
        {
          field: 'sTaxPrice',
          headerName: this.$t('grid.title.unitPriceWithTax'),
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return params.data._hiddenCheckbox ? '' : SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: '分段号',
          field: 'sSubsection'
        },
        {
          headerName: '切边形态',
          field: 'sSideShape'
        },
        {
          headerName: '探伤要求',
          field: 'sInspection'
        },
        {
          headerName: '交期时间',
          field: 'sDeliveryDate',
          cellStyle: { textAlign: 'right' },
          minWidth: 150,
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sDeliveryDate)
          }
        },
        {
          headerName: '交货允差上限',
          field: 'sDelvUpLimit',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return +params.value + '%'
            // return SteelFormat.toPercent(params.value)
          }
        },
        {
          headerName: '交货允差下限',
          field: 'sDelvDownLimit',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return +params.value + '%'
            // return SteelFormat.toPercent(params.value)
          }
        },
        {
          headerName: '备注',
          field: 'sRemark'
        }
      ],
      rowData: []
    }
  },
  computed: {
    sSheetStatus() {
      return this.info.sSheetStatus
    }
  },
  methods: {
    updateData() {
      this.$nextTick(() => {
        this.$refs.aggrid.loadTableData()
      })
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        factContractDetial(
          {
            sId: this.id
          }
        ).then(res => {
          this.rowData = res.data.map(item => {
            item._selected = false
            return item
          })
          this.handleCellTotal()
          resolve(res.data)
        }).catch(() => {
          reject([])
        })
      })
    },
    handleFooterTotal() {
      this.$refs.aggrid.getSelectedData(res => {
        this.handleCellTotal(res.length ? 'selected' : 'all')
      })
    },
    handleCellTotal(type = 'all') {
      setTimeout(() => {
        const totalList = computeCellTotal(
          this.rowData,
          {
            sContractQty: 0,
            sQty: 0,
            sLeftQtx: 0,
            sLeftQty: 0
          },
          {
            sFactoryScontCode: type === 'all' ? this.$t('grid.others.amountTo') : `${this.$t('components.selected')} ${this.rowData.filter(item => item._selected).length} ${this.$t('pagination.items')}`,
            sFactoryGoods: null,
            sArtName: null,
            sWeight: null,
            sSpec: null,
            sHigh: null,
            sWidth: null,
            sTaxPrice: null,
            sSubsection: null,
            sSideShape: null,
            sInspection: null,
            sDeliveryDate: null,
            sDelvUpLimit: null,
            sDelvDownLimit: null,
            sRemark: null,
            _selected: false,
            _hiddenCheckbox: true
          },
          type
        )
        this.$refs.aggrid.gridApi.setPinnedBottomRowData([totalList])
      }, 0)
    },
    closeDialog() {
      this.dialogVisible = false
      // this.$refs.aggrid.loadTableData()
      this.updateFn()
    },
    remove() {
      this.$refs.aggrid.getSelectedData(res => {
        if (res.length) {
          const idList = []
          res.forEach(item => {
            idList.push(item.sId)
          })
          factContractDetailDelete(idList).then(() => {
            this.$refs.aggrid.reloadTableData()
            this.$message.success(this.$t('tips.deletedSuccessfully'))
          })
        } else {
          this.$message.warning(this.$t('grid.others.pleaseCheckTheBox'))
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
