<template>
  <div>
    <cnd-btn-position top="6" right="10">
      <!-- <el-button
        type="primary"
        size="mini"
        :disabled="id && isBusinessDisabled('save', form.sSheetStatus)"
        @click="save"
      >{{ $t('btns.save') }}</el-button> -->
    </cnd-btn-position>
    <el-form
      ref="form"
      class="el-form-w100"
      label-width="110px"
      :inline="true"
      :model="form"
      :rules="rules"
      size="small"
      :disabled="id && isBusinessDisabled('save', form.sSheetStatus)"
    >
      <cnd-form-card-list :active-panel="activeCollapseName">
        <cnd-form-card :title="$t('grid.tabs.basicInformation')" name="1">
          <el-row>
            <cnd-form-item
              label="匹配单号"
              prop="sCode"
            >
              <el-input v-model="form.sCode" :disabled="true" />
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.others.supplier')" prop="sSupplierName">
              <horizon-search-select
                v-model="form.sSupplierName"
                type="customer"
                :placeholder="$t('components.pleaseSelect')"
                disabled
                @change="handleChangeSelect($event, 'sSupplierId')"
              />
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.title.company')" prop="sCompanyName">
              <horizon-search-select
                v-model="form.sCompanyName"
                disabled
                type="company"
                :placeholder="$t('components.pleaseSelect')"
                @change="handleChangeSelect($event, 'sCompanyId')"
              />
            </cnd-form-item>
            <!-- <cnd-form-item :custom-width="12" label="备注" prop="sRemark">
              <el-input v-model="form.sRemark" maxlength="256" type="textarea" :rows="2" />
            </cnd-form-item> -->
          </el-row>
        </cnd-form-card>
        <cnd-form-card :title="$t('grid.tabs.systemInformation')" name="3">
          <el-row>
            <cnd-form-item :label="$t('grid.title.createdBy')" prop="sCreatorName">
              <el-input v-model="form.sCreatorName" :disabled="true" clearable />
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.title.createdAt')" prop="sCreateTime">
              <el-date-picker
                v-model="form.sCreateTime"
                format="yyyy-MM-dd HH:mm:ss"
                type="date"
                :disabled="true"
                :placeholder="$t('grid.title.createdAt')"
              />
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.title.modifiedBy')" prop="sModifierName">
              <el-input v-model="form.sModifierName" :disabled="true" clearable />
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.title.modifiedAt')" prop="sModifyTime">
              <el-date-picker
                v-model="form.sModifyTime"
                format="yyyy-MM-dd HH:mm:ss"
                type="date"
                :disabled="true"
                :placeholder="$t('grid.title.modifiedAt')"
              />
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.title.status')" prop="sSheetStatus">
              <el-select v-model="form.sSheetStatus" disabled>
                <el-option
                  v-for="item in selectOps['dev.common.sheet.status']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
          </el-row>
        </cnd-form-card>
      </cnd-form-card-list>
    </el-form>
  </div>
</template>

<script>
import businessMixin from '@/utils/businessMixin'
import agreement from '@/api/agreement'
export default {
  mixins: [businessMixin],
  props: {
    id: {
      type: String,
      default: null
    },
    info: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      activeCollapseName: ['1', '2', '3'],
      form: { },
      selectOps: {
        'dev.common.sheet.status': null,
        'currencylist': null
      },
      rules: {
        // sIncomeAdjustDate: [
        //   { required: true, message: '请选择调整日期', trigger: 'change' }
        // ],
        // sAdjustReason: [
        //   { required: true, message: '请输入调整原因', trigger: 'blur' }
        // ]
      }
    }
  },
  watch: {
    info: {
      immediate: true,
      handler(val) {
        this.form = val
      }
    }
  },
  beforeMount() {
    agreement.getDict(['dev.common.sheet.status']).then(result => {
      this.selectOps['dev.common.sheet.status'] = result.data[0].dicts
    })
    agreement.currencylist().then(result => {
      this.selectOps['currencylist'] = result.data
    })
  },
  methods: {
    saveForm(fn) {
      this.$refs.form.validate((valid) => {
        if (valid) {
          fn && fn(this.form, true, this.id)
        } else {
          fn && fn(this.form, false, this.id)
          return false
        }
      })
    },
    save() {
      console.log('save: ')
      // this.$refs.form.validate(valid => {
      //   if (valid) {
      //     payOffsetSave(this.form)
      //       .then(() => {
      //         this.$message.success('保存成功')
      //         this.$emit('success')
      //       })
      //   }
      // })
    },
    handleChangeSelect(selected, val) {
      this.form[val] = selected ? selected.sId : undefined
      if (val === 'sCustomerId') {
        this.form['sInvCustomerId'] = selected ? selected.sId : undefined
      }
      if (val === 'sStaffId') {
        this.form['sDepartmentId'] = selected ? selected.orgUnitId : undefined
      // this.form['sReviewer'] = selected ? selected.sName : undefined
      }
      console.log(this.form.sInvCustomerId, this.form.sDepartmentId)
      console.log(selected)
    }
  }

}
</script>

<style scoped>

</style>
