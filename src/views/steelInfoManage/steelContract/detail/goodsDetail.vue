<template>
  <div>
    <purchaseDetail
      :id="id"
      ref="purchaseDetail"
      :info="info"
      :update-fn="updateFn"
    />
    <saleDetail
      :id="id"
      ref="saleDetail"
      :info="info"
      :update-fn="updateFn"
    />
    <orderDetail
      :id="id"
      ref="orderDetail"
      :info="info"
      :update-fn="updateFn"
    />
  </div>
</template>

<script>
import orderDetail from './orderDetail.vue'
import purchaseDetail from './purchaseDetail.vue'
import saleDetail from './saleDetail.vue'
export default {
  components: { purchaseDetail, saleDetail, orderDetail },
  props: {
    id: {
      type: String,
      default: null
    },
    info: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      updateFn: (refreshList = ['purchaseDetail', 'saleDetail', 'orderDetail']) => {
        console.log(refreshList)
        refreshList.forEach(el => {
          console.log('refreshList', this.$refs[el])
          if (this.$refs[el]) {
            this.$refs[el].updateData()
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
