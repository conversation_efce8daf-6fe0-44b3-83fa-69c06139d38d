<template>
  <div class="page-container">
    <cnd-dialog :title="$t('grid.tabs.basicInformation')" :visible="true" @close="onClose">
      <template slot="leftBtn">
        <el-button
          v-has:esc_steel_factory_contract_submit
          type="primary"
          size="mini"
          :disabled="isBusinessDisabled('submit', form.sSheetStatus)"
          @click="submit"
        >{{ $t('grid.others.submit') }}</el-button>
        <el-button
          v-has:esc_steel_factory_contract_delete
          type="danger"
          size="mini"
          :disabled="isBusinessDisabled('remove', form.sSheetStatus)"
          @click="remove"
        >{{ $t('btns.delete') }}</el-button>
        <el-button
          v-has:esc_steel_factory_contract_invalid
          type="danger"
          size="mini"
          :disabled="isBusinessDisabled('invalid', form.sSheetStatus)"
          @click="cancel"
        >{{ $t('grid.others.invalidate') }}</el-button>
        <el-button
          v-has:esc_steel_factory_contract_file
          type="primary"
          size="mini"
          :disabled="isBusinessDisabled('annex', form.sSheetStatus)"
          @click="dialogVisible.annex = true"
        >{{ $t('btns.attachmentManagement') }}</el-button>
      </template>
      <template slot="content">
        <el-tabs v-model="activeName" :before-leave="beforeLeave">
          <el-tab-pane :label="$t('grid.tabs.basicInformation')" name="basic">
            <baseInfo
              v-if="activeName === 'basic'"
              :id="id"
              ref="basic"
              :info="form"
              @success="getDetail"
            />
          </el-tab-pane>
          <el-tab-pane :label="$t('grid.tabs.commodityDetails')" name="goods">
            <goodsDetail
              v-if="activeName === 'goods'"
              :id="id"
              ref="goods"
              :info="form"
            />
          </el-tab-pane>
        </el-tabs>
      </template>
    </cnd-dialog>
    <steel-annex-dialog
      :visible="dialogVisible.annex"
      append-to-body
      :biz-id="id"
      :disabled-btn="{ scan: isBusinessDisabled('save', form.sSheetStatus), del: isBusinessDisabled('save', form.sSheetStatus) }"
      @onSelect="dialogVisible.annex = false"
    />

  </div>
</template>

<script>
import baseInfo from './basicInfo'
import goodsDetail from './goodsDetail'
import businessMixin from '@/utils/businessMixin'
import {
  factContractbasics,
  factContractSubmit,
  factContractRemove,
  factContractInvalid
} from '@/api/steelInfoManage/steelContract.js'
export default {
  name: 'SteelContractDetail',
  components: {
    baseInfo,
    goodsDetail
  },
  mixins: [businessMixin],
  data() {
    return {
      id: null,
      activeName: 'basic',
      oldDetail: null,
      dialogVisible: {
        annex: false
      },
      form: {}
    }
  },
  created() {
    this.id = this.$route.query.Id
    this.getDetail()
  },
  methods: {
    getDetail(val) {
      if (this.id) {
        factContractbasics(this.id).then(res => {
          this.form = res.data
          this.oldDetail = JSON.stringify(res.data)
        })
      }
    },
    // beforeLeave(activeName, oldActiveName) {
    //   if (activeName === 'basic') {
    //     this.getDetail()
    //   }
    //   let blFlag = true
    //   if (oldActiveName === 'basic' && !this.isBusinessDisabled('save', this.form.sSheetStatus)) {
    //     this.$refs.basic.saveForm((form, flag, id) => {
    //       if (flag) {
    //         if (this.oldDetail !== JSON.stringify(this.form)) {
    //           if (id) {
    //             payOffsetSave(this.form)
    //               .then(() => {
    //                 this.$message.success('保存成功')
    //               })
    //           }
    //         }
    //       } else {
    //         blFlag = false
    //       }
    //     })
    //   }
    //   return blFlag
    // },
    submit() {
      this.$confirm(this.$t('tips.isItOkToSubmit'), this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('grid.others.yes'),
        cancelButtonText: this.$t('btns.no'),
        type: 'warning'
      }).then(() => {
        factContractSubmit({
          sId: this.id
        }).then(() => {
          this.$message.success(this.$t('tips.submitSuccess'))
          this.getDetail()
        })
      })
    },
    remove() {
      this.$confirm(this.$t('tips.isItOkToDelete'), this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'info'
      }).then(() => {
        factContractRemove({
          sId: this.id
        }).then((res) => {
          if (res.code === '0000') {
            this.$message.success(this.$t('tips.deletedSuccessfully'))
            this.$tabDelete()
          }
        })
      })
    },
    cancel() {
      this.$confirm(this.$t('grid.tips.isVoidingConfirmed'), this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'info'
      }).then(() => {
        factContractInvalid({
          sId: this.id
        }).then(() => {
          this.$message.success(this.$t('tips.validateSuccess'))
          this.getDetail()
        })
      })
    },
    onClose() {
      this.$emit('onClose')
    }
  }
}
</script>

<style scoped>

</style>
