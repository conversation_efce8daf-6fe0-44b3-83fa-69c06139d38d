<template>
  <div>
    <div class="auto-page-title">
      <div class="btn-group">
        <div class="text">
          匹配采购合同
        </div>
        <div>
          <el-button
            v-has:esc_steel_factory_contract_pur_info
            type="primary"
            size="mini"
            :disabled="isBusinessDisabled('save', sSheetStatus)"
            @click="dialogVisible= true"
          >
            匹配采购合同
          </el-button>
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :heightinif="120"
        row-key="sId"
        :load-data="loadData"
        :paginationinif="false"
        @selectedChange="handleFooterTotal"
        @cellValueChanged="cellValueChanged"
      />
    </div>
    <purchaseDialog
      :id="id"
      :dialog-visible="dialogVisible"
      :info="info"
      @close="closeDialog"
    />
  </div>
</template>

<script>
import { SteelFormat } from 'cnd-horizon-utils'
import {
  factContractPurInfo
} from '@/api/steelInfoManage/steelContract.js'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import purchaseDialog from '../dialog/purchaseDialog.vue'
import businessMixin from '@/utils/businessMixin'
import { computeCellTotal } from '@/utils/common'
export default {
  components: { steelTradeAggrid, purchaseDialog },
  mixins: [businessMixin],
  props: {
    id: {
      type: String,
      default: null
    },
    info: {
      type: Object,
      default: () => {}
    },
    updateFn: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      dialogVisible: false,
      columnDefs: [
        {
          headerName: this.$t('grid.title.purchaseContractNumber'),
          field: 'sPurContractCode'
        },
        // {
        //   headerName: this.$t('grid.others.customer'),
        //   field: 'sCustomerName'
        // },
        {
          headerName: this.$t('grid.others.item'),
          field: 'sGoodsDetailName'
        },
        {
          headerName: this.$t('grid.others.supplier'),
          field: 'sSupplierName'
        },
        {
          headerName: this.$t('grid.title.company'),
          field: 'sCompanyName'
        },
        {
          headerName: '匹配数量',
          field: 'sContractQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: '匹配件数',
          field: 'sQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value)
          }
        }
      ],
      rowData: []
    }
  },
  computed: {
    sSheetStatus() {
      return this.info.sSheetStatus
    }
  },
  methods: {
    updateData() {
      this.$nextTick(() => {
        this.$refs.aggrid.loadTableData()
      })
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        factContractPurInfo(
          {
            sId: this.id
          }
        ).then(res => {
          console.log('res: ', res)
          // this.rowData = res.data.map(item => {
          //   item._selected = false
          //   return item
          // })
          this.rowData = [{ ...res.data, _selected: false }]
          // this.handleCellTotal()
          resolve(this.rowData)
        }).catch(() => {
          reject([])
        })
      })
    },
    handleFooterTotal() {
      // this.$refs.aggrid.getSelectedData(res => {
      //   this.handleCellTotal(res.length ? 'selected' : 'all')
      // })
    },
    handleCellTotal(type = 'all') {
      setTimeout(() => {
        const totalList = computeCellTotal(
          this.rowData,
          {
            sTaxAmt: 0
          },
          {
            sNoticeGoodsCode: type === 'all' ? this.$t('grid.others.amountTo') : `${this.$t('components.selected')} ${this.rowData.filter(item => item._selected).length} ${this.$t('pagination.items')}`,
            sPurContractCode: null,
            sSaleContractCode: null,
            vCompanyName: null,
            vDepartmentName: null,
            vStaffName: null,
            vCheckGroupName: null,
            _selected: false,
            _hiddenCheckbox: true
          },
          type
        )
        this.$refs.aggrid.gridApi.setPinnedBottomRowData([totalList])
      }, 0)
    },
    closeDialog() {
      this.dialogVisible = false
      // this.$refs.aggrid.loadTableData()
      this.updateFn()
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
