<template>
  <div class="page-container">
    <p class="page-title">合同关系配置工作台</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form
        ref="searchForm"
        :form-items="formItems"
        @search="onSearch"
      />
      <div class="btn-group mt-10">
        <div class="text">
          合同关系配置工作台列表
        </div>
        <div>
          <el-button
            v-has:esc_steel_factory_contract_wb_create
            type="primary"
            size="mini"
            @click="createAdjust"
          >
            配置合同关系
          </el-button>
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :child-column-defs="childColumnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :load-detail="loadDetail"
        :auto-load-data="false"
        :show-header-select="false"
        table-selection="multiple"
        row-key="sId"
        child-row-key="_Id"
        children-list-key="details"
        :header-total="headerTotal"
        :footer-total="footerTotal"
        is-subtable
        full-row-type="child"
        @selectedChange="handleFooterCount"
        @childRowValueChanged="rowValueChanged"
      />
    </div>
    <matchPurDialog
      v-if="dialogVisible"
      :select-data="selectData"
      :dialog-visible="dialogVisible"
      @close="closeDialog"
    />
  </div>
</template>

<script>
var Decimal = window.Decimal
// import Vue from 'vue'
// import { Middleware } from 'cndinfo-ui'
import { SteelFormat } from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
import {
  factContractWbPage,
  factContractWbPageSum,
  factContractWbChild
  // factoryContractCreate
} from '@/api/steelInfoManage/steelContract.js'
import matchPurDialog from './dialog/matchPurDialog.vue'
// import {
//   getDictet, getCnDitc
// } from '@/api/logistics/saleDelivery/saleorder'
import steelTradeAggrid from '@/components/steelTradeAggrid'
export default {
  name: 'RelationConfigWorkbench',
  components: { steelTradeAggrid, matchPurDialog },
  data() {
    return {
      searchInfo: null,
      formItems: [
        {
          label: '钢厂销售合同号',
          value: 'sFactoryScontCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.title.company'),
          value: 'sCompanyId',
          type: 'cndInputDialog',
          dialogType: 'company'
        },
        {
          label: this.$t('grid.others.supplier'), // 供应商
          value: 'sSupplierId',
          type: 'cndInputDialogItem',
          dialogType: 'customer',
          defaultUrl: '/esc/customer/page',
          option: { valueKey: 'sPath' },
          placeholder: this.$t('grid.others.pleaseSelectSupplier')
        },
        {
          label: this.$t('grid.title.createdAt'),
          value: ['startTime', 'sEndTime'],
          placeholder: [this.$t('grid.others.startDate'), this.$t('grid.others.endDate')],
          unlinkPanels: true,
          type: 'elDatePicker'
        },
        {
          label: '建发采购合同号',
          value: 'sPurContractCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.others.accountingFilter'),
          value: 'isWriteOff',
          type: 'elSelect',
          dict: 'dev.common.verify.finish.type',
          itemType: 'occultation',
          default: '0'
        }
      ],
      columnDefs: [
        {
          headerName: '钢厂销售合同号',
          field: 'sFactoryScontCode'
        },
        {
          headerName: '建发采购合同号',
          field: 'sPurContractCode'
        },
        {
          headerName: this.$t('grid.others.supplier'), // 供应商
          field: 'sSupplierName'
        },
        {
          headerName: this.$t('grid.title.company'),
          field: 'sCompanyName'
        },
        {
          headerName: this.$t('grid.title.createdAt'),
          field: 'sExtend10',
          cellStyle: { textAlign: 'right' },
          minWidth: 150,
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sExtend10)
          }
        }
      ],
      childColumnDefs: [
        {
          headerName: '钢厂品类',
          field: 'sFactoryGoods'
        },
        {
          headerName: '品名',
          field: 'sArtName'
        },
        {
          headerName: '订货重量(吨)',
          field: 'sContractQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: '订货件数',
          field: 'sQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value)
          }
        },
        // {
        //   headerName: this.$t('grid.others.quantityConfirmedThisTime'),
        //   field: 'confirmQtx',
        //   editable: true,
        //   cellEditorFramework: Vue.extend(
        //     Middleware.createComponent(
        //       'CndInputNumber',
        //       {
        //         mark: 'confirmQtx',
        //         type: 'number',
        //         decimalDigit: 4,
        //         autoFocus: true,
        //         focusSelect: true
        //       },
        //       {
        //         blur: ({ event, rowData, middleware }) => {
        //           // _this.setCurQty(event, rowData, middleware)
        //         }
        //       }
        //     )
        //   ),
        //   cellStyle: { textAlign: 'right' },
        //   valueFormatter: params => {
        //     return SteelFormat.formatThousandthSign(params.value, 4)
        //   }
        // },
        // {
        //   headerName: this.$t('grid.others.theNumberOfConfirmedPieces'),
        //   field: 'confirmQty',
        //   editable: true,
        //   cellEditorFramework: Vue.extend(
        //     Middleware.createComponent(
        //       'CndInputNumber',
        //       {
        //         mark: 'confirmQty',
        //         type: 'number',
        //         decimalDigit: 0,
        //         focusSelect: true
        //       },
        //       {
        //         blur: ({ event, rowData, middleware }) => {
        //           // _this.setCurPkgQty(event, rowData, middleware)
        //         }
        //       }
        //     )
        //   ),
        //   cellStyle: { textAlign: 'right' }
        // },
        // {
        //   headerName: '剩余数量/件数',
        //   cellStyle: { textAlign: 'right' },
        //   valueFormatter: params => {
        //     return `${SteelFormat.formatThousandthSign(params.data.sLeftQtx, 4)}/${SteelFormat.formatThousandthSign(params.data.sLeftQty)}`
        //   }
        // },
        {
          headerName: '件重',
          field: 'sWeight',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: '规格',
          field: 'sSpec'
        },
        {
          headerName: '厚度',
          field: 'sHigh'
        },
        {
          headerName: '宽度',
          field: 'sWidth'
        },
        {
          headerName: '长度',
          field: 'sLength'
        },
        {
          field: 'sTaxPrice',
          headerName: this.$t('grid.title.unitPriceWithTax'),
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: '分段号',
          field: 'sSubsection'
        },
        {
          headerName: '切边形态',
          field: 'sSideShape'
        },
        {
          headerName: '探伤要求',
          field: 'sInspection'
        },
        {
          headerName: '交期时间',
          field: 'sDeliveryDate',
          cellStyle: { textAlign: 'right' },
          minWidth: 150,
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sDeliveryDate)
          }
        },
        {
          headerName: '交货允差上限',
          field: 'sDelvUpLimit',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return +params.value + '%'
            // return SteelFormat.toPercent(params.value)
          }
        },
        {
          headerName: '交货允差下限',
          field: 'sDelvDownLimit',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return +params.value + '%'
            // return SteelFormat.toPercent(params.value)
          }
        },
        {
          headerName: '备注',
          field: 'sRemark'
        }
      ],
      rowData: [],
      headerTotal: null,
      footerTotal: null,
      options: {
        'income.type': ''
      },
      selectData: [],
      dialogVisible: false

    }
  },
  // created() {
  //   getDictet([
  //     'income.type'
  //   ]).then(result => {
  //     this.options['income.type'] = result.data[0].dicts
  //   }).catch(() => {
  //   })
  // },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        factContractWbPage(this.$refs.searchForm.getSearchData(), pagination).then(res => {
          this.rowData = res.data.content.map(item => {
            item._selected = false
            item._selectedKeys = []
            item.details = []
            return item
          })
          factContractWbPageSum(this.$refs.searchForm.getSearchData(), pagination).then(v => {
            const { dataCount, sumsQtx, sumsQty } = v.data
            this.setTotal(dataCount, sumsQtx, sumsQty, 'headerTotal')
          })
          resolve(res.data)
        }).catch(() => {
          reject([])
        })
      })
    },
    loadDetail(params) {
      return new Promise((resolve, reject) => {
        factContractWbChild(params.sId, this.searchInfo).then(res => {
          const data = res.data.map(item => {
            item._Id = params.sId
            item.confirmQtx = item.sLeftQtx
            item.confirmQty = item.sLeftQty
            return item
          })
          resolve(data)
        }).catch(() => {
          reject([])
        })
      })
    },
    setTotal(vCount = 0, sumsQtx = 0, sumsQty = 0, flag) {
      this[flag] = [
        { count: vCount, key: 'count' },
        { title: '订货件数', count: SteelFormat.formatThousandthSign(sumsQty), unit: this.$t('grid.others.pieces') },
        { title: '订货重量', count: SteelFormat.formatThousandthSign(sumsQtx, 4), unit: this.$t('grid.others.ton') }
      ]
    },
    handleFooterCount(rowData) {
      this.$refs.aggrid.getSelectedData(res => {
        const vCount = res.length
        let sContractQty = new Decimal(0)
        let sQty = new Decimal(0)
        res.forEach(el => {
          sContractQty = sContractQty.add(el.sContractQty)
          sQty = sQty.add(el.sQty)
        })
        this.setTotal(vCount, +sContractQty, +sQty, 'footerTotal')
      }, 'margeChild')
    },
    rowValueChanged(params) {
      console.log('params: ', params)
      const { data, rowIndex } = params
      this.rowData[data.parentId].details[rowIndex] = data
    },
    createAdjust() {
      // this.$refs.aggrid.stopChildEditing()
      this.$refs.aggrid.getSelectedData(selectedList => {
        if (!selectedList.length) {
          this.$message.warning(this.$t('grid.others.pleaseSelectAData'))
          return false
        }
        this.selectData = selectedList
        this.dialogVisible = true
        // this.$confirm('确定配置合同关系？', this.$t('grid.others.prompt'), {
        //   confirmButtonText: this.$t('btns.confirm'),
        //   cancelButtonText: this.$t('btns.cancel'),
        //   type: 'info'
        // }).then(() => {
        //   factoryContractCreate(selectedList).then(res => {
        //     this.$router.push({
        //       path: `/steelContractDetail/${res.data.sId}`,
        //       query: {
        //         Id: res.data.sId,
        //         type: 'edit',
        //         name: `匹配单【${res.data.sCode}】`,
        //         activeId: localStorage.getItem('menuId')
        //       }
        //     })
        //   })
        // })
      }, 'margeChild')
    },
    closeDialog() {
      this.dialogVisible = false
      this.$refs.aggrid.loadTableData()
    }
  }
}
</script>

<style scoped>

</style>
