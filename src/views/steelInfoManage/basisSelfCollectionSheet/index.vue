<!--
 * @Description:
-->
<template>
  <div class="page-container">
    <p class="page-title">基差自营收集表</p>
    <div class="layout-content auto-page-title flexV">
      <!-- 搜索条件 -->
      <steel-search-form
        ref="searchForm"
        :form-items="formItems"
        @search="onSearch"
      />
      <!-- 按钮组 -->
      <div class="btn-group mt-10">
        <div class="text">
          收集单列表
        </div>
        <div>
          <el-button
            key="comm_puragreement_list_add"
            v-has:basis_self_collection_sheet_list_add
            type="primary"
            size="mini"
            style="margin-left: 10px"
            @click="addContract"
          >{{ $t('btns.add') }}</el-button>
        </div>
      </div>

      <!-- 收集单列表 -->
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        table-selection="single"
        :page-sizes=" [10, 20, 30, 50, 100, 200, 500]"
        :default-page-size="defaultPageSize"
        @selectedChange="handleFooterCount"
      />
    </div>
  </div>
</template>

<script>
import Index from './Index.js'
export default Index
</script>
