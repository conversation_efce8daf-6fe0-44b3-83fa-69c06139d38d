<!--
 * @Author: dqr
 * @Date: 2024-09-27 09:24:06
 * @LastEditors: D <NAME_EMAIL>
 * @LastEditTime: 2024-12-09 16:30:40
 * @FilePath: /egl-web/src/views/steelInfoManage/basisSelfCollectionSheet/detail/BasisSelfTrackingSheetDetail.vue
 * @Description:
 *
-->
<!-- 跟踪单详情 -->
<template>
  <div class="detail">
    <cnd-dialog
      :title="$t('grid.tabs.basicInformation')"
      :visible="true"
      @close="onClose"
    >
      <template slot="leftBtn">
        <el-button
          v-if="selectId && basicData.sCollectionStatus === '30'"
          v-has:basis_self_collection_sheet_detail_finish
          type="primary"
          size="mini"
          @click="onContinue"
        >继续收集</el-button>
        <el-button
          v-if="selectId && basicData.sCollectionStatus === '20'"
          v-has:basis_self_collection_sheet_detail_finish
          type="primary"
          size="mini"
          @click="checkHedgingContract"
        >结束收集</el-button>
        <el-button
          v-if="selectId && basicData.sCollectionStatus !== '40'"
          v-has:basis_self_collection_sheet_detail_nullify
          type="danger"
          size="mini"
          @click="onNullify"
        >{{ $t('btns.void') }}</el-button>
      </template>
      <template slot="content">
        <!-- tab -->
        <el-tabs v-model="activeName" :before-leave="beforeLeave">
          <!-- 基础信息 -->
          <el-tab-pane :label="$t('grid.tabs.basicInformation')" name="basic">
            <BasisSelfCollectionSheetBasicDetail
              ref="basic"
              :form-data="basicData"
              @success="getDetail"
            />
          </el-tab-pane>
          <!-- 统计报表 -->
          <el-tab-pane label="统计报表" name="statistics">
            <StatisticsList
              :basic-data="basicData"
              :active-name="activeName"
            />
          </el-tab-pane>
          <!-- 填报详情 -->
          <el-tab-pane label="填报详情" name="priorDetail">
            <PriorDetail
              ref="priorDetail"
              :active-name="activeName"
              :basic-data="basicData"
            />
          </el-tab-pane>
        </el-tabs>
      </template>
    </cnd-dialog>
    <!-- 弹出层 -->
    <horizon-approval-dialog
      :id="selectId"
      :solt-btn="false"
      :visible.sync="dialogVisible.approval"
      @handleClose="dialogVisible.approval = false"
    />
    <horizon-approval-dialog
      :id="selectId"
      :type="'n8'"
      :solt-btn="false"
      :visible.sync="dialogVisible.approvalN8"
      @handleClose="dialogVisible.approvalN8 = false"
    />
    <steel-annex-dialog
      :visible="dialogVisible.annex"
      append-to-body
      :biz-id="selectId"
      :disabled-btn="{
        scan: isBusinessDisabled('save', basicData.sSheetStatus),
        del: isBusinessDisabled('save', basicData.sSheetStatus)
      }"
      @onSelect="dialogVisible.annex = false"
    />
    <!-- 结算收集弹窗 -->
    <!-- 校验填报明细都已提交，不通过提示“存在未提交数据，确认结束收集单？”，选择“确认”通过则收集状态变更为“已结束”，选择“取消”则放弃操作。 -->
    <cnd-dialog
      title="结束收集"
      :visible.sync="dialogVisible.finish"
      width="30%"
      ppend-to-body
      height="50"
      :fullscreen="false"
    >
      <div slot="content">
        <p class="mt-20 f16">存在未提交数据，确认结束收集单？</p>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="dialogVisible.finish = false">取 消</el-button>
        <el-button size="mini" type="primary" @click="onFinish">确 认</el-button>
      </div>
    </cnd-dialog>
  </div>
</template>

<script>
import basisSelfCollectionSheet from '@/api/basisSelfCollectionSheet'
import businessMixin from '@/utils/businessMixin'
import BasisSelfCollectionSheetBasicDetail from '../components/BasisSelfCollectionSheetBasicDetail'
import StatisticsList from '../components/StatisticsList.vue'
import PriorDetail from '../components/PriorDetail.vue'
export default {
  name: 'BasisSelfTrackingSheetDetail',
  components: {
    BasisSelfCollectionSheetBasicDetail,
    StatisticsList,
    PriorDetail
  },
  mixins: [businessMixin],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    },
    sCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogVisible: {
        annex: false, // 附件管理
        processQuery: false, // 进程查询
        approval: false, // 审批情况
        approvalN8: false,
        finish: false // 结束收集
      },
      selectId: this.$route.query.Id,
      selectCode: this.$route.params.code,
      basicData: {
        sCode: '',
        sTitle: '',
        sCollectionStartDate: '',
        sCollectionEndDate: '',
        sCollectionDate: [],
        sCollectionWeek: '',
        week: '',
        sStaffId: '',
        vStaffName: '',
        sDepartmentId: '',
        vDepartmentName: '',
        sCompanyId: '',
        vCompanyName: '',
        sCheckGroupId: '',
        vCheckGroupName: ''
      },
      activeName: 'basic', // active tab
      sDateType: '',
      oldDetail: ''
    }
  },
  activated() {
    if (this.activeName === 'priorDetail') {
      this.$refs.priorDetail.ensureIndexVisible()
    }
  },
  created() {
    if (this.selectId) {
      this.getDetail()
    }
  },
  methods: {
    // 获取基本信息
    getDetail(res) {
      if (res) {
        this.selectId = res.sId
      }
      basisSelfCollectionSheet
        .getMsbBasisProprietaryHedgingCollection(this.selectId)
        .then((res) => {
          this.basicData = res.data || {}
          this.oldDetail = JSON.stringify(res.data)
        })
    },
    // 关闭弹窗
    onClose() {
      this.$emit('onClose')
    },
    onSaveBasic(data) {
      const params = data
      basisSelfCollectionSheet.addMsbBasisProprietaryHedgingCollection(params)
    },
    beforeLeave(activeName, oldActiveName) {
      const blFlag = new Promise((resolve, reject) => {
        if (activeName === 'basic') {
          this.getDetail()
          resolve()
        }
        if (
          oldActiveName === 'basic' &&
          !this.isBusinessDisabled('save', this.basicData.sSheetStatus)
        ) {
          this.checkSave()
            .then(() => {
              resolve()
            })
            .catch(() => {
              reject()
            })
        } else {
          resolve()
        }
      })
      return blFlag
    },
    checkSave() {
      return new Promise((resolve, reject) => {
        this.$refs.basic.saveForm((flag, isChange) => {
          if (flag) {
            if (isChange) {
              this.$refs.basic
                .save()
                .then((res) => {
                  resolve(true)
                  this.loadDetail()
                })
                .catch(() => {
                  reject(false)
                })
            } else {
              resolve(true)
            }
          } else {
            reject(false)
          }
        })
      })
    },
    processQuery() {
      this.$router.push({
        path: '/purchaseQuerys',
        query: {
          sCode: this.basicData.sCode,
          name: this.$t('grid.others.procurementContractProcess'),
          activeId: localStorage.getItem('menuId')
        }
      })
    },
    onNullify() {
      this.$confirm(
        this.$t('grid.tips.isTheDeletionConfirmed'),
        this.$t('grid.others.prompt'),
        {
          confirmButtonText: this.$t('btns.confirm'),
          cancelButtonText: this.$t('btns.cancel'),
          type: 'warning'
        }
      )
        .then(() => {
          basisSelfCollectionSheet
            .nullifyHedgingContract(this.selectId)
            .then(() => {
              this.$message.success('操作成功')
              this.$tabDelete()
            })
        })
        .catch(() => {})
    },
    save() {
      this.$refs.basic.save('1')
    },
    // 校验填报明细都已提交，不通过提示“存在未提交数据，确认结束收集单？”，选择“确认”通过则收集状态变更为“已结束”，选择“取消”则放弃操作。
    checkHedgingContract() {
      basisSelfCollectionSheet
        .checkHedgingContract(this.selectId)
        .then((res) => {
          if (res.data) {
            this.onFinish()
          } else {
            this.$confirm(
              '存在未提交数据，确认结束收集单？',
              this.$t('grid.others.prompt'),
              {
                confirmButtonText: this.$t('btns.confirm'),
                cancelButtonText: this.$t('btns.cancel'),
                type: 'warning'
              }
            )
              .then(() => {
                this.onFinish()
              })
              .catch(() => {})
          }
        })
    },
    onFinish() {
      basisSelfCollectionSheet
        .endHedgingContract(this.selectId)
        .then(() => {
          this.$message.success('操作成功')
          this.dialogVisible.finish = false
          this.$tabDelete()
        })
    },
    onContinue() {
      this.$confirm(
        '是否确认继续收集？',
        this.$t('grid.others.prompt'),
        {
          confirmButtonText: this.$t('btns.confirm'),
          cancelButtonText: this.$t('btns.cancel'),
          type: 'warning'
        }
      )
        .then(() => {
          basisSelfCollectionSheet
            .continueHedgingContract(this.selectId)
            .then(() => {
              this.$message.success('操作成功')
              this.getDetail()
            })
        })
        .catch(() => {})
    }
  }
}
</script>
<style>
@import url('~@/styles/basisSelfCollectionSheet/detail.scss');
</style>
