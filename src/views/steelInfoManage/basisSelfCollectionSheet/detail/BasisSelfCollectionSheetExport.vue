<!--
 * @Author: dqr
 * @Date: 2024-07-02 09:12:07
 * @LastEditors: D <NAME_EMAIL>
 * @LastEditTime: 2024-09-23 17:31:23
 * @FilePath: /egl-web/src/views/steelInfoManage/basisSelfCollectionSheet/detail/BasisSelfCollectionSheetExport.vue
 * @Description:
 *
-->
<template>
  <div class="export-page">
    <!-- 根据list数据展示表格,相同type,option,name合并单元格,需要展示小计,合计,总计-->
    <table class="export-page_table">
      <caption>
        <h1>金属公司基差及自营业务周度简表（吨·万元）</h1>
      </caption>
      <thead>
        <tr>
          <th>业务类型</th>
          <th>操作</th>
          <th>班子分管</th>
          <th>部组</th>
          <th>未锁定现货数量</th>
          <th>未锁定现货盈亏</th>
          <th>期货套保</th>
          <th>期货盈亏</th>
          <th>期货套保后剩余敞口</th>
          <th>期货套保后整体盈亏</th>
          <th>备注</th>
        </tr>
      </thead>
      <tbody>
        <template v-for="(item, index) in list">
          <tr :key="index">
            <td
              v-if="index === 0 || item.sBusinessCategory !== list[index - 1].sBusinessCategory"
              :rowspan="item.sBusinessCategoryNum + add(item) + 1"
            >
              <div class="column">
                {{ item.sBusinessCategory }}
              </div>
            </td>
            <td
              v-if="index === 0 || item.sBusinessType !== list[index - 1].sBusinessType"
              :rowspan="item.sBusinessTypeNum + 1"
            >
              <div class="column">
                {{ item.sBusinessType }}
              </div>
            </td>
            <td
              v-if="
                index === 0 ||
                  item.sLeaderName !== list[index - 1].sLeaderName ||
                  (item.sBusinessType !== list[index - 1].sBusinessType && show(item.sLeaderNum))
              "
              :rowspan="item.sLeaderNum"
            >
              {{ item.sLeaderName }}
            </td>
            <td>{{ item.vDepartmentName }}</td>
            <td>{{ formatThousandthSign(item.sUnlockedExposureQty, 0) }}</td>
            <td>{{ formatPrice(item.sUnlockedExposureProprietaryProfit, 2) }}</td>
            <td>{{ formatThousandthSign(item.sFuturesHedgingQty, 0) }}</td>
            <td>{{ formatPrice(item.sOpenPositionProfit, 2) }}</td>
            <td class="big-font">{{ formatThousandthSign(item.sFuturesHedgingSurQty, 0) }}</td>
            <td class="big-font">{{ formatPrice(item.sFuturesHedgingQtyProfit, 2) }}</td>
            <td>{{ item.sRemark }}</td>
          </tr>
          <tr
            v-if="isOptionShow(item.sBusinessTypeNum - 1)"
            :key="index"
            class="subtotal f14 blod"
          >
            <td colspan="2">小计</td>
            <td>
              {{
                formatThousandthSign(list
                  .filter(
                    (i) => i.sBusinessCategory === item.sBusinessCategory && i.sBusinessType === item.sBusinessType
                  )
                  .reduce((acc, cur) => acc + cur.sUnlockedExposureQty, 0),0)
              }}
            </td>
            <td>
              {{
                formatPrice(list
                  .filter(
                    (i) => i.sBusinessCategory === item.sBusinessCategory && i.sBusinessType === item.sBusinessType
                  )
                  .reduce((acc, cur) => acc + cur.sUnlockedExposureProprietaryProfit, 0),2)
              }}
            </td>
            <td>
              {{
                formatThousandthSign(list
                  .filter(
                    (i) => i.sBusinessCategory === item.sBusinessCategory && i.sBusinessType === item.sBusinessType
                  )
                  .reduce((acc, cur) => acc + cur.sFuturesHedgingQty, 0),0)
              }}
            </td>
            <td>
              {{
                formatPrice(list
                  .filter(
                    (i) => i.sBusinessCategory === item.sBusinessCategory && i.sBusinessType === item.sBusinessType
                  )
                  .reduce((acc, cur) => acc + cur.sOpenPositionProfit, 0),2)
              }}
            </td>
            <td class="big-font">
              {{
                formatThousandthSign(list
                  .filter(
                    (i) => i.sBusinessCategory === item.sBusinessCategory && i.sBusinessType === item.sBusinessType
                  )
                  .reduce((acc, cur) => acc + cur.sFuturesHedgingSurQty, 0),0)
              }}
            </td>
            <td class="big-font">
              {{
                formatPrice(list
                  .filter(
                    (i) => i.sBusinessCategory === item.sBusinessCategory && i.sBusinessType === item.sBusinessType
                  )
                  .reduce((acc, cur) => acc + cur.sFuturesHedgingQtyProfit, 0),2)
              }}
            </td>
            <td />
          </tr>
          <tr v-if="isTypeShow(item.sBusinessCategoryNum - 1)" :key="index" class="sum f14 blod">
            <td colspan="3">共计</td>
            <td>
              {{
                formatThousandthSign(list
                  .filter((i) => i.sBusinessCategory === item.sBusinessCategory)
                  .reduce((acc, cur) => acc + cur.sUnlockedExposureQty, 0),0)
              }}
            </td>
            <td>
              {{
                formatPrice(list
                  .filter((i) => i.sBusinessCategory === item.sBusinessCategory)
                  .reduce((acc, cur) => acc + cur.sUnlockedExposureProprietaryProfit, 0),2)
              }}
            </td>
            <td>
              {{
                formatThousandthSign(list
                  .filter((i) => i.sBusinessCategory === item.sBusinessCategory)
                  .reduce((acc, cur) => acc + cur.sFuturesHedgingQty, 0),0)
              }}
            </td>
            <td>
              {{
                formatPrice(list
                  .filter((i) => i.sBusinessCategory === item.sBusinessCategory)
                  .reduce((acc, cur) => acc + cur.sOpenPositionProfit, 0),2)
              }}
            </td>
            <td class="big-font">
              {{
                formatThousandthSign(list
                  .filter((i) => i.sBusinessCategory === item.sBusinessCategory)
                  .reduce((acc, cur) => acc + cur.sFuturesHedgingSurQty, 0),0)
              }}
            </td>
            <td class="big-font">
              {{
                formatPrice(list
                  .filter((i) => i.sBusinessCategory === item.sBusinessCategory)
                  .reduce((acc, cur) => acc + cur.sFuturesHedgingQtyProfit, 0),2)
              }}
            </td>
            <td />
          </tr>
        </template>
        <tr class="aggregate f14 blod">
          <td colspan="4">总计</td>
          <td>
            {{
              formatThousandthSign(list.reduce((acc, cur) => acc + cur.sUnlockedExposureQty, 0),0)
            }}
          </td>
          <td>
            {{
              formatPrice(list.reduce((acc, cur) => acc + cur.sUnlockedExposureProprietaryProfit, 0),2)
            }}
          </td>
          <td>
            {{
              formatThousandthSign(list.reduce((acc, cur) => acc + cur.sFuturesHedgingQty, 0),0)
            }}
          </td>
          <td>
            {{
              formatPrice(list.reduce((acc, cur) => acc + cur.sOpenPositionProfit, 0),2)
            }}
          </td>
          <td class="big-font">
            {{
              formatThousandthSign(list.reduce((acc, cur) => acc + cur.sFuturesHedgingSurQty, 0),0)
            }}
          </td>
          <td class="big-font">
            {{
              formatPrice(list.reduce((acc, cur) => acc + cur.sFuturesHedgingQtyProfit, 0),2)
            }}
          </td>
          <td />
        </tr>
      </tbody>
      <tfoot>
        <tr>
          <td colspan="10"> 1、数据更新于{{ sCollectionStartDate }}（期货按收盘价计算）</td>
        </tr>
        <tr>
          <td colspan="10"> 2、按照做账规则，现货盈亏为不含税</td>
        </tr>
        <tr>
          <td colspan="10"> 3、期货套保后剩余敞口=未锁定现货数量-期货套保数量</td>
        </tr>
        <tr>
          <td colspan="10"> 4、期货套保后整体盈亏=未锁定现货盈亏+期货盈亏</td>
        </tr>
      </tfoot>
    </table>
  </div>
</template>

<script>
import { Moment } from 'cnd-utils'
import { SteelFormat } from 'cnd-horizon-utils'
import basisSelfCollectionSheet from '@/api/basisSelfCollectionSheet'
let businessTypeNum = 0
let businessCategoryNum = 0
export default {
  name: 'HelloWorld',
  data() {
    return {
      list: [],
      selectId: this.$route.query.Id,
      sCollectionStartDate: ''
    }
  },
  created() {
    this.exportStatistics()
  },
  methods: {
    exportStatistics() {
      basisSelfCollectionSheet.exportStatistics(this.selectId).then(res => {
        if (!res.data) return
        this.list = res.data.statisticsVoList
        this.sCollectionStartDate = res.data.sCollectionStartDate
      })
    },
    formatThousandthSign(num, fixed) {
      return SteelFormat.formatThousandthSign(num, fixed)
    },
    formatPrice(num, fixed) {
      return SteelFormat.formatPrice(num, fixed)
    },
    formatTime(time) {
      return Moment.time('YYYY.MM.DD', time)
    },
    show(num) {
      return Math.abs(0 - num) === num
    },
    isOptionShow(sBusinessTypeNum) {
      console.log('--------', businessTypeNum, sBusinessTypeNum)
      if (businessTypeNum !== sBusinessTypeNum) {
        businessTypeNum++
        return false
      } else {
        businessTypeNum = 0
        return true
      }
    },
    isTypeShow(sBusinessCategoryNum) {
      console.log('--------', businessCategoryNum, sBusinessCategoryNum)
      if (businessCategoryNum !== sBusinessCategoryNum) {
        businessCategoryNum++
        return false
      } else {
        businessCategoryNum = 0
        return true
      }
    },
    add(item) {
      // list中相同的type下有option的类型有几种
      console.log('-------------------', this.list
        .filter((i) => i.sBusinessCategory === item.sBusinessCategory))
      const list = this.list
        .filter((i) => i.sBusinessCategory === item.sBusinessCategory)
        .map((i) => i.sBusinessType)

      return [...new Set(list)].length
    }
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="scss">
.export-page {
  background: #ffffff;
  height: 100%;
  padding: 20px 40px;
  box-sizing: border-box;
  overflow-y: scroll;
  h1 {
    text-align: center;
  }
  th {
    font-size: 16px;
  }
  &_content {
    padding: 20px 40px;
  }
  &_header {
    height: 50px;
    border: 1px solid #000;
    & > div {
      flex: 1;
      text-align: center;
      border-right: 1px solid #000;
      height: 50px;
      line-height: 25px;
    }
  }

  .export-page_table {
    width: 100%;
  }
  caption {
    margin-bottom: 20px;
  }
  table {
    border-collapse: collapse;
    border-spacing: 0px;
  }
  thead th,
  thead td,
  tbody th,
  tbody td {
    padding: 5px;
    border: 1px solid black;
    text-align: center;
    &.big-font {
      font-weight: 700;
      font-size: 14px;
    }
  }
  tfoot td {
    padding: 5px 0;
  }
  tfoot {
    border: 1px solid black;
  }
  .aggregate {
    background-color: #ffbe00;
  }
  .subtotal {
    background-color: #ffff00;
  }
  .sum {
    background-color: #00b0f0;
  }
  .f14 {
    font-size: 14px;
  }
  .blod {
    font-weight: 600;
  }
  .column {
    width: 20px;
    display: inline-block;
  }
}
</style>
