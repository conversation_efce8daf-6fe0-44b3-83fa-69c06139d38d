
<!-- 跟踪单-合同详情、上期填报表数据 -->
<template>
  <div>
    <cnd-dialog
      :title="$t('grid.tabs.basicInformation')"
      :visible="true"
      @close="onClose"
    >
      <template v-if="isMaintain === '1' && sCollectionStatus !== '40'" slot="leftBtn">
        <el-button v-has:basis_self_collection_sheet_contract_fill_in_save type="primary" size="mini" @click="save">{{
          $t('btns.save')
        }}</el-button>
        <el-button
          type="primary"
          size="mini"
          @click="gotoContractDetail"
        >查看系统合同信息</el-button>
        <el-button v-has:basis_self_collection_sheet_contract_fill_in_delete type="danger" size="mini" @click="onDel">{{
          $t('btns.delete')
        }}</el-button>
      </template>
      <template slot="content">
        <el-form
          ref="form"
          class="el-form-w100"
          label-width="100px"
          :inline="true"
          :model="basicData"
          :rules="rules"
          size="small"
          :disabled="isMaintain !== '1'"
        >
          <cnd-form-card-list :active-panel="activeCollapseName">
            <!-- 销售 上期填报基础信息、合同维护填报-->
            <SellPriorContractBasicDetail
              v-if="basicData.sBusinessType === '02' || basicData.sBusinessType === '04'"
              ref="basicDetail"
              :form-data="basicData"
              :is-maintain="isMaintain"
              :other-data="otherData"
              @updateOptionInvolved="updateOptionInvolved"
            />
            <!-- 采购 上期填报基础信息、合同维护填报-->
            <PurchasePriorContractBasicDetail
              v-if="basicData.sBusinessType === '01' || basicData.sBusinessType === '03'"
              ref="basicDetail"
              :form-data="basicData"
              :is-maintain="isMaintain"
              :other-data="otherData"
              @updateOptionInvolved="updateOptionInvolved"
            />
          </cnd-form-card-list>
        </el-form>
        <el-row>
          <el-col :span="16">
            <!-- 期货信息 -->
            <FuturesList
              ref="FuturesList"
              :form-data="basicData"
              :row-data="futuresRowData"
            />
            <!-- 期货交易明细 -->
            <FuturesDealList
              :form-data="basicData"
              :row-data="futuresDealRowData"
              :s-hedging-collection-contract-id="selectId"
              :is-maintain="isMaintain"
              :s-collection-status="sCollectionStatus"
              :is-edit="isMaintain === '1'"
              :ops="selectOps"
              :save-basic-data="save"
              @refresh="onFuturesRefresh"
            /></el-col>
          <el-col :span="8" class="h100">
            <!-- 期权信息 -->
            <ShareOptionList
              ref="ShareOptionList"
              :form-data="basicData"
              :is-edit="isMaintain === '1' && sIsOptionInvolved === '1'"
              :row-data="shareOptionRowData"
              :s-hedging-collection-contract-id="selectId"
              :s-collection-status="sCollectionStatus"
              @refresh="getShareOptionList"
            />
          </el-col></el-row>
      </template>
    </cnd-dialog>
  </div>
</template>

<script>
import { getDictet } from '@/api/logistics/saleDelivery/saleorder'
import businessMixin from '@/utils/businessMixin'
import FuturesList from '../components/FuturesList.vue'
import FuturesDealList from '../components/FuturesDealList'
import basisSelfCollectionSheet from '@/api/basisSelfCollectionSheet'
import ShareOptionList from '../components/ShareOptionList.vue'
import SellPriorContractBasicDetail from '../components/SellPriorContractBasicDetail.vue'
import PurchasePriorContractBasicDetail from '../components/PurchasePriorContractBasicDetail.vue'
export default {
  name: 'BasisSelfCollectionSheetContractPriorDetail',
  components: {
    FuturesList,
    FuturesDealList,
    ShareOptionList,
    SellPriorContractBasicDetail,
    PurchasePriorContractBasicDetail
  },
  mixins: [businessMixin],
  data() {
    return {
      activeCollapseName: ['1', '2', '3'],
      selectOps: {
        'dev.common.sheet.status': [],
        'collection.state': [],
        'base.yes-no': [],
        'open.close.position': []
      },
      selectId: this.$route.query.Id,
      sContractType: this.$route.query.sContractType,
      sContractNo: this.$route.query.sContractNo,
      sCollectionStatus: this.$route.query.sCollectionStatus,
      sHedgingCollectionId: this.$route.query.sHedgingCollectionId,
      basicData: {},
      otherData: {
        sOptionsOpenPositionProfit: 0,
        sOptionsPositionQty: 0,
        sOpenPositionProfit: 0,
        sPositionQty: 0
      },
      rules: {
        // 合同号
        sContractNo: [
          { required: true, message: '请输入合同号', trigger: 'blur' }
        ],
        // 项目号
        sProjectNo: [
          { required: true, message: '请输入项目号', trigger: 'blur' }
        ],
        // 合同数量
        sContractQty: [
          { required: true, message: '请输入合同数量', trigger: 'blur' }
        ],
        // 已销售数量
        sTradedQty: [
          { required: true, message: '请输入已销售数量', trigger: 'blur' }
        ],
        // 采购成本
        sTradedPrice: [
          { required: true, message: '请输入采购成本', trigger: 'blur' }
        ],
        // 部门
        vDepartmentName: [
          { required: true, message: '请选择部门', trigger: 'change' }
        ],
        // 人员
        vStaffName: [
          { required: true, message: '请选择人员', trigger: 'change' }
        ],
        // 公司
        vCompanyName: [
          { required: true, message: '请选择公司', trigger: 'change' }
        ],
        // 核算组
        vCheckGroupName: [
          { required: true, message: '请选择核算组', trigger: 'change' }
        ],
        // 结算方式
        sSettlementMethod: [
          { required: true, message: '请选择结算方式', trigger: 'change' }
        ],
        // 钢厂资源
        sGoodsCategoryDesc: [
          { required: false, message: '请选择钢厂资源', trigger: 'change' }
        ],
        sTradedProfit: [
          { required: true, message: '请输入盈亏', trigger: 'blur' }
        ],
        sMarketingPrice: [
          { required: true, message: '请输入市价', trigger: 'blur' }
        ],
        sIsOptionInvolved: [
          { required: true, message: '请选择是否涉及期权', trigger: 'change' }
        ],
        sTaxRate: [
          { required: true, message: '请输入税率', trigger: 'blur' }
        ],
        sCollectionCompleteStatus: [
          { required: true, message: '请选择是否完结', trigger: 'change' }
        ],
        sCostStatus: [
          { required: true, message: '请选择成本状态', trigger: 'change' }
        ]
      },
      // 维护状态 0 查看 1 维护
      isMaintain: this.$route.query.isMaintain,
      // 期货信息列表
      futuresRowData: [],
      // 期货交易明细列表
      futuresDealRowData: [],
      // 期权信息列表
      shareOptionRowData: [],
      sIsOptionInvolved: '0'
    }
  },
  async created() {
    const result = await getDictet(['dev.common.sheet.status', 'collection.state', 'base.yes-no', 'open.close.position'])
    this.selectOps['dev.common.sheet.status'] = result.data[0]?.dicts
    this.selectOps['collection.state'] = result.data[1]?.dicts
    this.selectOps['base.yes-no'] = result.data[2]?.dicts
    this.selectOps['open.close.position'] = result.data[3]?.dicts
    this.getDetail()
    if (this.isMaintain === '1') {
      this.getFuturesList()
      this.getFuturesDealList()
      this.getShareOptionList()
    }
  },
  methods: {
    // 获取基本信息
    getDetail() {
      if (this.isMaintain === '1') {
        basisSelfCollectionSheet
          .getReportContractInfo(this.sHedgingCollectionId)
          .then((res) => {
            this.basicData = res.data ? { ...res.data } : {}
            this.oldDetail = JSON.stringify(res.data)
          })
      } else {
        basisSelfCollectionSheet.getReportContractInfoPreviousPeriod({
          sId: this.sHedgingCollectionId,
          sContractNo: this.sContractNo
        }).then((res) => {
          if (res.data) {
            this.basicData = res.data ? { ...res.data } : {}
            this.futuresRowData = res.data.futureInfoVoList || []
            this.futuresDealRowData = res.data.futuresVoList || []
            this.shareOptionRowData = res.data.optionsVoList || []
            this.futuresRowData = res.data.futureInfoVoList.map((item) => {
              item._selected = false
              item._selectedKeys = []
              return item
            })
            this.futuresDealRowData = res.data.futuresVoList.map((item) => {
              item._selected = false
              item._selectedKeys = []
              return item
            })
            this.shareOptionRowData = res.data.optionsVoList.map((item) => {
              item._selected = false
              item._selectedKeys = []
              return item
            })
          }
        })
      }
    },
    // 获取期货信息列表
    getFuturesList() {
      basisSelfCollectionSheet
        .getReportContractInfoFutures(this.selectId)
        .then((res) => {
          if (res.data) {
            this.otherData.sOpenPositionProfit = res.data.sOpenPositionProfit
            this.otherData.sPositionQty = res.data.sPositionQty
            this.futuresRowData = res.data.futureInfoVoList.map((item) => {
              item._selected = false
              item._selectedKeys = []
              return item
            })
          }
        })
    },
    // 获取期货交易明细列表
    getFuturesDealList() {
      basisSelfCollectionSheet
        .getReportContractFuturesList(this.selectId)
        .then((res) => {
          if (!res.data) return
          this.futuresDealRowData = res.data.map((item) => {
            item._selected = false
            item._selectedKeys = []
            return item
          })
        })
    },
    // 获取期权信息列表
    getShareOptionList() {
      basisSelfCollectionSheet
        .getReportContractInfoOptions(this.selectId)
        .then((res) => {
          if (res.data) {
            this.otherData.sOptionsOpenPositionProfit = res.data.sOptionsOpenPositionProfit
            this.otherData.sOptionsPositionQty = res.data.sOptionsPositionQty
            this.shareOptionRowData = res.data.optionsVoList.map((item) => {
              item._selected = false
              item._selectedKeys = []
              return item
            })
          }
        })
    },
    save() {
      return new Promise(resolve => {
        const form = this.$refs.basicDetail.form
        const params = {
          ...form,
          sIsCollectionCompleteApply: form.sIsCollectionCompleteApply || form.sCollectionCompleteStatus
        }
        this.$refs.form.validate((valid) => {
          if (valid) {
            basisSelfCollectionSheet
              .updateReportContractInfo(params)
              .then(() => {
                this.$message.success('保存成功')
                this.getShareOptionList()
                resolve()
              })
          }
        })
      })
    },
    onDel() {
      this.$confirm('是否删除该条数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          basisSelfCollectionSheet
            .removeReportContractInfo(
              this.selectId
            )
            .then(() => {
              this.$message.success('删除成功')
              this.$tabDelete(this.$route.query.fromPath)
            })
        })
        .catch(() => {})
    },
    // 期货信息刷新
    onFuturesRefresh() {
      this.getFuturesList()
      this.getFuturesDealList()
    },
    gotoContractDetail() {
      this.$router.push({
        path: `/basisSelfCollectionSheetContractDetail/${this.selectId}`,
        query: {
          Id: this.selectId,
          sContractType: this.sContractType,
          sBusinessType: this.basicData.sBusinessType,
          isMaintain: this.isMaintain,
          name: `合同详情【${this.basicData.sContractNo}】`,
          sContractNo: this.basicData.sContractNo,
          type: 'read',
          activeId: localStorage.getItem('menuId')
        }
      })
    },
    addFutureDeal() {
      this.$refs.FuturesDealList.add()
    },
    updateOptionInvolved(data) {
      console.log('🚀 ~ updateOptionInvolved ~ data:', data)
      this.sIsOptionInvolved = data.sIsOptionInvolved
    }
  }
}
</script>
<style lang="scss" scoped>
.el-range-editor--small.el-input__inner {
  height: 26px;
}
::v-deep.form-item-container {
  .el-form-item .el-form-item__content {
    line-height: 24px !important;
  }
}
::v-deep.el-range-editor--small .el-range__icon {
  line-height: 20px;
}
::v-deep .dialog-body {
  height: 100%;
}
</style>
