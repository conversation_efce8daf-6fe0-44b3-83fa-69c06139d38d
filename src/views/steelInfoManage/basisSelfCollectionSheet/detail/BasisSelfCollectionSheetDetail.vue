<!--
 * @Author: dqr
 * @Date: 2024-07-02 09:11:54
 * @LastEditors: D <NAME_EMAIL>
 * @LastEditTime: 2024-11-26 10:33:11
 * @FilePath: /egl-web/src/views/steelInfoManage/basisSelfCollectionSheet/detail/BasisSelfCollectionSheetDetail.vue
 * @Description:
 *
-->
<!-- 收集单详情 -->
<template>
  <div class="detail">
    <cnd-dialog
      :title="$t('grid.tabs.basicInformation')"
      :visible="true"
      @close="onClose"
    >
      <template slot="leftBtn">
        <el-button
          v-if="selectId"
          v-has:basis_self_collection_sheet_detail_submit
          :disabled="isBusinessDisabled('submit', basicData.sSheetStatus)"
          type="primary"
          size="mini"
          @click="save"
        >{{ $t('grid.others.submit') }}</el-button>
        <el-button
          v-if="selectId"
          v-has:basis_self_collection_sheet_detail_delete
          :disabled="isBusinessDisabled('remove', basicData.sSheetStatus)"
          type="danger"
          size="mini"
          @click="onDel"
        >{{ $t('btns.delete') }}</el-button>

        <el-button
          type="primary"
          size="mini"
          @click="dialogVisible.approval = true"
        >{{ $t('grid.others.approvalStatus') }}</el-button>
      </template>
      <template slot="content">
        <!-- tab -->
        <el-tabs v-model="activeName" :before-leave="beforeLeave">
          <!-- 基础信息 -->
          <el-tab-pane :label="$t('grid.tabs.basicInformation')" name="basic">
            <BasisSelfCollectionSheetBasicDetail
              ref="basic"
              :form-data="basicData"
              @success="getDetail"
            />
          </el-tab-pane>
          <!-- 合同明细 -->
          <el-tab-pane
            :label="$t('grid.others.contractDetails')"
            name="contract"
          >
            <BasisSelfCollectionSheetContractList
              :active-name="activeName"
              :basic-data="basicData"
            />
          </el-tab-pane>
        </el-tabs>
      </template>
    </cnd-dialog>
    <!-- 弹出层 -->
    <horizon-approval-dialog
      :id="selectId"
      :solt-btn="false"
      :visible.sync="dialogVisible.approval"
      @handleClose="dialogVisible.approval = false"
    />
    <horizon-approval-dialog
      :id="selectId"
      :type="'n8'"
      :solt-btn="false"
      :visible.sync="dialogVisible.approvalN8"
      @handleClose="dialogVisible.approvalN8 = false"
    />
    <steel-annex-dialog
      :visible="dialogVisible.annex"
      append-to-body
      :biz-id="selectId"
      :disabled-btn="{
        scan: isBusinessDisabled('save', basicData.sSheetStatus),
        del: isBusinessDisabled('save', basicData.sSheetStatus)
      }"
      @onSelect="dialogVisible.annex = false"
    />
  </div>
</template>

<script>
import basisSelfCollectionSheet from '@/api/basisSelfCollectionSheet'
import businessMixin from '@/utils/businessMixin'
import BasisSelfCollectionSheetBasicDetail from '../components/BasisSelfCollectionSheetBasicDetail'
import BasisSelfCollectionSheetContractList from '../components/BasisSelfCollectionSheetContractList.vue'
export default {
  name: 'BasisSelfCollectionSheetDetail',
  components: {
    BasisSelfCollectionSheetBasicDetail,
    BasisSelfCollectionSheetContractList
  },
  mixins: [businessMixin],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    },
    sCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogVisible: {
        annex: false, // 附件管理
        processQuery: false, // 进程查询
        approval: false, // 审批情况
        approvalN8: false
      },
      selectId: this.$route.query.Id,
      selectCode: this.$route.params.code,
      basicData: {
        sCode: '',
        sTitle: '',
        sCollectionStartDate: '',
        sCollectionEndDate: '',
        sCollectionDate: [],
        sCollectionWeek: '',
        week: '',
        sStaffId: '',
        vStaffName: '',
        sDepartmentId: '',
        vDepartmentName: '',
        sCompanyId: '',
        vCompanyName: '',
        sCheckGroupId: '',
        vCheckGroupName: ''
      },
      activeName: 'basic', // active tab
      sDateType: '',
      oldDetail: ''
    }
  },
  created() {
    if (this.selectId) {
      this.getDetail()
    }
  },
  methods: {
    // 获取基本信息
    getDetail(res) {
      if (res) {
        this.selectId = res.sId
      }
      basisSelfCollectionSheet
        .getMsbBasisProprietaryHedgingCollection(this.selectId)
        .then((res) => {
          this.basicData = res.data || {}
          this.oldDetail = JSON.stringify(res.data)
        })
    },
    // 关闭弹窗
    onClose() {
      this.$emit('onClose')
    },
    onSaveBasic(data) {
      const params = data
      basisSelfCollectionSheet.addMsbBasisProprietaryHedgingCollection(params)
    },
    beforeLeave(activeName, oldActiveName) {
      const blFlag = new Promise((resolve, reject) => {
        if (activeName === 'basic') {
          this.getDetail()
          resolve()
        }
        if (
          oldActiveName === 'basic' &&
          !this.isBusinessDisabled('save', this.basicData.sSheetStatus)
        ) {
          this.checkSave()
            .then(() => {
              resolve()
            })
            .catch(() => {
              reject()
            })
        } else {
          resolve()
        }
      })
      return blFlag
    },
    checkSave() {
      return new Promise((resolve, reject) => {
        this.$refs.basic.saveForm((flag, isChange) => {
          if (flag) {
            if (isChange) {
              this.$refs.basic
                .save()
                .then((res) => {
                  resolve(true)
                  this.loadDetail()
                })
                .catch(() => {
                  reject(false)
                })
            } else {
              resolve(true)
            }
          } else {
            reject(false)
          }
        })
      })
    },
    processQuery() {
      this.$router.push({
        path: '/purchaseQuerys',
        query: {
          sCode: this.basicData.sCode,
          name: this.$t('grid.others.procurementContractProcess'),
          activeId: localStorage.getItem('menuId')
        }
      })
    },
    onDel() {
      this.$confirm(this.$t('grid.tips.isTheDeletionConfirmed'), this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'warning'
      })
        .then(() => {
          basisSelfCollectionSheet
            .removeMsbBasisProprietaryHedgingCollection(this.selectId)
            .then(() => {
              this.$message.success(this.$t('tips.deletedSuccessfully'))
              this.$tabDelete()
            })
        })
        .catch(() => {})
    },
    save() {
      this.$refs.basic?.save('1')
    }
  }
}
</script>
<style lang="scss" scoped>
@import url('~@/styles/basisSelfCollectionSheet/detail.scss');
</style>

