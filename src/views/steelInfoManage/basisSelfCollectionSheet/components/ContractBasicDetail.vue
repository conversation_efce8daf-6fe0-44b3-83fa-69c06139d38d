<!-- 收集单合同详情-合同信息基础信息 -->
<template>
  <cnd-form-card :title="$t('grid.tabs.basicInformation')" name="1">
    <el-row>
      <cnd-form-item label="业务类型" prop="sBusinessType">
        <el-select v-model="form.sBusinessType">
          <el-option
            v-for="item in selectOps['basis.self.business.type']"
            :key="item.sCodeValue"
            :label="item.sCodeName"
            :value="item.sCodeValue"
          />
        </el-select>
      </cnd-form-item>
      <cnd-form-item label="合同类型" prop="sBusinessType">
        <el-select v-model="form.sBusinessType">
          <el-option
            v-for="item in selectOps['basis.self.business.type']"
            :key="item.sCodeValue"
            :label="item.sCodeName"
            :value="item.sCodeValue"
          />
        </el-select>
      </cnd-form-item>
      <cnd-form-item label="合同号" prop="sContractNo">
        <el-input v-model="form.sContractNo" />
      </cnd-form-item>
      <cnd-form-item label="项目号" prop="sProjectNo">
        <el-input v-model="form.sProjectNo" />
      </cnd-form-item>
      <cnd-form-item label="合同数量" prop="sContractQty">
        <cnd-input-number v-model="form.sContractQty" clearable type="number" :decimal-digit="3" />
      </cnd-form-item>
      <cnd-form-item label="已销售数量" prop="sTradedQty">
        <cnd-input-number v-model="form.sTradedQty" type="number" :decimal-digit="3" />
      </cnd-form-item>
      <cnd-form-item label="敞口吨数" prop="sExposureQty">
        <cnd-input-number v-model="form.sExposureQty" type="number" :decimal-digit="3" />
      </cnd-form-item>
      <cnd-form-item label="税率" prop="sTaxRate">
        <cnd-input-number :value="form.sTaxRate || ''" type="percent" />
      </cnd-form-item>
      <cnd-form-item label="采购成本" prop="sTradedPrice">
        <cnd-input-number v-model="form.sTradedPrice" />
      </cnd-form-item>
      <cnd-form-item label="当前销售市价" prop="sMarketingPrice">
        <cnd-input-number v-model="form.sMarketingPrice" />
      </cnd-form-item>
      <!-- <cnd-form-item label="是否长协采购" prop="sCollectionStatus">
        <el-select v-model="form.sCollectionStatus">
          <el-option
            v-for="item in selectOps['base.yes-no']"
            :key="item.sCodeValue"
            :label="item.sCodeName"
            :value="item.sCodeValue"
          />
        </el-select>
      </cnd-form-item> -->
      <cnd-form-item label="钢厂品种资源" prop="sGoodsCategoryDesc">
        <el-input v-model="form.sGoodsCategoryDesc" />
      </cnd-form-item>
      <cnd-form-item label="是否完结" prop="sCollectionCompleteStatus">
        <el-select v-model="form.sCollectionCompleteStatus">
          <el-option
            v-for="item in selectOps['base.yes-no']"
            :key="item.sCodeValue"
            :label="item.sCodeName"
            :value="item.sCodeValue"
          />
        </el-select>
      </cnd-form-item>
      <cnd-form-item label="完结说明" prop="sCollectionCompleteRemark">
        <el-input v-model="form.sCollectionCompleteRemark" />
      </cnd-form-item>
      <cnd-form-item label="备注" prop="sRemark">
        <el-input v-model="form.sRemark" />
      </cnd-form-item>
      <cnd-form-item label="上期填报人" prop="vPreviousReportName">
        <el-input v-model="form.vPreviousReportName" disabled />
      </cnd-form-item>
      <!-- 人员 -->
      <cnd-form-item :label="$t('grid.title.personnel')" prop="vStaffName">
        <horizon-search-select
          v-model="form.vStaffName"
          default-url="/esc/staff/dialog/org/v2/page/"
          search-key="sName"
          :option="{label: 'sPath', value: 'id'}"
          @change="handleStaffChange"
        />
      </cnd-form-item>
      <!-- 部门 -->
      <cnd-form-item
        :label="$t('grid.others.department')"
        prop="vDepartmentName"
      >
        <horizon-search-select
          v-model="form.vDepartmentName"
          type="depart"
          @change="handleChange($event, 'sDepartmentId')"
        />
      </cnd-form-item>

      <!-- 公司 -->
      <!-- <cnd-form-item :label="$t('grid.title.company')" prop="vCompanyName">
        <horizon-search-select
          v-model="form.vCompanyName"
          type="company"
          @change="handleChange($event, 'sCompanyId')"
        />
      </cnd-form-item> -->
      <!-- 核算组 -->
      <!-- <cnd-form-item
        :label="$t('grid.title.accountingGroup')"
        prop="vCheckGroupName"
      >
        <horizon-search-select
          v-model="form.vCheckGroupName"
          type="cost"
          @change="handleChange($event, 'sCheckGroupId')"
        />
      </cnd-form-item> -->
    </el-row>
  </cnd-form-card>
</template>

<script>
import { getDictet } from '@/api/logistics/saleDelivery/saleorder'
// import agreement from '@/api/agreement'
import businessMixin from '@/utils/businessMixin'
import { Moment } from 'cnd-utils'
export default {
  name: 'DomesticProcurementBasicDetail',
  mixins: [businessMixin],
  props: {
    formData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    id: {
      type: String,
      default: ''
    },
    // 是否维护状态
    isMaintain: {
      type: String,
      default: '0'
    }
  },
  data() {
    return {
      activeCollapseName: ['1', '2'],
      selectOps: {
        'base.yes-no': [],
        'basis.self.business.type': [],
        'dev.common.sheet.status': []
      }

    }
  },
  computed: {
    form() {
      console.log('----------------', this.formData)
      return this.formData
    }
  },
  created() {
    getDictet(['base.yes-no', 'basis.self.business.type', 'dev.common.sheet.status'])
      .then((result) => {
        this.selectOps['base.yes-no'] = result.data[0]?.dicts
        this.selectOps['basis.self.business.type'] = result.data[1]?.dicts
        this.selectOps['dev.common.sheet.status'] = result.data[2]?.dicts
      })
      .catch(() => {})
  },
  methods: {
    handleChange(val, key) {
      this.form[key] = val.sId || val.sCode
    },
    // 选择填表日期
    fillInDateChange(val) {
      console.log('🚀 ~ fillInDateChange ~ val:', val, Moment.weekCount(val[0]))
      if (val) {
        this.form.week = `第${Moment.weekCount(val[0])}周`
      }
    },
    handleStaffChange(val) {
      console.log('🚀 ~ handleStaffChange ~ val:', val)
      this.handleChange(val, 'sStaffId')
      if (val) {
        this.form.sDepartmentId = val.orgUnitId
        this.form.vDepartmentName = val.orgUnitName
      }
    }
  }
}
</script>
