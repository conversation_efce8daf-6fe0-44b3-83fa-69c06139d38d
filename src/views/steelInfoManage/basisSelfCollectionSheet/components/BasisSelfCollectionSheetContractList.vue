<!-- 基差自营收集单-合同信息 -->
<template>
  <div class="page-container tab">
    <div class="layout-content auto-page-title flexV">
      <!-- 搜索条件 -->
      <steel-search-form
        ref="searchForm"
        :form-items="formItems"
        :active-panel="''"
        @search="onSearch"
      />
      <!-- 按钮组 -->
      <div class="btn-group mt-10">
        <div class="text">{{ $t('grid.others.contractDetails') }}</div>
        <div>
          <el-button
            key="comm_puragreement_list_add"
            v-has:basis_self_collection_sheet_contract_add_n8
            type="primary"
            size="mini"
            style="margin-left: 10px"
            :disabled="isRead"
            @click="addN8Contract"
          >添加N8合同信息</el-button>
          <el-button
            key="comm_puragreement_list_add"
            v-has:basis_self_collection_sheet_contract_eliminate
            type="primary"
            size="mini"
            style="margin-left: 10px"
            :disabled="isRead"
            @click="openDialog('closingPriceConfigDialogVisible')"
          >收盘价配置</el-button>
          <el-button
            key="comm_puragreement_list_add"
            v-has:basis_self_collection_sheet_contract_add
            type="primary"
            size="mini"
            style="margin-left: 10px"
            :disabled="isRead"
            @click="addContractDetail('sale')"
          >新增销售合同</el-button>
          <el-button
            key="comm_puragreement_list_add"
            v-has:basis_self_collection_sheet_contract_add
            type="primary"
            size="mini"
            style="margin-left: 10px"
            :disabled="isRead"
            @click="addContractDetail('pur')"
          >新增采购合同</el-button>
          <el-button
            key="comm_puragreement_list_add"
            v-has:basis_self_collection_sheet_contract_eliminate
            type="danger"
            size="mini"
            style="margin-left: 10px"
            :disabled="isRead"
            @click="eliminateFinishContract"
          >剔除完结合同</el-button>
          <el-button
            key="comm_puragreement_list_add"
            type="primary"
            size="mini"
            style="margin-left: 10px"
            :disabled="isRead"
            @click="openContractDetail"
          >合同详情</el-button>
          <el-button
            key="comm_puragreement_list_add"
            v-has:basis_self_collection_sheet_contract_add
            type="primary"
            size="mini"
            style="margin-left: 10px"
            :disabled="isRead"
            @click="openDialog('addPriorVisible')"
          >添加上期新增</el-button>
          <el-button
            key="comm_puragreement_list_add"
            v-has:basis_self_collection_sheet_contract_delete
            type="danger"
            size="mini"
            style="margin-left: 10px"
            :disabled="isRead"
            @click="onDel"
          >{{ $t('btns.delete') }}</el-button>
        </div>
      </div>
      <!-- 合同明细 -->
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        row-key="sId"
        table-selection="multiple"
        :page-sizes=" [10, 20, 30, 50, 100, 200, 500]"
        :default-page-size="defaultPageSize"
        @selectedChange="handleFooterCount"
        @rowDoubleClicked="rowDoubleClicked"
      />
    </div>
    <ClosingPriceConfigDialog v-if="closingPriceConfigDialogVisible" :visible.sync="closingPriceConfigDialogVisible" :basic-data="basicData" @refresh="onSearch" />
    <AddPriorDialog v-if="addPriorVisible" :visible.sync="addPriorVisible" :s-hedging-collection-id="basicData.sId" @refresh="onSearch" />
  </div>
</template>

<script>
import BasisSelfCollectionSheetContractList from './BasisSelfCollectionSheetContractList.js'
export default BasisSelfCollectionSheetContractList
</script>
<style lang="scss" scoped>
@import url('~@/styles/basisSelfCollectionSheet/detail.scss');
</style>
