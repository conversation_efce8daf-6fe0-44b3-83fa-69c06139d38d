<!--
 * @Author: dqr
 * @Date: 2024-07-02 09:12:07
 * @LastEditors: D <NAME_EMAIL>
 * @LastEditTime: 2024-12-12 09:39:23
 * @FilePath: /egl-web/src/views/steelInfoManage/basisSelfCollectionSheet/components/PriorDetail.vue
 * @Description:
 *
-->
<!-- 基差自营跟踪单-填报明细 -->
<template>
  <div class="page-container tab">
    <div class="layout-content auto-page-title flexV">
      <!-- 搜索条件 -->
      <steel-search-form
        ref="searchForm"
        :form-items="formItems"
        :is-collapse="true"
        active-panel="''"
        @search="onSearch"
      />
      <div class="btn-group">
        <div class="text">明细</div>
        <div>
          <el-button
            v-if="basicData.sCollectionStatus !== '40'"
            v-has:basis_self_collection_sheet_contract_finish_re_check
            type="primary"
            size="mini"
            :disabled="!curSelRowData.length"
            @click="dialogVisible.finish = true"
          >完结复核</el-button>
          <el-button
            type="primary"
            size="mini"
            :disabled="curSelRowData.length !== 1"
            @click="gotoPriorDetail"
          >填报详情</el-button>
        </div>
      </div>
      <!-- 统计报表 -->
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="true"
        table-selection="multiple"
        :footer-total="footerCount"
        :private-column-defs="true"
        :suppress-copy-rows-to-clipboard="true"
        :page-sizes=" [10, 20, 30, 50, 100, 200, 500]"
        :default-page-size="defaultPageSize"
        @selectedChange="gridSelectedChange"
        @rowDoubleClicked="rowDoubleClicked"
        @bodyScroll="agBodyScroll"
      />
    </div>
    <!-- 完结复核 -->
    <cnd-dialog
      :visible.sync="dialogVisible.finish"
      :fullscreen="false"
      title="完结复核"
      height="80px"
      width="100px"
      :append-to-body="true"
      @close="dialogVisible.finish = false"
    >
      <template slot="content">
        <div class="flexCC mt-15">
          <el-button
            size="mini"
            @click="onFinish(false)"
          >驳回</el-button>
          <el-button
            size="mini"
            type="primary"
            @click="onFinish(true)"
          >通过</el-button>
        </div>
      </template>
    </cnd-dialog>
  </div>
</template>

<script>
import { SteelFormat } from 'cnd-horizon-utils'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import basisSelfCollectionSheet from '@/api/basisSelfCollectionSheet'
import agreement from '@/api/agreement'
import { getCnDitc } from '@/api/logistics/saleDelivery/saleorder'
import exportBtn from '@/components/exportBtnV2'
import { Moment } from 'cnd-utils'
export default {
  name: 'PriorDetail',
  components: {
    steelTradeAggrid,
    exportBtn
  },
  props: {
    basicData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    isEdit: {
      type: Boolean,
      default: true
    },
    activeName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogVisible: {
        finish: false
      },
      formItems: [
        // 填报状态
        {
          label: '填报状态',
          value: 'sReportingStatus',
          type: 'elSelect',
          dict: [
            {
              sCodeName: '已填报',
              sCodeValue: '1'
            },
            {
              sCodeName: '未填报',
              sCodeValue: '0'
            }
          ],
          placeholder: '请选择填报状态'
        },
        // 提交状态
        {
          label: '提交状态',
          value: 'sSheetStatus',
          type: 'elSelect',
          dict: [
            {
              sCodeName: '准备',
              sCodeValue: '10'
            },
            {
              sCodeName: '已提交',
              sCodeValue: '21'
            }
          ],
          placeholder: '请选择提交状态'
        },
        // 是否申请完结
        {
          label: '是否申请完结',
          value: 'sIsCollectionCompleteApply',
          type: 'elSelect',
          dict: 'base.yes-no',
          placeholder: '请选择是否申请完结'
        },
        // 是否新增
        {
          label: '是否新增',
          value: 'sIsSystemData',
          type: 'elSelect',
          dict: 'base.yes-no',
          placeholder: '请选择是否新增'
        },
        // 合同号
        {
          label: '合同号',
          value: 'sContractNo',
          type: 'elInput',
          placeholder: '请输入合同号'
        },
        {
          label: '业务类型',
          value: 'sBusinessType',
          type: 'elSelect',
          dict: 'basis.self.business.type',
          placeholder: '请选择收集状态'
        },
        // 完结复核
        {
          label: '完结复核',
          value: 'sCollectionCompleteReviewStatus',
          type: 'elSelect',
          dict: 'basis.check.state',
          placeholder: '请选择完结复核'
          // default: '30'
        },
        // 部门
        {
          label: this.$t('grid.others.department'),
          value: 'sDepartmentId',
          type: 'cndInputDialog',
          dialogType: 'depart',
          placeholder: this.$t('grid.others.pleaseSelectTheDepartment')
        },
        // 人员
        {
          label: this.$t('grid.title.personnel'),
          value: 'sStaffId',
          type: 'cndInputDialogItem',
          option: { valueKey: 'sPath' },
          dialogType: 'staff',
          placeholder: this.$t('grid.others.pleaseSelectPersonnel')
        }
        // // 公司
        // {
        //   label: this.$t('grid.title.company'),
        //   value: 'sCompanyId',
        //   type: 'cndInputDialog',
        //   dialogType: 'company',
        //   placeholder: this.$t('grid.others.pleaseSelectCompany')
        // },
        // // 核算组
        // {
        //   label: this.$t('grid.title.accountingGroup'),
        //   value: 'sCheckGroupId',
        //   type: 'cndInputDialog',
        //   dialogType: 'cost',
        //   placeholder: this.$t('grid.others.pleaseSelectAccountingGroup')
        // }
      ],
      selectOps: {
        'base.yes-no': [],
        'open.close.position': [],
        // 填报状态
        'reporting.status': [
          {
            sCodeName: '已填报',
            sCodeValue: '1'
          },
          {
            sCodeName: '未填报',
            sCodeValue: '0'
          }
        ],
        // 提交状态
        'sheet.status': [
          {
            sCodeName: '准备',
            sCodeValue: '10'
          },
          {
            sCodeName: '已提交',
            sCodeValue: '21'
          }
        ],
        'basis.self.contract.type': []
      },
      columnDefs: [
        {
          headerName: '填报人',
          field: 'vReportName',
          enableRowGroup: true
        },
        {
          headerName: '填报状态',
          field: 'sReportingStatus',
          valueFormatter: (params) => {
            return getCnDitc(
              params,
              this.selectOps['reporting.status'],
              'sReportingStatus'
            )
          }
        },
        {
          headerName: '提交人',
          field: 'sSubmitterName'
        },
        {
          headerName: '提交状态',
          field: 'sSheetStatus',
          valueFormatter: (params) => {
            return getCnDitc(
              params,
              this.selectOps['sheet.status'],
              'sSheetStatus'
            )
          }
        },
        {
          headerName: '是否申请完结',
          field: 'sIsCollectionCompleteApply',
          valueFormatter: (params) => {
            return getCnDitc(
              params,
              this.selectOps['base.yes-no'],
              'sIsCollectionCompleteApply'
            )
          }
        },
        {
          headerName: '完结原因',
          field: 'sCollectionCompleteRemark'
        },
        {
          headerName: '完结复核',
          field: 'sCollectionCompleteReviewStatus',
          valueFormatter: (params) => {
            return getCnDitc(
              params,
              this.selectOps['basis.check.state'],
              'sCollectionCompleteReviewStatus'
            )
          }
        },
        {
          headerName: '人员',
          field: 'vStaffName'
        },
        {
          headerName: '业务类型',
          field: 'sBusinessType',
          valueFormatter: (params) => {
            return getCnDitc(
              params,
              this.selectOps['basis.self.business.type'],
              'sBusinessType'
            )
          }
        },
        {
          headerName: '项目号',
          field: 'sProjectNo'
        },
        {
          headerName: '合同号',
          field: 'sContractNo'
        },
        // {
        //   headerName: '合同类型',
        //   field: 'sContractType',
        //   valueFormatter: (params) => {
        //     return getCnDitc(
        //       params,
        //       this.selectOps['basis.self.contract.type'],
        //       'sContractType'
        //     )
        //   }
        // },
        {
          headerName: '合同数量',
          field: 'sContractQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.data.sContractQty, 3)
          }
        },
        {
          headerName: '已销售/已采购数量',
          field: 'sTradedQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.data.sTradedQty, 3)
          }
        },
        {
          headerName: '已销售/已采购盈亏（万元）',
          field: 'sTradedProfit',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: '敞口吨数',
          field: 'sExposureQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.data.sExposureQty, 3)
          }
        },
        {
          headerName: '采购成本/当前采购市价',
          field: '',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            if (params.data.sBusinessType === '01' || params.data.sBusinessType === '03') {
              return SteelFormat.formatPrice(params.data.sTradedPrice)
            } else {
              return SteelFormat.formatPrice(params.data.sMarketingPrice)
            }
          }
        },
        {
          headerName: '销售/预售价格',
          field: '',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            if (params.data.sBusinessType === '02' || params.data.sBusinessType === '04') {
              return SteelFormat.formatPrice(params.data.sTradedPrice)
            } else {
              return SteelFormat.formatPrice(params.data.sMarketingPrice)
            }
          }
        },
        {
          headerName: '敞口现货盈亏（万元）',
          field: 'sExposureProprietaryProfit',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: '期货持仓吨数',
          field: 'sPositionQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.data.sPositionQty, 3)
          }
        },
        {
          headerName: '期货持仓盈亏（万元）',
          field: 'sExposureFuturesProfit',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: '期现合计盈利（万元）',
          field: 'sExposureProfit',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: '是否涉及期权',
          field: 'sIsOptionInvolved',
          valueFormatter: (params) => {
            return getCnDitc(
              params,
              this.selectOps['base.yes-no'],
              'sIsOptionInvolved'
            )
          }
        },
        {
          headerName: '期权持仓盈亏（万元）',
          field: 'sOptionsOpenPositionProfit',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: '钢厂品种资源',
          field: 'sGoodsCategoryDesc'
        },
        {
          headerName: '是否长协采购',
          field: 'sIsLongtermPur',
          valueFormatter: (params) => {
            return getCnDitc(
              params,
              this.selectOps['base.yes-no'],
              'sIsLongtermPur'
            )
          }
        },
        {
          headerName: '是否新增',
          field: 'sIsSystemData',
          valueFormatter: (params) => {
            return !params.value ? '是' : '否'
          }
        },
        // 部门
        {
          headerName: this.$t('grid.others.department'),
          field: 'vDepartmentName',
          enableRowGroup: true
        },
        // // 公司
        // {
        //   headerName: this.$t('grid.title.company'),
        //   field: 'vCompanyName',
        //   enableRowGroup: true
        // },
        // // 核算组
        // {
        //   headerName: this.$t('grid.title.accountingGroup'),
        //   field: 'vCheckGroupName',
        //   enableRowGroup: true
        // },
        // 修改人
        {
          headerName: this.$t('grid.title.modifiedBy'),
          field: 'vModifierName'
        },
        // 修改时间
        {
          headerName: this.$t('grid.title.modifiedAt'),
          field: 'sModifyTime',
          valueGetter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sModifyTime)
          }
        }
      ],
      rowData: [],
      curSelRowData: [],
      searchInfo: {},
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      footerCount: null,
      selectId: this.$route.query.Id,
      defaultPageSize: 500,
      currentScrollTop: 0
    }
  },
  watch: {
    activeName() {
      if (this.activeName === 'priorDetail') {
        console.log('====================================')
        console.log(this.currentScrollTop)
        console.log('====================================')
      }
    }
  },
  created() {
    agreement
      .getDict(['base.yes-no', 'open.close.position', 'basis.self.contract.type', 'basis.self.business.type', 'basis.check.state'])
      .then((result) => {
        this.selectOps['base.yes-no'] = result.data[0]?.dicts
        this.selectOps['open.close.position'] = result.data[1]?.dicts
        this.selectOps['basis.self.contract.type'] = result.data[2]?.dicts
        this.selectOps['basis.self.business.type'] = result.data[3]?.dicts
        this.selectOps['basis.check.state'] = result.data[4]?.dicts
      })
      .catch(() => {})
  },
  methods: {
    onSearch(load = true) {
      console.log('🚀 ~ onSearch ~ onSearch:')
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    gridSelectedChange(rowData) {
      console.log('🚀 ~ gridSelectedChange ~ rowData:', rowData)
      this.curSelRowData = rowData.filter((item) => item._selected)
    },
    loadData(pagination = { page: 0, limit: 30 }, searchInfo) {
      const params = {
        ...this.$refs.searchForm.getSearchData(),
        sHedgingCollectionId: this.selectId,
        sIsSystemData: !this.searchInfo.sIsSystemData ? '' : this.searchInfo.sIsSystemData !== '1'
      }
      return new Promise((resolve, reject) => {
        basisSelfCollectionSheet
          .getReportContractInfoPage(
            params,
            pagination
          )
          .then((res) => {
            this.rowData = res.data.content.map((item) => {
              item._selected = false
              item._selectedKeys = []
              return item
            })
            resolve(res.data)
          })
          .catch(() => {
            reject(0)
          })
      })
    },
    async onFinish(pass) {
      const ids = this.curSelRowData.map((item) => item.sId)
      if (pass) {
        await basisSelfCollectionSheet.passReportContractInfo(ids)
      } else {
        await basisSelfCollectionSheet.rejectReportContractInfo(ids)
      }
      this.$message({
        type: 'success',
        message: '操作成功!'
      })
      this.dialogVisible.finish = false
      this.loadData()
    },
    // 获取基本信息
    async getDetail(sId) {
      return basisSelfCollectionSheet
        .getReportContractInfo(sId)
    },
    async gotoPriorDetail() {
      const res = await this.getDetail(this.curSelRowData[0].sId)
      if (!res.data) {
        return
      }
      this.$router.push({
        path: `/basisSelfCollectionSheetContractPriorDetail/${this.curSelRowData[0].sId}`,
        query: {
          Id: this.curSelRowData[0].sId,
          name: '合同维护填报' + `【${this.curSelRowData[0].sContractNo}】`,
          sHedgingCollectionId: this.curSelRowData[0].sId,
          activeId: localStorage.getItem('menuId'),
          isMaintain: '1',
          type: 'edit',
          sBusinessType: this.curSelRowData[0].sBusinessType,
          sCollectionStatus: this.basicData.sCollectionStatus,
          fromPath: window.location.pathname + window.location.search
        }
      })
    },
    async rowDoubleClicked(d) {
      const res = await this.getDetail(d.data.sId)
      if (!res.data) {
        return
      }
      this.$router.push({
        path: `/basisSelfCollectionSheetContractPriorDetail/${d.data.sId}`,
        query: {
          Id: d.data.sId,
          type: 'edit',
          name: '合同维护填报' + `【${d.data.sContractNo}】`,
          sHedgingCollectionId: d.data.sId,
          isMaintain: '1',
          sBusinessType: d.data.sBusinessType,
          activeId: localStorage.getItem('menuId'),
          sCollectionStatus: this.basicData.sCollectionStatus,
          fromPath: window.location.pathname + window.location.search
        }
      })
    },
    agBodyScroll(event) {
      if (event.direction === 'vertical') {
        this.currentScrollTop = event.top
      }
    },
    ensureIndexVisible(index) {
      const gridApi = this.getGridApi()
      if (gridApi) {
        const element = this.$el.querySelector('.ag-row')
        const styles = window.getComputedStyle(element).height
        const rowHeight = Number(styles.replace('px', ''))
        this.$nextTick(() => {
          if (this.currentScrollTop > 0 && rowHeight > 0) {
            const scrollNumber = Math.floor(this.currentScrollTop / rowHeight)
            this.getGridApi().ensureIndexVisible(scrollNumber, 'top')
          }
        })
      }
    }
  }
}
</script>
<style>
@import url('~@/styles/basisSelfCollectionSheet/detail.scss');
</style>
