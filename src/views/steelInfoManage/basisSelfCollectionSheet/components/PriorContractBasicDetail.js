import { getDictet } from '@/api/logistics/saleDelivery/saleorder'
import businessMixin from '@/utils/businessMixin'
import { Moment } from 'cnd-utils'
import numberValidator from '@/components/numberValidator/index.vue'
export default {
  name: 'DomesticProcurementBasicDetail',
  mixins: [businessMixin],
  components: {
    numberValidator
  },
  props: {
    formData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    id: {
      type: String,
      default: ''
    },
    // 是否维护状态
    isMaintain: {
      type: String,
      default: '0'
    },
    otherData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      activeCollapseName: ['1', '2'],
      selectOps: {
        'dev.common.sheet.status': [],
        // 业务类型
        'basis.self.business.type': [],
        // 结算方式
        'basis.self.settlement.method': [
          {
            sCodeName: '锁价',
            sCodeValue: 'PL'
          },
          {
            sCodeName: '后结算',
            sCodeValue: 'AS'
          }
        ],
        // 成本情况
        'basis.self.cost.situation': [
          {
            sCodeName: '全部已出',
            sCodeValue: 'AR'
          },
          {
            sCodeName: '未出',
            sCodeValue: 'UR'
          }
        ],
        // 是否
        'base.yes-no': [],
        // 开平仓
        'open.close.position': [],
        // 填报状态
        'reporting.status': [
          {
            sCodeName: '已填报',
            sCodeValue: '1'
          },
          {
            sCodeName: '未填报',
            sCodeValue: '0'
          }
        ],
        // 复核状态
        'basis.check.state': []
      }
      // form: {}
    }
  },
  computed: {
    form() {
      return this.formData
    },
    // 敞口现货盈亏（万元）
    // existingProfitLoss() {
    //   if (this.form.sCostStatus === 'UR') {
    //     return 0
    //   } else {
    //     return (
    //       +(
    //         ((this.form.sMarketingPrice - this.form.sTradedPrice) *
    //         (this.form.sContractQty - this.form.sTradedQty)) /
    //       (1 + this.form.sTaxRate) /
    //       10000
    //       ).toFixed(2) || 0
    //     )
    //   }
    // },
    // 敞口期现盈亏（万元）
    hedgingProfitLoss() {
      console.log('🚀 ~ hedgingProfitLoss ~ this.otherData:', this.otherData, Number(this.sOpenSpotProfitLoss), Number(this.otherData.sOptionsOpenPositionProfit), Number(this.otherData.sOpenPositionProfit))

      return (
        Number(this.sOpenSpotProfitLoss) +
        Number(this.otherData.sOptionsOpenPositionProfit) +
        Number(this.otherData.sOpenPositionProfit)
      )
    }
  },
  watch: {
    'formData.sIsOptionInvolved': {
      handler(val) {
        this.$emit('updateOptionInvolved', this.form)
      },
      immediate: true
    }
  },
  created() {
    getDictet([
      'base.yes-no',
      'basis.self.business.type',
      'dev.common.sheet.status',
      'basis.check.state'
    ])
      .then((result) => {
        this.selectOps['base.yes-no'] = result.data[0]?.dicts
        this.selectOps['basis.self.business.type'] = result.data[1]?.dicts
        this.selectOps['dev.common.sheet.status'] = result.data[2]?.dicts
        this.selectOps['basis.check.state'] = result.data[3]?.dicts
      })
      .catch(() => {})
  },
  methods: {
    handleChange(val, key) {
      this.form[key] = val.sId || val.sCode
    },
    // 选择填表日期
    fillInDateChange(val) {
      console.log('🚀 ~ fillInDateChange ~ val:', val, Moment.weekCount(val[0]))
      if (val) {
        this.form.week = `第${Moment.weekCount(val[0])}周`
      }
    },
    handleStaffChange(val) {
      console.log('🚀 ~ handleStaffChange ~ val:', val)
      this.handleChange(val, 'sStaffId')
      if (val) {
        this.form.sDepartmentId = val.orgUnitId
        this.form.vDepartmentName = val.orgUnitName
      }
    }
  }
}
