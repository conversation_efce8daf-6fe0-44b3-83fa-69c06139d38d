<!-- 上期合同新增弹窗 -->
<template>
  <div class="page-container">
    <cnd-dialog
      title="上期新增合同"
      ppend-to-body
      width="80%"
      height="610"
      :fullscreen="false"
      :visible.sync="visible"
      @close="onClose"
    >
      <template slot="content">
        <steel-search-form
          ref="searchForm"
          :form-items="formItems"
          :params-button="false"
          @search="$refs.aggrid.loadTableData()"
        />
        <auto-wrap class="mt-10">
          <steelTradeAggrid
            ref="aggrid"
            :column-defs="columnDefs"
            :row-data="rowData"
            :load-data="loadData"
            table-selection="multiple"
            @selectedChange="handleFooterCount"
          />
        </auto-wrap>
      </template>
      <template slot="footer" class="dialog-footer">
        <el-button size="mini" @click="onClose">{{
          $t('btns.cancel')
        }}</el-button>
        <el-button type="primary" size="mini" @click="handleSubmit">{{
          $t('btns.confirm')
        }}</el-button>
      </template>
    </cnd-dialog>
  </div>
</template>

<script>
import { SteelFormat } from 'cnd-horizon-utils'
import basisSelfCollectionSheet from '@/api/basisSelfCollectionSheet'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import { getCnDitc } from '@/utils/common'
import agreement from '@/api/agreement'
export default {
  name: 'AddPlan',
  components: {
    steelTradeAggrid
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    sHedgingCollectionId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      formItems: [
        {
          label: this.$t('grid.others.contractNumber'),
          value: 'sContractNo',
          type: 'elInput',
          placeholder: this.$t('grid.others.pleaseEnterTheContractNumber')
        }
      ],
      columnDefs: [
        {
          field: 'sBusinessType',
          headerName: '类型',
          cellStyle: { textAlign: 'left' },
          valueFormatter: (params) => {
            return getCnDitc(
              params,
              this.selectOps['basis.self.business.type'],
              'sBusinessType'
            )
          }
        },
        {
          field: 'sContractNo',
          headerName: this.$t('grid.others.contractNumber'), // 合同号
          cellStyle: { textAlign: 'left' }
        },
        {
          field: 'sContractQty',
          headerName: this.$t('grid.others.contractQuantity'), // 合同数量
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value, 3)
          }
        },
        {
          field: 'sTradedQty',
          headerName: '已销售/已采购数量',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value, 3)
          }
        },
        {
          field: 'sExposureQty',
          headerName: '敞口吨数',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.data.sExposureQty, 3)
          }
        },
        {
          field: 'sPositionQty',
          headerName: '期货持吨数',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value, 3)
          }
        },
        {
          field: 'sOpenPositionProfit',
          headerName: '期货持仓盈亏(万元)',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            // vTaxAmt/vNetAmt
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          field: 'vDepartmentName',
          headerName: this.$t('grid.others.department') // 部门
        }
      ],
      // 表头定义 end
      reload: true, // 重载
      rowData: [],
      selectOps: {
        'basis.self.business.type': null
      }
    }
  },
  beforeMount() {
    agreement.getDict(['basis.self.business.type']).then((result) => {
      this.selectOps['basis.self.business.type'] = result.data[0].dicts
    })
  },
  methods: {
    onClose() {
      this.$emit('update:visible', false)
    },
    loadData(pagination = { page: 0, limit: 30 }) {
      return new Promise((resolve, reject) => {
        this.$refs.searchForm.validate().then((valid) => {
          if (valid) {
            basisSelfCollectionSheet
              .getMsbBasisProprietaryHedgingCollectionInfoHistoryPage(
                { ...this.$refs.searchForm.getSearchData(), sIsLast: '1', sHedgingCollectionId: this.sHedgingCollectionId },
                pagination
              )
              .then((res) => {
                this.rowData = res.data.content.map((item) => {
                  item._selected = false
                  item._selectedKeys = []
                  return item
                })
                resolve(res.data)
              })
              .catch(() => {
                reject(0)
              })
          } else {
            resolve(0)
          }
        })
      })
    },
    handleSubmit() {
      const selData = this.rowData.filter((item) => item._selected)
      if (selData.length === 0) {
        this.$message.warning(this.$t('grid.others.pleaseSelectData'))
        return
      }
      selData.forEach((item) => {
        item.sHedgingCollectionId = this.sHedgingCollectionId
      })
      basisSelfCollectionSheet
        .addsMsbBasisProprietaryHedgingCollectionInfo(selData, this.sHedgingCollectionId)
        .then((res) => {
          console.log('🚀 ~ .then ~ res:', res)
          this.$message.success(this.$t('tips.addSuccess'))
          this.onClose()
          this.$emit('refresh')
        })
    }
  }
}
</script>
