<!--
 * @Author: dqr
 * @Date: 2024-07-02 09:12:07
 * @LastEditors: D <NAME_EMAIL>
 * @LastEditTime: 2025-01-13 09:42:39
 * @FilePath: /egl-web/src/views/steelInfoManage/basisSelfCollectionSheet/components/AddContractDialog.vue
 * @Description:
 *
-->
<template>
  <cnd-dialog
    v-if="visible"
    :visible="visible"
    :fullscreen="false"
    append-to-body
    title="期货合约维护"
    width="420px"
    height="180px"
    @close="close"
  >
    <template slot="content">
      <div>
        <el-form
          ref="form"
          :model="form"
          label-width="60px"
          size="small"
          :rules="rules"
          @submit.native.prevent
        >
          <cnd-form-item label="期货合约" :custom-width="22">
            <div class="flex">
              <div class="pr-10">
                <el-select
                  v-model="form.type"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in selectOps['msb.futures.type']"
                    :key="item.sCodeValue"
                    :label="item.sCodeName"
                    :value="item.sCodeValue"
                  />
                </el-select>
              </div>
              <div class="pr-10">
                <el-select
                  v-model="form.year"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in selectOps['futures.year']"
                    :key="item.sCodeValue"
                    :label="item.sCodeName"
                    :value="item.sCodeValue"
                  />
                </el-select>
              </div>
              <div class="pr-10">
                <el-select
                  v-model="form.month"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in selectOps['msb.futures.month']"
                    :key="item.sCodeValue"
                    :label="item.sCodeName"
                    :value="item.sCodeValue"
                  />
                </el-select>
              </div>
            </div>
          </cnd-form-item>
          <cnd-form-item label="开平仓" :custom-width="22">
            <el-select v-model="form.sFuturePosition">
              <el-option
                v-for="item in selectOps['open.close.position']"
                :key="item.sCodeValue"
                :label="item.sCodeName"
                :value="item.sCodeValue"
              />
            </el-select>
          </cnd-form-item>
          <cnd-form-item label="吨数" prop="sQty" :custom-width="22">
            <cnd-input-number v-model="form.sQty" type="number" :decimal-digit="3" />
          </cnd-form-item>
          <cnd-form-item label="价格" prop="sPrice" :custom-width="22">
            <cnd-input-number v-model="form.sPrice" :decimal-digit="2" />
          </cnd-form-item>
          <cnd-form-item label="是否移仓" :custom-width="22">
            <el-select
              v-model="form.sIsAllot"
              placeholder="请选择是否移仓"
            >
              <el-option
                v-for="item in selectOps['base.yes-no']"
                :key="item.sCodeValue"
                :label="item.sCodeName"
                :value="item.sCodeValue"
              />
            </el-select>
          </cnd-form-item>
        </el-form>
      </div>
    </template>
    <template slot="footer">
      <el-button size="mini" @click="close">{{ $t('btns.cancel') }}</el-button>
      <el-button type="primary" size="mini" @click="submit">{{
        $t('btns.confirm')
      }}</el-button>
    </template>
  </cnd-dialog>
</template>

<script>
import agreement from '@/api/agreement'
import basisSelfCollectionSheet from '@/api/basisSelfCollectionSheet'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    sHedgingCollectionContractId: {
      type: String,
      default: ''
    },
    selectInfo: {
      type: Object,
      default: () => {
        return {}
      }
    },
    isMaintain: {
      type: String,
      default: '0'
    }
  },
  data() {
    return {
      form: {
        type: '',
        year: '',
        month: '',
        sFuturePosition: '10',
        sQty: '',
        sPrice: '',
        sIsAllot: '0'
      },
      selectOps: {
        'base.yes-no': null,
        // 期货合约类型
        'msb.futures.type': null,
        'msb.futures.month': null,
        'futures.year': null,
        'open.close.position': null
      },
      rules: {
        sQty: [{ required: true, message: '请输入吨数', trigger: 'change' }],
        sPrice: [{ required: true, message: '请输入价格', trigger: 'change' }]
      },
      apiType: {
        '0': {
          add: 'addMsbBasisProprietaryHedgingCollectionDetailFutures',
          update: 'updateMsbBasisProprietaryHedgingCollectionDetailFutures',
          contractId: 'sHedgingCollectionContractId'
        },
        '1': {
          add: 'addReportContractFutures',
          update: 'modifyReportContractFutures',
          contractId: 'sHedgingCollectionReportContractId'
        }
      }
    }
  },
  watch: {
    selectInfo: {
      handler(val) {
        console.log('🚀 ~ handler ~ val:', val)
        if (val.sId) {
          this.form = {
            ...val,
            type: val.sFutureContractNo.slice(0, 2),
            year: val.sFutureContractNo.slice(2, 4),
            month: val.sFutureContractNo.slice(4, 6)
          }
        }
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    agreement.getDict(['base.yes-no', 'msb.futures.type', 'msb.futures.month', 'open.close.position'])
      .then((result) => {
        this.selectOps['base.yes-no'] = result.data[0]?.dicts
        this.selectOps['msb.futures.type'] = result.data[1]?.dicts
        this.selectOps['msb.futures.month'] = result.data[2]?.dicts
        this.selectOps['open.close.position'] = result.data[3]?.dicts
      })
      .catch(() => {})
    this.getFuturesYear()
  },
  methods: {
    getFuturesYear() {
      let year = new Date().getFullYear()
      const month = new Date().getMonth() + 1
      if (month > 10) {
        year = year + 1
      }
      const yearList = []
      for (let i = 0; i < 2; i++) {
        yearList.push({
          sCodeValue: `${year + i}`.toString().slice(-2),
          sCodeName: `${year + i}`.toString().slice(-2)
        })
      }
      this.selectOps['futures.year'] = yearList
    },
    close() {
      this.$emit('update:visible', false)
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const apiType = this.apiType[this.isMaintain]
          const params = {
            ...this.form,
            sFutureContractNo: `${this.form.type}${this.form.year}${this.form.month}`
          }
          params[apiType.contractId] = this.sHedgingCollectionContractId
          if (params.sId) {
            basisSelfCollectionSheet[apiType.update](params).then((res) => {
              this.$message.success('修改成功')
              this.$emit('update:visible', false)
              this.$emit('refresh')
            })
            return
          }
          basisSelfCollectionSheet[apiType.add](params).then((res) => {
            this.$message.success('新增成功')
            this.$emit('update:visible', false)
            this.$emit('refresh')
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
