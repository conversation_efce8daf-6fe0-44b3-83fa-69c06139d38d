<!-- 收集单合同详情-期货信息 -->
<template>
  <div class="h100">
    <div class="btn-group">
      <div class="text">期权信息</div>
      <div v-if="showBtn()">
        <el-button v-has:basis_self_collection_sheet_share_option_add type="primary" size="mini" @click="addShareOptionDialogVisible = true">{{
          $t('btns.add')
        }}</el-button>
        <el-button
          v-has:basis_self_collection_sheet_share_option_edit
          type="primary"
          :disabled="!curSelRowData || curSelRowData.length !== 1"
          size="mini"
          @click="addShareOptionDialogVisible = true"
        >{{ $t('btns.edit') }}</el-button>
        <el-button
          v-has:basis_self_collection_sheet_share_option_delete
          type="danger"
          size="mini"
          :disabled="!curSelRowData || !curSelRowData.length"
          @click="onDelete"
        >{{ $t('btns.delete') }}</el-button>
      </div>
    </div>
    <el-row class="share-list-height">
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :auto-load-data="false"
        heightinif="100%"
        :row-data="rowData"
        :paginationinif="false"
        table-selection="multiple"
        @selectedChange="gridSelectedChange"
      />
    </el-row>
    <AddShareOptionDialog
      v-if="addShareOptionDialogVisible"
      :visible.sync="addShareOptionDialogVisible"
      :s-hedging-collection-contract-id="sHedgingCollectionContractId"
      :select-info="curSelRowData[0] || {}"
      @refresh="refresh"
    />
  </div>
</template>/salesdeliveryList/

<script>
import { SteelFormat } from 'cnd-horizon-utils'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import basisSelfCollectionSheet from '@/api/basisSelfCollectionSheet'
import AddShareOptionDialog from './AddShareOptionDialog'
export default {
  components: {
    steelTradeAggrid,
    AddShareOptionDialog
  },
  props: {
    formData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    sHedgingCollectionContractId: {
      type: String,
      default: ''
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Array,
      default: () => {
        return []
      }
    },
    sCollectionStatus: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      columnDefs: [
        {
          headerName: '建仓吨数',
          field: 'sOptionSize',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.data.sOptionSize, 3)
          }
        },
        {
          headerName: '期权金/每吨',
          field: 'sOptionPremium',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: '已平仓数量',
          field: 'sClosedPositionQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.data.sClosedPositionQty, 3)
          }
        },
        {
          headerName: '平仓盈亏(万元)',
          field: 'sClosedPositionProfit',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: '持仓数量',
          field: 'sPositionQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.data.sPositionQty, 3)
          }
        },
        {
          headerName: '持仓盈亏(万元)',
          field: 'sOpenPositionProfit',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        // 备注
        {
          headerName: '备注',
          field: 'sRemark'
        }
      ],
      addShareOptionDialogVisible: false,
      curSelRowData: [],
      info: {}
    }
  },
  methods: {
    showBtn() {
      return this.isEdit && ((this.sCollectionStatus && (this.sCollectionStatus !== '40')) || !this.sCollectionStatus)
    },
    gridSelectedChange(rowData) {
      console.log('🚀 ~ gridSelectedChange ~ rowData:', rowData)
      this.curSelRowData = rowData.filter((item) => item._selected)
    },
    loadData() {
      return new Promise((resolve, reject) => {
        basisSelfCollectionSheet
          .getReportContractInfoOptions(this.sHedgingCollectionContractId)
          .then((res) => {
            this.info = res.data
            // this.rowData = res.data.map((item) => {
            //   item._selected = false
            //   item._selectedKeys = []
            //   return item
            // })
            resolve(res.data)
          })
          .catch(() => {
            reject(0)
          })
      })
    },
    onDelete() {
      if (!this.curSelRowData.length) {
        this.$message({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      this.$confirm('是否删除该条数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const ids = this.curSelRowData.map((item) => item.sId)
          basisSelfCollectionSheet.removesReportContractOptions(ids).then(() => {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.refresh()
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    refresh() {
      this.loadData()
      this.$emit('refresh')
    }
  }
}
</script>

<style scoped>
.share-list-height {
  height: calc(100% - 39px);
}
</style>
