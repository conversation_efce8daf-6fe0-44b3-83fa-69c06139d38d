<!--
 * @Author: dqr
 * @Date: 2024-07-02 09:11:54
 * @LastEditors: D <NAME_EMAIL>
 * @LastEditTime: 2024-09-23 17:49:38
 * @FilePath: /egl-web/src/views/steelInfoManage/basisSelfCollectionSheet/components/BasisSelfCollectionSheetBasicDetail.vue
 * @Description:
 *
-->
<!-- 基差自营收集单-基础信息 -->
<template>
  <div>
    <cnd-btn-position top="6" right="10">
      <el-button
        v-has:basis_self_collection_sheet_detail_save
        type="primary"
        size="mini"
        :disabled="
          (form.sId && isBusinessDisabled('save', form.sSheetStatus)) ||
            $route.query.disabled === '1'
        "
        @click="save"
      >{{ $t('btns.save') }}</el-button>
    </cnd-btn-position>
    <!-- disabled: true -->
    <el-form
      ref="form"
      class="el-form-w100"
      label-width="100px"
      :inline="true"
      :model="form"
      :rules="rules"
      size="small"
      :disabled="form.sId ? isBusinessDisabled('submit', form.sSheetStatus) : false"
    >
      <cnd-form-card-list :active-panel="activeCollapseName">
        <!-- 基础信息 -->
        <cnd-form-card :title="$t('grid.tabs.basicInformation')" name="1">
          <el-row>
            <cnd-form-item :label="$t('grid.others.code')" prop="sCode">
              <el-input v-model="form.sCode" disabled />
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.others.title')" prop="sTitle">
              <el-input v-model="form.sTitle" clearable />
            </cnd-form-item>
            <cnd-form-item label="填表日期" prop="sCollectionDate">
              <el-date-picker
                v-model="form.sCollectionDate"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                format="yyyy-MM-dd"
                @change="fillInDateChange"
              />
            </cnd-form-item>
            <cnd-form-item label="周度" prop="week">
              <el-input disabled :value="form.week" />
            </cnd-form-item>
            <cnd-form-item
              :label="$t('grid.title.personnel')"
              prop="vStaffName"
            >
              <horizon-search-select
                v-model="form.vStaffName"
                default-url="/esc/staff/dialog/org/v2/page/"
                search-key="sName"
                :option="{label: 'sPath', value: 'id'}"
                @change="handleStaffChange"
              />
            </cnd-form-item>
            <cnd-form-item
              :label="$t('grid.others.department')"
              prop="vDepartmentName"
            >
              <horizon-search-select
                v-model="form.vDepartmentName"
                disabled
                type="depart"
                @change="handleChange($event, 'sDepartmentId')"
              />
            </cnd-form-item>
            <cnd-form-item
              :label="$t('grid.title.company')"
              prop="vCompanyName"
            >
              <horizon-search-select
                v-model="form.vCompanyName"
                type="company"
                @change="handleChange($event, 'sCompanyId')"
              />
            </cnd-form-item>
            <cnd-form-item
              :label="$t('grid.title.accountingGroup')"
              prop="vCheckGroupName"
            >
              <horizon-search-select
                v-model="form.vCheckGroupName"
                type="cost"
                @change="handleChange($event, 'sCheckGroupId')"
              />
            </cnd-form-item>
            <cnd-form-item label="收集状态">
              <el-select v-model="form.sCollectionStatus" disabled>
                <el-option
                  v-for="item in selectOps['collection.state']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.title.remarks')" prop="sRemark">
              <el-input v-model="form.sRemark" clearable />
            </cnd-form-item>
          </el-row>
        </cnd-form-card>
        <!-- 系统信息 -->
        <cnd-form-card :title="$t('grid.tabs.systemInformation')" name="2">
          <el-row>
            <cnd-form-item
              :label="$t('grid.title.createdBy')"
              prop="vCreatorName"
            >
              <el-input
                v-model="form.vCreatorName"
                :disabled="true"
                clearable
              />
            </cnd-form-item>
            <cnd-form-item
              :label="$t('grid.title.createdAt')"
              prop="sCreateTime"
            >
              <el-date-picker
                v-model="form.sCreateTime"
                type="date"
                format="yyyy-MM-dd HH:mm:ss"
                disabled
                clearable
              />
            </cnd-form-item>
            <cnd-form-item
              :label="$t('grid.title.modifiedBy')"
              prop="vModifierName"
            >
              <el-input v-model="form.vModifierName" disabled clearable />
            </cnd-form-item>
            <cnd-form-item
              :label="$t('grid.title.modifiedAt')"
              prop="sModifyTime"
            >
              <el-date-picker
                v-model="form.sModifyTime"
                type="date"
                format="yyyy-MM-dd HH:mm:ss"
                disabled
                clearable
              />
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.title.status')">
              <el-select v-model="form.sSheetStatus" disabled>
                <el-option
                  v-for="item in selectOps['dev.common.sheet.status']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item
              :label="$t('grid.others.effectiveTime')"
              prop="sRatifyDate"
            >
              <el-date-picker
                v-model="form.sRatifyDate"
                type="date"
                format="yyyy-MM-dd HH:mm:ss"
                disabled
                clearable
              />
            </cnd-form-item>
          </el-row>
        </cnd-form-card>
      </cnd-form-card-list>
    </el-form>
  </div>
</template>

<script>
import { getDictet } from '@/api/logistics/saleDelivery/saleorder'
import basisSelfCollectionSheet from '@/api/basisSelfCollectionSheet'
import businessMixin from '@/utils/businessMixin'
import { Moment } from 'cnd-utils'
import { getCostPage, getCorpPage } from '@/api/common'
export default {
  name: 'DomesticProcurementBasicDetail',
  mixins: [businessMixin],
  props: {
    formData: {
      type: Object,
      default: null
    },
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      activeCollapseName: ['1', '2'],
      selectOps: {
        'dev.common.sheet.status': null,
        'collection.state': null
      },
      rules: {
        sTitle: [{ required: true, message: '请输入标题', trigger: 'blur' }],
        // 填表日期
        sCollectionDate: [
          { required: true, message: '请选择填表日期', trigger: 'change' }
        ],
        // 人员
        vStaffName: [
          { required: true, message: '请选择人员', trigger: 'change' }
        ],
        // 公司
        vCompanyName: [
          { required: true, message: '请选择公司', trigger: 'change' }
        ],
        // 核算组
        vCheckGroupName: [
          { required: true, message: '请选择核算组', trigger: 'change' }
        ]
      },
      form: {
        sCode: '',
        sTitle: '基差自营敞口收集表',
        sCollectionStartDate: '',
        sCollectionEndDate: '',
        sCollectionDate: [],
        sCollectionWeek: '',
        week: '',
        sStaffId: '',
        vStaffName: '',
        sDepartmentId: '',
        vDepartmentName: '',
        sCompanyId: '',
        vCompanyName: '',
        sCheckGroupId: '',
        vCheckGroupName: ''
      },
      oldForm: '',
      selectId: this.$route.query.Id
    }
  },
  watch: {
    formData: {
      immediate: true,
      handler(newVal) {
        if (!newVal.sId && !this.selectId) {
          this.getCorpPage()
          this.getCostPage()
          return
        }
        const data = { ...this.formData }
        if (this.formData.sCollectionStartDate) {
          data.sCollectionDate = [
            new Date(this.formData.sCollectionStartDate),
            new Date(this.formData.sCollectionEndDate)
          ]
          data.week = `第${this.formData.sCollectionWeek}周`
        }
        this.form = data
        this.oldForm = JSON.stringify(this.form)
      }
    }
  },
  created() {
    getDictet(['dev.common.sheet.status', 'collection.state'])
      .then((result) => {
        this.selectOps['dev.common.sheet.status'] = result.data[0]?.dicts
        this.selectOps['collection.state'] = result.data[1]?.dicts
      })
      .catch(() => {})
  },
  methods: {
    handleChange(val, key) {
      val && (this.form[key] = val.sId || val.sCode)
    },
    handleStaffChange(val) {
      console.log('🚀 ~ handleStaffChange ~ val:', val)
      this.handleChange(val, 'sStaffId')
      if (val) {
        this.form.sDepartmentId = val.orgUnitId
        this.form.vDepartmentName = val.orgUnitName
      }
    },
    // 选择填表日期
    fillInDateChange(val) {
      console.log('🚀 ~ fillInDateChange ~ val:', val)
      if (val) {
        this.form.week = `第${Moment.weekCount(val[0])}周`
        this.form.sCollectionWeek = Moment.weekCount(val[0])
      }
    },
    saveForm(fn) {
      this.$refs.form.validate((valid) => {
        const str = JSON.stringify(this.form)

        if (valid) {
          fn && fn(true, str !== this.oldForm)
        } else {
          fn && fn(false)
          return false
        }
      })
    },
    save(isSubmit) {
      return new Promise((resolve, reject) => {
        this.$refs.form.validate((valid) => {
          if (valid) {
            const params = {
              ...this.form,
              sCollectionStartDate: this.form.sCollectionDate && this.form.sCollectionDate[0],
              sCollectionEndDate: this.form.sCollectionDate && this.form.sCollectionDate[1]
            }
            if (isSubmit === '1') {
              basisSelfCollectionSheet.submitMsbBasisProprietaryHedgingCollection(params)
                .then((res) => {
                  this.$message.success('提交成功')
                  this.$tabDelete()
                  resolve(true)
                })
                .catch(() => {
                  reject(false)
                })
              return
            }
            if (this.form.sId) {
              basisSelfCollectionSheet
                .modifyMsbBasisProprietaryHedgingCollection(params)
                .then((res) => {
                  this.$message.success('保存成功')
                  this.$emit('success', res.data)
                  resolve(true)
                })
                .catch(() => {
                  reject(false)
                })
            } else {
              basisSelfCollectionSheet
                .addMsbBasisProprietaryHedgingCollection(params)
                .then((res) => {
                  this.$message.success('保存成功')
                  this.$emit('success', res.data)
                  resolve(true)
                })
                .catch(() => {
                  reject(false)
                })
            }
          } else {
            reject(false)
          }
        })
      })
    },
    getCorpPage() {
      getCorpPage({ pageNum: 0, pageSize: 20 }).then(res => {
        const data = res.data.content
        if (data.length === 1) {
          this.form.sCompanyId = data[0].sId
          this.form.vCompanyName = data[0].sName
        }
      })
    },
    getCostPage() {
      getCostPage({ pageNum: 0, pageSize: 20 }).then(res => {
        const data = res.data.content
        if (data.length === 1) {
          this.form.sCheckGroupId = data[0].sId
          this.form.vCheckGroupName = data[0].sName
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.el-range-editor--small.el-input__inner {
  height: 26px;
}
::v-deep.form-item-container {
  .el-form-item .el-form-item__content {
    line-height: 24px !important;
  }
}
::v-deep.el-range-editor--small .el-range__icon {
  line-height: 20px;
}
</style>
