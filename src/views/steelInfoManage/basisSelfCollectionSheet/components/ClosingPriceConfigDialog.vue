<!-- 收盘价配置弹窗 -->
<template>
  <cnd-dialog
    v-if="visible"
    :visible="visible"
    :fullscreen="false"
    append-to-body
    title="收盘价配置"
    width="550px"
    height="300px"
    @close="close"
  >
    <template slot="content">
      <div class="flexCB mb-10">
        <div>数据获取时间：{{ newestGainDate }}</div>
        <el-button
          type="primary"
          size="mini"
          @click="gainNewestClosingPrice"
        >获取</el-button>
      </div>
      <div>
        <steelTradeAggrid
          ref="aggrid"
          :column-defs="columnDefs"
          :row-data="rowData"
          :auto-load-data="false"
          :heightinif="300"
          :paginationinif="false"
        />
      </div>
    </template>
    <template slot="footer">
      <el-button size="mini" @click="close">{{ $t('btns.cancel') }}</el-button>
      <el-button type="primary" size="mini" @click="submit">{{
        $t('btns.confirmKey')
      }}</el-button>
    </template>
  </cnd-dialog>
</template>

<script>
import steelTradeAggrid from '@/components/steelTradeAggrid'
import moment from 'moment'
import { Middleware } from 'cndinfo-ui'
import basisSelfCollectionSheet from '@/api/basisSelfCollectionSheet'
import Vue from 'vue'
export default {
  components: {
    steelTradeAggrid
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    basicData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      columnDefs: [
        {
          headerName: '收盘价(人民币)',
          field: 'price',
          minWidth: 200,
          cellStyle: { textAlign: 'right' },
          editable: true,
          cellEditorFramework: Vue.extend(
            Middleware.createComponent(
              'CndInputNumber',
              {
                mark: 'price',
                type: 'number',
                decimalDigit: 2
              },
              {
                blur: ({ event, rowData, middleware }) => {
                  this.rowData[rowData.node.childIndex].price = middleware.value
                }
              }
            )
          )
        },
        {
          headerName: '合约',
          field: 'code',
          width: 150
        },
        {
          headerName: '名称',
          field: 'name',
          width: 150
        }
      ],
      rowData: [],
      // 最新获取时间
      newestGainDate: ''
    }
  },
  created() {
    this.getMsbBasisProprietaryHedgingCollectionInfoContract()
  },
  methods: {
    close() {
      this.$emit('update:visible', false)
    },
    submit() {
      console.log('--------', this.rowData)
      const params = {
        sHedgingCollectionId: this.basicData.sId,
        configVoList: this.rowData
      }
      basisSelfCollectionSheet.confirmMsbBasisProprietaryHedgingCollectionInfo(params).then(() => {
        this.$emit('refresh')
        this.$emit('update:visible', false)
      })
    },
    // 获取最新收盘价
    gainNewestClosingPrice() {
      basisSelfCollectionSheet
        .getMsbBasisProprietaryHedgingCollectionInfoClosingPrice()
        .then((res) => {
          this.rowData = res.data || []
          this.newestGainDate = moment(new Date()).format('YYYY-MM-DD')
        })
    },
    // 获取获取合约
    getMsbBasisProprietaryHedgingCollectionInfoContract() {
      basisSelfCollectionSheet
        .getMsbBasisProprietaryHedgingCollectionInfoContract(this.basicData.sId)
        .then((res) => {
          this.rowData = res.data || []
          this.newestGainDate = moment(new Date()).format('YYYY-MM-DD')
        })
    }
  }
}
</script>

<style lang="scss" scoped></style>
