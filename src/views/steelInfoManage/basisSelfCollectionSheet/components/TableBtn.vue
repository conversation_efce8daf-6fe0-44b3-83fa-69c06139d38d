<template>
  <div class="button-wrap">
    <el-button class="text-underline" type="text" @click.stop="btnRow">{{
      btnText
    }}</el-button>
  </div>
</template>

<script>
export default {
  name: 'InlineButton',
  data() {
    return {
      btnText: '确定选择',
      fileId: '',
      type: ''
    }
  },
  created() { },
  mounted() { },
  beforeMount() {
    this.cellParams = this.params.colDef.cellParams ? this.params.colDef.cellParams() : this.params.cellParams()
    const { btnText, fileId, type } = this.cellParams
    this.fileId = fileId
    if (btnText) {
      this.btnText = btnText
    } else if (fileId) {
      this.btnText = this.params.data[fileId]
    }
    this.type = type
  },
  methods: {
    btnRow() {
      if (this.params.context.componentParent.listCellBtnSelect) {
        this.params.context.componentParent.listCellBtnSelect(this.params.data, this.fileId, this.type)
      } else {
        this.params.sendEvent(this.params.data, this.fileId, this.type)
      }
    }
  }
}
</script>
