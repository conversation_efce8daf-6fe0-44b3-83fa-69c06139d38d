<template>
  <cnd-dialog
    v-if="visible"
    :visible="visible"
    :fullscreen="false"
    append-to-body
    title="期权合约维护"
    width="420px"
    height="250px"
    @close="close"
  >
    <template slot="content">
      <div>
        <el-form
          ref="form"
          :model="form"
          label-width="100px"
          size="small"
          :rules="rules"
          @submit.native.prevent
        >
          <cnd-form-item label="建仓数量" prop="sOptionSize" :custom-width="22">
            <cnd-input-number v-model="form.sOptionSize" type="number" :decimal-digit="3" />
          </cnd-form-item>
          <cnd-form-item label="权利金/每吨" prop="sOptionPremium" :custom-width="22">
            <cnd-input-number v-model="form.sOptionPremium" />
          </cnd-form-item>
          <cnd-form-item label="已平仓数量" prop="sClosedPositionQty" :custom-width="22">
            <cnd-input-number v-model="form.sClosedPositionQty" type="number" :decimal-digit="3" />
          </cnd-form-item>
          <cnd-form-item label="已平仓盈亏（万元）" prop="sClosedPositionProfit" :custom-width="22">
            <cnd-input-number v-model="form.sClosedPositionProfit" />
          </cnd-form-item>
          <cnd-form-item label="持仓数量" prop="sPositionQty" :custom-width="22">
            <cnd-input-number v-model="form.sPositionQty" type="number" :decimal-digit="3" />
          </cnd-form-item>
          <cnd-form-item label="持仓盈亏（万元）" prop="sOpenPositionProfit" :custom-width="22">
            <cnd-input-number v-model="form.sOpenPositionProfit" />
          </cnd-form-item>
          <cnd-form-item label="备注" prop="sRemark" :custom-width="22">
            <el-input v-model="form.sRemark" />
          </cnd-form-item>
        </el-form>
      </div>
    </template>
    <template slot="footer">
      <el-button size="mini" @click="close">{{ $t('btns.cancel') }}</el-button>
      <el-button type="primary" size="mini" @click="submit">{{
        $t('btns.confirm')
      }}</el-button>
    </template>
  </cnd-dialog>
</template>

<script>
import basisSelfCollectionSheet from '@/api/basisSelfCollectionSheet'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    sHedgingCollectionContractId: {
      type: String,
      default: ''
    },
    selectInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      form: {},
      rules: {
        sOptionSize: [{ required: true, message: '请输入吨数', trigger: 'change' }],
        sOptionPremium: [{ required: true, message: '请输入权利金', trigger: 'change' }],
        sClosedPositionQty: [{ required: true, message: '请输入已平仓数量', trigger: 'change' }],
        sClosedPositionProfit: [{ required: true, message: '请输入已平仓盈亏', trigger: 'change' }],
        sPositionQty: [{ required: true, message: '请输入持仓数量', trigger: 'change' }],
        sOpenPositionProfit: [{ required: true, message: '请输入持仓盈亏', trigger: 'change' }]
      }
    }
  },
  watch: {
    selectInfo: {
      handler(val) {
        if (val.sId) {
          this.form = {
            ...val
          }
        }
      },
      deep: true,
      immediate: true
    }
  },
  created() {
  },
  methods: {
    close() {
      this.$emit('update:visible', false)
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const params = {
            ...this.form,
            sHedgingCollectionReportContractId: this.sHedgingCollectionContractId
          }

          if (params.sId) {
            basisSelfCollectionSheet.modifyReportContractOptions(params).then((res) => {
              this.$message.success('修改成功')
              this.$emit('update:visible', false)
              this.$emit('refresh')
            })
            return
          }
          basisSelfCollectionSheet.addReportContractOptions(params).then((res) => {
            this.$message.success('新增成功')
            this.$emit('update:visible', false)
            this.$emit('refresh')
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
