/*
基差自营收集单-合同信息
 */
import { SteelFormat } from 'cnd-horizon-utils'
import basisSelfCollectionSheet from '@/api/basisSelfCollectionSheet'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import { getCnDitc } from '@/utils/common'
import agreement from '@/api/agreement'
import TableBtn from './TableBtn'
import ClosingPriceConfigDialog from './ClosingPriceConfigDialog'
import AddPriorDialog from './AddPriorDialog'
export default {
  name: 'BasisSelfCollectionSheetContractList',
  components: {
    steelTradeAggrid,
    TableBtn,
    ClosingPriceConfigDialog,
    AddPriorDialog
  },
  props: {
    basicData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    isRead() {
      return (this.basicData.sSheetStatus !== '10' && this.basicData.sSheetStatus !== '15') && this.basicData.sId
    }
  },
  data() {
    return {
      selectOps: {
        'basis.self.contract.type': [],
        'basis.self.business.type': [],
        'base.yes-no': []
      },
      searchInfo: null,
      formItems: [
        {
          // 是否完结
          label: '是否完结',
          value: 'sCollectionStatus',
          type: 'elSelect',
          dict: 'base.yes-no',
          // 请选择单据状态
          placeholder: '请选择'
        },
        {
          label: '业务类型',
          value: 'sBusinessType',
          type: 'elSelect',
          dict: 'basis.self.business.type',
          placeholder: '请选择收集状态'
        },
        {
          // 合同号
          label: this.$t('grid.others.contractNumber'),
          value: 'sContractNo',
          type: 'elInput'
        },
        {
          // 项目号
          label: this.$t('grid.others.itemNumberTag'),
          value: 'sProjectNo',
          type: 'elInput'
        },
        {
          label: this.$t('grid.title.personnel'),
          value: 'sStaffId',
          type: 'cndInputDialogItem',
          option: { valueKey: 'sPath' },
          dialogType: 'staff',
          placeholder: this.$t('grid.others.pleaseSelectPersonnel')
        },
        {
          label: this.$t('grid.others.department'),
          value: 'sDepartmentId',
          type: 'cndInputDialog',
          dialogType: 'depart',
          placeholder: this.$t('grid.others.pleaseSelectTheDepartment')
        }
        // {
        //   // 公司
        //   label: this.$t('grid.title.company'),
        //   value: 'sCompanyId',
        //   type: 'cndInputDialog',
        //   dialogType: 'company',
        //   // 请选择公司
        //   placeholder: this.$t('grid.others.pleaseSelectCompany')
        // },
        // // 核算组
        // {
        //   label: this.$t('grid.title.accountingGroup'),
        //   value: 'sCheckGroupId',
        //   type: 'cndInputDialog',
        //   dialogType: 'cost',
        //   placeholder: '请选择核算组'
        // }
      ],
      columnDefs: [
        {
          field: 'sBusinessType',
          headerName: '业务类型',
          cellStyle: { textAlign: 'left' },
          valueFormatter: (params) => {
            return getCnDitc(
              params,
              this.selectOps['basis.self.business.type'],
              'sBusinessType'
            )
          }
        },
        // 人员
        {
          field: 'vStaffName',
          headerName: this.$t('grid.title.personnel'),
          cellStyle: { textAlign: 'left' }
        },
        // 项目号
        {
          field: 'sProjectNo',
          headerName: this.$t('grid.others.itemNumberTag'),
          cellStyle: { textAlign: 'left' }
        },
        // 合同号
        {
          field: 'sContractNo',
          headerName: this.$t('grid.others.contractNumber'),
          cellStyle: { textAlign: 'left' }
        },
        // 合同类型
        {
          field: 'sContractType',
          headerName: this.$t('grid.others.contractType'),
          cellStyle: { textAlign: 'left' },
          valueFormatter: (params) => {
            return getCnDitc(
              params,
              this.selectOps['basis.self.contract.type'],
              'sContractType'
            )
          }
        },
        // 合同数量
        {
          field: 'sContractQty',
          headerName: this.$t('grid.others.contractQuantity'),
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.data.sContractQty, 3)
          }
        },
        {
          field: 'sTradedQty',
          headerName: '已销售/已采购数量',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.data.sTradedQty, 3)
          }
        },
        {
          field: 'sPurchaseCost',
          headerName: '采购成本',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return params.value && SteelFormat.formatPrice(params.value)
          }
        },
        {
          field: 'sSalePrice',
          headerName: '销售价格',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return params.value && SteelFormat.formatPrice(params.value)
          }
        },
        {
          field: 'sExposureQty',
          headerName: '敞口吨数',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.data.sExposureQty, 3)
          }
        },
        {
          field: 'sPositionQty',
          headerName: '期货持仓吨数',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.data.sPositionQty, 3)
          }
        },
        {
          field: 'sOpenPositionProfit',
          headerName: '期货持仓盈亏(万元)',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          field: 'sGoodsCategoryDesc',
          headerName: '钢厂品种资源',
          cellStyle: { textAlign: 'left' }
        },
        {
          field: 'sCollectionStatus',
          headerName: '是否完结',
          cellStyle: { textAlign: 'left' },
          valueFormatter: (params) => {
            return getCnDitc(
              params,
              this.selectOps['base.yes-no'],
              'sCollectionStatus'
            )
          }
        },
        {
          field: 'sCollectionCompleteRemark',
          headerName: '完结原因',
          cellStyle: { textAlign: 'left' }
        },
        {
          field: 'vDepartmentName',
          headerName: this.$t('grid.others.department'),
          cellStyle: { textAlign: 'left' }
        }
        // // 公司
        // {
        //   field: 'vCompanyName',
        //   headerName: this.$t('grid.title.company'),
        //   cellStyle: { textAlign: 'left' }
        // },
        // // 核算组
        // {
        //   field: 'vCheckGroupName',
        //   headerName: this.$t('grid.title.accountingGroup'),
        //   cellStyle: { textAlign: 'left' }
        // }
      ],
      headerCount: null,
      footerCount: null,
      // 表头定义 end
      reload: true, // 重载
      rowData: [],
      totalRowData: '',
      // 弹窗控制值
      dialogVisible: {
        detail: false // 详情信息
      },
      detail: null,
      rowId: '',
      rowSCode: '',
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      // 收盘价配置弹窗
      closingPriceConfigDialogVisible: false,
      // 添加上期新增弹窗
      addPriorVisible: false,
      // 当前选中的数据
      curSelRowData: null,
      defaultPageSize: 200
    }
  },
  beforeMount() {
    agreement
      .getDict([
        'basis.self.contract.type',
        'base.yes-no',
        'basis.self.business.type'
      ])
      .then((result) => {
        this.selectOps['basis.self.contract.type'] = result.data[0].dicts
        this.selectOps['base.yes-no'] = result.data[1].dicts
        this.selectOps['basis.self.business.type'] = result.data[2].dicts
      })
  },
  watch: {
    basicData: {
      handler: function(val) {
        if (val.sId) {
          this.$nextTick(() => {
            this.onSearch()
          })
        }
      },
      immediate: true
    }
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm?.getSearchData()
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    setCount(vCount = 0, vSumQty = 0, vSumAmt = 0, flag) {
      this[flag] = [
        { count: vCount, key: 'count' },
        {
          title: this.$t('grid.title.quantity'),
          count: SteelFormat.formatThousandthSign(vSumQty, 4),
          unit: this.$t('grid.others.ton')
        },
        {
          title: this.$t('grid.title.amount'),
          count: SteelFormat.formatPrice(vSumAmt, 3),
          unit: this.$t('grid.others.yuan')
        }
      ]
    },
    handleFooterCount(rowData) {
      const details = rowData.filter((item) => item._selected)
      const vCount = details.length
      let count = 0
      let m = 0
      this.rowData.map((item) => {
        if (item._selected) {
          count += Number(item.vSumQty || 0)
          m += Number(item.sOriginalAmt || 0)
        }
      })
      this.setCount(vCount, count, m, 'footerCount')
    },
    loadData(pagination = { page: 0, limit: 30 }) {
      return new Promise((resolve, reject) => {
        basisSelfCollectionSheet
          .getMsbBasisProprietaryHedgingCollectionInfoPage(
            {
              ...this.$refs.searchForm?.getSearchData(),
              sHedgingCollectionId: this.basicData.sId,
              sIsLast: '0'
            },
            pagination
          )
          .then((res) => {
            const { vCount } = res.data
            this.rowData = res.data.content.map((item) => {
              item._selected = false
              item._selectedKeys = []
              return item
            })
            this.setCount(
              vCount,
              res.data.vSumQty,
              res.data.vSumAmt,
              'headerCount'
            )
            resolve(res.data)
          })
          .catch(() => {
            reject(0)
          })
      })
    },
    onClose() {
      this.dialogVisible.detail = false
      this.$refs.aggrid.loadTableData()
    },
    // 获取基本信息
    async getDetail(sId) {
      return await basisSelfCollectionSheet
        .getMsbBasisProprietaryHedgingCollectionInfo(sId)
    },
    async rowDoubleClicked(d) {
      const res = await this.getDetail(d.data.sId)
      if (!res.data) return
      this.$router.push({
        path: `/basisSelfCollectionSheetContractDetail/${d.data.sId}`,
        query: {
          Id: d.data.sId,
          status: this.basicData.sSheetStatus,
          type: 'edit',
          name: '合同详情' + `【${d.data.sContractNo}】`,
          activeId: localStorage.getItem('menuId'),
          sContractType: d.data.sContractType,
          fromPath: window.location.pathname + window.location.search,
          sHedgingCollectionId: this.basicData.sId
        }
      })
    },
    openDialog(prop) {
      this[prop] = true
    },
    // 剔除完结合同
    eliminateFinishContract() {
      this.$confirm('是否确认剔除完结合同？', this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'info'
      }).then(() => {
        basisSelfCollectionSheet.deleteMsbBasisProprietaryHedgingCollectionInfo(this.basicData.sId).then((res) => {
          this.$message.success('操作成功')
          this.$refs.aggrid.loadTableData()
        })
      })
    },
    onDel() {
      this.curSelRowData = this.rowData.filter((item) => item._selected)
      if (!this.curSelRowData || this.curSelRowData.length === 0) {
        this.$message.warning(this.$t('grid.others.pleaseCheckTheData'))
        return
      }
      this.$confirm('是否确认删除？', this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'info'
      }).then(() => {
        const ids = this.curSelRowData.map((item) => item.sId)
        basisSelfCollectionSheet.removesMsbBasisProprietaryHedgingCollectionInfo(ids).then((res) => {
          this.$message.success('操作成功')
          this.$refs.aggrid.loadTableData()
        })
      })
    },
    addN8Contract() {
      this.$confirm('相同合同将覆盖，是否确认添加N8合同信息？', this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'info'
      }).then(() => {
        basisSelfCollectionSheet.addErpMsbBasisProprietaryHedgingCollectionInfo(this.basicData.sId).then((res) => {
          this.$message.success('操作成功')
          this.$refs.aggrid.loadTableData()
        })
      })
    },
    async openContractDetail() {
      this.curSelRowData = this.rowData.filter((item) => item._selected)
      if (this.curSelRowData.length === 0 || !this.curSelRowData) return
      const res = await this.getDetail(this.curSelRowData[0]?.sId)
      if (!res.data) return
      this.$router.push({
        path: `/basisSelfCollectionSheetContractDetail/${this.curSelRowData[0]?.sId}`,
        query: {
          Id: this.curSelRowData[0]?.sId,
          type: 'edit',
          name: '合同详情' + `【${this.curSelRowData[0]?.sContractNo}】`,
          status: this.basicData.sSheetStatus,
          activeId: localStorage.getItem('menuId'),
          sContractType: this.curSelRowData[0]?.sContractType,
          fromPath: window.location.pathname + window.location.search,
          sHedgingCollectionId: this.basicData.sId
        }
      })
    },
    addContractDetail(sContractType) {
      this.$router.push({
        path: `/basisSelfCollectionSheetContractDetail/add`,
        query: {
          Id: '',
          type: 'add',
          name: '新增合同',
          status: this.basicData.sSheetStatus,
          activeId: localStorage.getItem('menuId'),
          sContractType,
          sHedgingCollectionId: this.basicData.sId,
          fromPath: window.location.pathname + window.location.search
        }
      })
    }
  }
}
