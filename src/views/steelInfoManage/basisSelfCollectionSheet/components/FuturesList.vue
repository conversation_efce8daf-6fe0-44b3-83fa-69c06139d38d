<!-- 收集单合同详情-期货信息 -->
<template>
  <div>
    <div class="btn-group">
      <div class="text">期货信息</div>
    </div>
    <el-row>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :auto-load-data="false"
        :heightinif="200"
        :paginationinif="false"
      />
    </el-row>
  </div>

</template>

<script>
import { SteelFormat } from 'cnd-horizon-utils'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import basisSelfCollectionSheet from '@/api/basisSelfCollectionSheet'
export default {
  components: {
    steelTradeAggrid
  },
  props: {
    formData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    rowData: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      columnDefs: [
        {
          headerName: '期货合约',
          field: 'sFutureContractNo'
        },
        {
          headerName: '建仓吨数',
          field: 'sOptionQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.data.sOptionQty, 3)
          }
        },
        {
          headerName: '建仓价格',
          field: 'sOptionPrice',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: '前期移仓单吨盈亏（元）',
          field: 'sEarlyWarehouseRelocationProfit',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: '平仓吨数',
          field: 'sClosedPositionQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.data.sClosedPositionQty, 3)
          }
        },
        {
          headerName: '平仓价格',
          field: 'sClosedPositionPrice',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: '平仓盈亏(万元)',
          field: 'sClosedPositionProfit',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: '持仓吨数',
          field: 'sPositionQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.data.sPositionQty, 3)
          }
        },
        {
          headerName: '收盘价',
          field: 'sClosingPrice',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return params.value && SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: '持仓盈亏(万元)',
          field: 'sOpenPositionProfit',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return params.value && SteelFormat.formatPrice(params.value)
          }
        }
      ],
      info: {}
    }
  },
  methods: {
    loadData() {
      return new Promise((resolve, reject) => {
        if (this.isMaintain === '0') {
          basisSelfCollectionSheet
            .getMsbBasisProprietaryHedgingCollectionInfoList(this.sHedgingCollectionContractId)
            .then((res) => {
              // this.rowData = res.data.map((item) => {
              //   item._selected = false
              //   item._selectedKeys = []
              //   return item
              // })
              resolve(res.data)
            })
            .catch(() => {
              reject(0)
            })
        } else {
          basisSelfCollectionSheet
            .getReportContractInfoFutures(this.sHedgingCollectionContractId)
            .then((res) => {
              this.info = res.data
              // this.rowData = res.data.map((item) => {
              //   item._selected = false
              //   item._selectedKeys = []
              //   return item
              // })
              resolve(res.data)
            })
            .catch(() => {
              reject(0)
            })
        }
      })
    }
  }
}
</script>

<style scoped></style>
