<!-- 收集单合同详情-采购基础信息 -->
<template>
  <cnd-form-card :title="$t('grid.tabs.basicInformation')" name="1">
    <el-row>
      <cnd-form-item label="业务类型" prop="sBusinessType">
        <el-select v-model="form.sBusinessType" disabled>
          <el-option
            v-for="item in selectOps['basis.self.business.type']"
            :key="item.sCodeValue"
            :label="item.sCodeName"
            :value="item.sCodeValue"
          />
        </el-select>
      </cnd-form-item>
      <!-- 合同号 -->
      <cnd-form-item label="销售合同号" prop="sContractNo">
        <el-input v-model="form.sContractNo" />
      </cnd-form-item>
      <cnd-form-item label="钢厂品种资源" prop="sGoodsCategoryDesc">
        <el-input v-model="form.sGoodsCategoryDesc" />
      </cnd-form-item>
      <cnd-form-item label="项目号" prop="sProjectNo">
        <el-input v-model="form.sProjectNo" />
      </cnd-form-item>
      <cnd-form-item label="合同数量" prop="sContractQty">
        <cnd-input-number v-model="form.sContractQty" clearable />
      </cnd-form-item>
      <cnd-form-item label="已采购数量" prop="sTradedQty">
        <cnd-input-number v-model="form.sTradedQty" />
      </cnd-form-item>
      <cnd-form-item label="已采购盈亏" prop="sTradedProfit">
        <number-validator v-model="form.sTradedProfit" :decimal-digit="2" />
      </cnd-form-item>
      <cnd-form-item label="预售价格" prop="sTradedPrice">
        <cnd-input-number v-model="form.sTradedPrice" />
      </cnd-form-item>
      <cnd-form-item label="当前采购市价" prop="sMarketingPrice">
        <cnd-input-number v-model="form.sMarketingPrice" />
      </cnd-form-item>
      <cnd-form-item label="税率" prop="sTaxRate">
        <cnd-input-number v-model="form.sTaxRate" type="percent" />
      </cnd-form-item>
      <cnd-form-item label="未采购敞口吨数" prop="sHedgingQty">
        <cnd-input-number
          :value="form.sContractQty - form.sTradedQty"
          disabled
        />
      </cnd-form-item>
      <cnd-form-item label="敞口现货盈亏（万元）" prop="sHedgingQty">
        <!-- (当前销售市价 - 采购成本） * 未采购敞口数量 / （1 + 税率) -->
        <cnd-input-number
          :value="sOpenSpotProfitLoss"
          disabled
          placeholder="请输入"
        />
      </cnd-form-item>
      <cnd-form-item label="敞口期现盈亏（万元）" prop="sHedgingQty">
        <!-- 敞口现货盈亏（万元）+期货盈亏合计+期权盈亏合计 -->
        <cnd-input-number :value="hedgingProfitLoss" disabled />
      </cnd-form-item>
      <cnd-form-item label="是否涉及期权" prop="sIsOptionInvolved">
        <el-select v-model="form.sIsOptionInvolved">
          <el-option
            v-for="item in selectOps['base.yes-no']"
            :key="item.sCodeValue"
            :label="item.sCodeName"
            :value="item.sCodeValue"
          />
        </el-select>
      </cnd-form-item>
      <cnd-form-item label="是否完结" prop="sCollectionCompleteStatus">
        <el-select v-model="form.sCollectionCompleteStatus">
          <el-option
            v-for="item in selectOps['base.yes-no']"
            :key="item.sCodeValue"
            :label="item.sCodeName"
            :value="item.sCodeValue"
          />
        </el-select>
      </cnd-form-item>
      <cnd-form-item label="完结说明" prop="sCollectionCompleteRemark">
        <el-input v-model="form.sCollectionCompleteRemark" />
      </cnd-form-item>
      <!-- 人员 -->
      <cnd-form-item :label="$t('grid.title.personnel')" prop="vStaffName">
        <horizon-search-select
          v-model="form.vStaffName"
          default-url="/esc/staff/dialog/org/v2/page/"
          search-key="sName"
          :option="{label: 'sPath', value: 'id'}"
          @change="handleStaffChange"
        />
      </cnd-form-item>
      <!-- 部门 -->
      <cnd-form-item
        :label="$t('grid.others.department')"
        prop="vDepartmentName"
      >
        <horizon-search-select
          v-model="form.vDepartmentName"
          type="depart"
          disabled
          @change="handleChange($event, 'sDepartmentId')"
        />
      </cnd-form-item>
      <cnd-form-item label="填报状态">
        <el-select v-model="form.sReportingStatus" disabled>
          <el-option
            v-for="item in selectOps['reporting.status']"
            :key="item.sCodeValue"
            :label="item.sCodeName"
            :value="item.sCodeValue"
          />
        </el-select>
      </cnd-form-item>
      <cnd-form-item label="填报人" prop="vReportName">
        <el-input v-model="form.vReportName" disabled />
      </cnd-form-item>
      <cnd-form-item label="备注" prop="sRemark">
        <el-input v-model="form.sRemark" />
      </cnd-form-item>
      <template v-if="isMaintain === '1'">
        <!-- <cnd-form-item v-if="form.sBusinessType !== '01'" label="是否长协采购" prop="sIsLongtermPur">
          <el-select v-model="form.sIsLongtermPur">
            <el-option
              v-for="item in selectOps['base.yes-no']"
              :key="item.sCodeValue"
              :label="item.sCodeName"
              :value="item.sCodeValue"
            />
          </el-select>
        </cnd-form-item> -->
        <cnd-form-item
          label="复核状态"
          prop="sCollectionCompleteReviewStatus"
          disabled
        >
          <el-select v-model="form.sCollectionCompleteReviewStatus">
            <el-option
              v-for="item in selectOps['basis.check.state']"
              :key="item.sCodeValue"
              :label="item.sCodeName"
              :value="item.sCodeValue"
            />
          </el-select>
        </cnd-form-item>
        <!-- <cnd-form-item label="结算方式" prop="sSettlementMethod">
          <el-select v-model="form.sSettlementMethod">
            <el-option
              v-for="item in selectOps['basis.self.settlement.method']"
              :key="item.sCodeValue"
              :label="item.sCodeName"
              :value="item.sCodeValue"
            />
          </el-select>
        </cnd-form-item> -->
        <!-- <cnd-form-item v-if="form.sSettlementMethod === 'AS'" label="成本情况" prop="sCostStatus">
          <el-select v-model="form.sCostStatus">
            <el-option
              v-for="item in selectOps['basis.self.cost.situation']"
              :key="item.sCodeValue"
              :label="item.sCodeName"
              :value="item.sCodeValue"
            />
          </el-select>
        </cnd-form-item> -->
      </template>
    </el-row>
  </cnd-form-card>
</template>

<script>
import PriorContractBasicDetail from './PriorContractBasicDetail.js'
export default {
  mixins: [PriorContractBasicDetail],
  computed: {
    // 敞口现货盈亏
    sOpenSpotProfitLoss() {
      return (
        ((this.form.sTradedPrice - this.form.sMarketingPrice) *
          (this.form.sContractQty - this.form.sTradedQty)) /
        (1 + this.form.sTaxRate) / 10000).toFixed(2)
    }

  }
}
</script>
