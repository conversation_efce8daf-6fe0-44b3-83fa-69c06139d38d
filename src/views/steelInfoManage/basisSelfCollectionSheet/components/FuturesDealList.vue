<!--
 * @Author: dqr
 * @Date: 2024-07-02 09:12:07
 * @LastEditors: D <NAME_EMAIL>
 * @LastEditTime: 2024-09-24 09:05:19
 * @FilePath: /egl-web/src/views/steelInfoManage/basisSelfCollectionSheet/components/FuturesDealList.vue
 * @Description:
 *
-->
<!-- 收集单合同详情-期货交易明细 -->
<template>
  <div>
    <div class="btn-group">
      <div class="text">期货交易明细</div>
      <div v-if="showBtn()">
        <el-button
          v-has:basis_self_collection_sheet_futures_deal_add
          type="primary"
          size="mini"
          @click="openAddContractDialog"
        >{{ $t('btns.add') }}</el-button>
        <el-button
          v-has:basis_self_collection_sheet_futures_deal_edit
          type="primary"
          :disabled="!curSelRowData || curSelRowData.length !== 1"
          size="mini"
          @click="editContractDetail"
        >{{ $t('btns.edit') }}</el-button>
        <el-button
          v-has:basis_self_collection_sheet_futures_deal_delete
          type="danger"
          size="mini"
          :disabled="!curSelRowData || !curSelRowData.length"
          @click="onDelete"
        >{{ $t('btns.delete') }}</el-button>
      </div>
    </div>

    <el-row>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="
          isMaintain === '1' ? columnDefs.concat(otherDefs) : columnDefs
        "
        :row-data="rowData"
        :heightinif="300"
        :paginationinif="false"
        :auto-load-data="false"
        table-selection="multiple"
        :header-total="headerTotal"
        @selectedChange="gridSelectedChange"
      />
    </el-row>
    <AddContractDialog
      v-if="addContractDialogVisible"
      :visible.sync="addContractDialogVisible"
      :s-hedging-collection-contract-id="sHedgingCollectionContractId"
      :select-info="selectInfo"
      :is-maintain="isMaintain"
      @refresh="refresh"
    />
  </div>
</template>

<script>
import { SteelFormat } from 'cnd-horizon-utils'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import AddContractDialog from './AddContractDialog'
import basisSelfCollectionSheet from '@/api/basisSelfCollectionSheet'
import agreement from '@/api/agreement'
import { getCnDitc } from '@/api/logistics/saleDelivery/saleorder'
export default {
  components: {
    steelTradeAggrid,
    AddContractDialog
  },
  props: {
    formData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    sHedgingCollectionContractId: {
      type: String,
      default: ''
    },
    isEdit: {
      type: Boolean,
      default: true
    },
    isMaintain: {
      type: String,
      default: '0'
    },
    rowData: {
      type: Array,
      default: () => {
        return []
      }
    },
    sCollectionStatus: {
      type: String,
      default: ''
    },
    addMsbBasisProprietaryHedgingCollectionInfo: {
      type: Function,
      default: () => {}
    },
    ops: {
      type: Object,
      default: () => {
        return null
      }
    },
    saveBasicData: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      selectOps: {
        'base.yes-no': [],
        'open.close.position': []
      },
      columnDefs: [
        {
          headerName: '期货合约',
          field: 'sFutureContractNo'
        },
        {
          headerName: '开平仓',
          field: 'sFuturePosition',
          valueFormatter: (params) => {
            return getCnDitc(
              params,
              this.selectOps['open.close.position'],
              'sFuturePosition'
            )
          }
        },
        {
          headerName: '吨数',
          field: 'sQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.data.sQty, 3)
          }
        },
        {
          headerName: '价格',
          field: 'sPrice',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: '是否移仓',
          field: 'sIsAllot',
          valueFormatter: (params) => {
            return getCnDitc(params, this.selectOps['base.yes-no'], 'sIsAllot')
          }
        }
      ],
      otherDefs: [
        {
          headerName: '数据来源',
          field: 'sIsSystemData',
          valueFormatter: (params) => {
            return params.value ? '系统' : '填报'
          }
        }
      ],
      curSelRowData: [],
      // 新增合同弹窗
      addContractDialogVisible: false,
      apiType: {
        0: {
          list: 'getMsbBasisProprietaryHedgingCollectionDetailFuturesList',
          remove: 'removesMsbBasisProprietaryHedgingCollectionDetailFutures'
        },
        1: {
          list: 'getReportContractFuturesList',
          remove: 'removesReportContractFutures'
        }
      },
      selectInfo: {}
    }
  },
  watch: {
    ops: {
      handler() {
        console.log('🚀 ~ ops ~ this.ops:', this.ops)

        if (!this.ops) {
          this.getDict()
        } else {
          this.selectOps = this.ops
        }
      },
      immediate: true
    }
  },
  methods: {
    getDict() {
      agreement
        .getDict(['base.yes-no', 'open.close.position'])
        .then((result) => {
          this.selectOps['base.yes-no'] = result.data[0]?.dicts
          this.selectOps['open.close.position'] = result.data[1]?.dicts
        })
        .catch(() => {})
    },
    showBtn() {
      return this.isEdit && ((this.sCollectionStatus && (this.sCollectionStatus !== '40')) || !this.sCollectionStatus)
    },
    loadData() {
      return new Promise((resolve, reject) => {
        if (this.isMaintain === '0') {
          basisSelfCollectionSheet
            .getMsbBasisProprietaryHedgingCollectionDetailFuturesList({
              sHedgingCollectionContractId: this.sHedgingCollectionContractId
            })
            .then((res) => {
              // this.rowData = res.data.map((item) => {
              //   item._selected = false
              //   item._selectedKeys = []
              //   return item
              // })
              resolve(res.data)
            })
            .catch(() => {
              reject(0)
            })
        } else {
          basisSelfCollectionSheet
            .getReportContractFuturesList(this.sHedgingCollectionContractId)
            .then((res) => {
              // this.rowData = res.data.map((item) => {
              //   item._selected = false
              //   item._selectedKeys = []
              //   return item
              // })
              resolve(res.data)
            })
            .catch(() => {
              reject(0)
            })
        }
      })
    },
    gridSelectedChange(rowData) {
      console.log('🚀 ~ gridSelectedChange ~ rowData:', rowData)
      this.curSelRowData = rowData.filter((item) => item._selected)
      const sQty = this.curSelRowData.map((item) => item.sQty).reduce((prev, next) => prev + next, 0)
      this.headerTotal = [
        { title: '已选', key: 'count', count: this.curSelRowData.length },
        { title: '吨数', count: SteelFormat.formatThousandthSign((sQty), 2) }
      ]
    },
    onDelete() {
      if (!this.curSelRowData.length) {
        this.$message({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      this.$confirm('是否删除该条数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const ids = this.curSelRowData.map((item) => item.sId)
          const removeApi = this.apiType[this.isMaintain].remove
          basisSelfCollectionSheet[removeApi](ids).then(() => {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.refresh()
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    refresh() {
      this.$emit('refresh')
    },
    async openAddContractDialog() {
      this.selectInfo = {}
      if (!this.sHedgingCollectionContractId) {
        await this.addMsbBasisProprietaryHedgingCollectionInfo()
      }
      await this.saveBasicData()
      this.addContractDialogVisible = true
    },
    async editContractDetail() {
      this.selectInfo = this.curSelRowData[0]
      await this.saveBasicData()
      this.addContractDialogVisible = true
    }
  }
}
</script>
