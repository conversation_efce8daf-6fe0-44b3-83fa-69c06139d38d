<!--
 * @Author: dqr
 * @Date: 2024-07-02 09:12:07
 * @LastEditors: D <NAME_EMAIL>
 * @LastEditTime: 2025-01-13 09:36:54
 * @FilePath: /egl-web/src/views/steelInfoManage/basisSelfCollectionSheet/components/StatisticsList.vue
 * @Description:
 *
-->
<!-- 基差自营跟踪单-统计报表 -->
<template>
  <div class="page-container tab">
    <div class="layout-content auto-page-title flexV">
      <!-- 搜索条件 -->
      <steel-search-form
        ref="searchForm"
        :form-items="formItems"
        :active-panel="''"
        @search="onSearch"
      />
      <div class="btn-group">
        <div class="text">收集明细</div>
        <div v-if="isEdit">
          <el-button
            v-if="basicData.sCollectionStatus !== '40'"
            v-has:basis_self_collection_sheet_statistics_save_remark
            type="primary"
            size="mini"
            @click="onSaveRemark"
          >保存备注</el-button>
          <el-button
            type="primary"
            size="mini"
            @click="openExportPage"
          >查看报表</el-button>
        </div>
      </div>
      <!-- 统计报表 -->
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="true"
        :table-selection="null"
        :footer-total="footerCount"
        :private-column-defs="true"
        :suppress-copy-rows-to-clipboard="true"
        :page-sizes=" [10, 20, 30, 50, 100, 200, 500]"
        :default-page-size="defaultPageSize"
        row-group-panel-show="always"
      />
    </div>
  </div>
</template>

<script>
import { SteelFormat } from 'cnd-horizon-utils'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import basisSelfCollectionSheet from '@/api/basisSelfCollectionSheet'
import agreement from '@/api/agreement'
import { getCnDitc } from '@/api/logistics/saleDelivery/saleorder'
import { handleDict } from '@/utils/common'
import exportBtn from '@/components/exportBtnV2'
import { Middleware } from 'cndinfo-ui'
import Vue from 'vue'
export default {
  components: {
    steelTradeAggrid,
    exportBtn
  },
  props: {
    basicData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    sHedgingCollectionContractId: {
      type: String,
      default: ''
    },
    isEdit: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      formItems: [
        {
          label: '业务类型',
          value: 'sBusinessType',
          type: 'elSelect',
          dict: 'basis.self.business.type',
          placeholder: '请选择收集状态'
        },
        // 部门
        {
          label: this.$t('grid.others.department'),
          value: 'sDepartmentId',
          type: 'cndInputDialog',
          dialogType: 'depart',
          placeholder: this.$t('grid.others.pleaseSelectTheDepartment')
        }
      ],
      selectOps: {
        'base.yes-no': [],
        'basis.self.business.type': [],
        // 分类
        'basis.self.business.category': [
          {
            sCodeName: '基差业务',
            sCodeValue: 'JC'
          },
          {
            sCodeName: '自营业务',
            sCodeValue: 'ZY'
          }
        ]
      },
      columnDefs: [
        {
          headerName: '分类',
          field: 'sBusinessCategory',
          enableRowGroup: true,
          valueGetter: (params) => {
            return params.data && getCnDitc(
              params,
              this.selectOps['basis.self.business.category'],
              'sBusinessCategory'
            )
          }
        },
        {
          headerName: '业务类型',
          field: 'sBusinessType',
          valueGetter: (params) => {
            return params.data && getCnDitc(
              params,
              this.selectOps['basis.self.business.type'],
              'sBusinessType'
            )
          },
          enableRowGroup: true
        },
        {
          headerName: '班子分管',
          field: 'sLeaderName',
          enableRowGroup: true
        },
        {
          headerName: '部门',
          field: 'vDepartmentName',
          enableRowGroup: true
        },
        {
          headerName: '未锁定敞口数量',
          field: 'sUnlockedExposureQty',
          cellStyle: { textAlign: 'right' },
          valueGetter: (params) => {
            return params.data && SteelFormat.formatThousandthSign(
              params.data.sUnlockedExposureQty,
              3
            )
          }
        },
        {
          headerName: '未锁定敞口盈亏',
          field: 'sUnlockedExposureProprietaryProfit',
          cellStyle: { textAlign: 'right' },
          valueGetter: (params) => {
            return params.data && SteelFormat.formatPrice(params.data.sUnlockedExposureProprietaryProfit)
          }
        },
        {
          headerName: '期货套保数量',
          field: 'sFuturesHedgingQty',
          cellStyle: { textAlign: 'right' },
          valueGetter: (params) => {
            return params.data && SteelFormat.formatThousandthSign(
              params.data.sFuturesHedgingQty,
              3
            )
          }
        },
        {
          headerName: '期货盈亏',
          field: 'sOpenPositionProfit',
          cellStyle: { textAlign: 'right' },
          valueGetter: (params) => {
            return params.data && SteelFormat.formatPrice(params.data.sOpenPositionProfit)
          }
        },
        {
          headerName: '期货套保后剩余敞口',
          field: 'sFuturesHedgingSurQty',
          cellStyle: { textAlign: 'right' },
          valueGetter: (params) => {
            return params.data && SteelFormat.formatThousandthSign(
              params.data.sFuturesHedgingSurQty,
              3
            )
          }
        },
        {
          headerName: '期货套保后整体盈亏',
          field: 'sFuturesHedgingQtyProfit',
          cellStyle: { textAlign: 'right' },
          valueGetter: (params) => {
            return params.data && SteelFormat.formatPrice(params.data.sFuturesHedgingQtyProfit)
          }
        },
        {
          headerName: '现货未定价数量',
          field: 'sSpotUnPricedQty',
          cellStyle: { textAlign: 'right' },
          valueGetter: (params) => {
            return params.data && SteelFormat.formatThousandthSign(
              params.data.sSpotUnPricedQty,
              3
            )
          }
        },
        // 备注可编辑
        {
          headerName: '备注',
          field: 'sRemark',
          editable: true,
          width: 200,
          cellEditorFramework: Vue.extend(
            Middleware.createComponent(
              'AgGridInput',
              {
                mark: 'sRemark',
                type: 'string'
              },
              {
                blur: ({ event, rowData, middleware }) => {
                  this.rowData[rowData.node.childIndex].sRemark = middleware.value
                }
              }
            )
          )
        }
      ],
      rowData: [],
      curSelRowData: [],
      searchInfo: {},
      exportConfig: [
        { label: this.$t('grid.title.purchaseContractNumber'), value: 'sCode' },
        { label: this.$t('grid.others.supplier'), value: 'vSupplierName' },
        { label: this.$t('grid.others.customer'), value: 'vCustomerName' },
        { label: this.$t('grid.others.item'), value: 'vGoodsDesc' },
        {
          label: this.$t('grid.title.quantity'),
          value: 'vSumQty',
          setValue: (value) => {
            return Number((+value).toFixed(4))
          }
        },
        {
          label: this.$t('grid.title.amount'),
          value: 'sOriginalAmt',
          setValue: (value) => {
            return Number((+value).toFixed(2))
          }
        },
        { label: this.$t('grid.others.recordWarehouse'), value: 'vScodeName' },
        { label: this.$t('grid.others.itemNumberTag'), value: 'sProjectCode' },
        { label: this.$t('grid.title.company'), value: 'vCompanyName' },
        {
          label: this.$t('grid.title.accountingGroup'),
          value: 'vCheckGroupName'
        },
        { label: this.$t('grid.title.personnel'), value: 'vStaffName' },
        {
          value: 'sSheetStatus',
          label: this.$t('grid.title.status'),
          setValue: (value) => {
            return handleDict(value, this.statusList)
          }
        },
        { label: this.$t('grid.title.createdBy'), value: 'vCreatorName' },
        { label: this.$t('grid.title.createdAt'), value: 'sCreateTime' }
      ],
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      footerCount: null,
      selectId: this.$route.query.Id,
      defaultPageSize: 200
    }
  },
  watch: {
    activeName(val) {
      if (val === 'statistics') {
        this.onSearch()
      }
    }
  },
  created() {
    agreement
      .getDict(['base.yes-no', 'basis.self.business.type'])
      .then((result) => {
        this.selectOps['base.yes-no'] = result.data[0]?.dicts
        this.selectOps['basis.self.business.type'] = result.data[1]?.dicts
      })
      .catch(() => {})
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    loadData(pagination = { page: 0, limit: 200 }) {
      const params = {
        ...this.searchInfo,
        sHedgingCollectionId: this.selectId
      }
      return new Promise((resolve, reject) => {
        basisSelfCollectionSheet
          .getStatisticPage(params, pagination)
          .then((res) => {
            this.rowData = res.data.content.map((item) => {
              item._selected = false
              item._selectedKeys = []
              return item
            })
            setTimeout(() => {
              this.$refs.aggrid?.gridApi?.setPinnedBottomRowData([this.setCount(this.rowData)])
            }, 0)
            resolve(res.data)
          })
          .catch(() => {
            reject(0)
          })
      })
    },
    gridSelectedChange(rowData) {
      console.log('🚀 ~ gridSelectedChange ~ rowData:', rowData)
      this.curSelRowData = rowData.filter((item) => item._selected)
    },
    onSaveRemark() {
      basisSelfCollectionSheet.saveStatisticsRemark(this.rowData).then(() => {
        this.$message.success('保存成功')
      })
    },
    setCount(rowData) {
      let sUnlockedExposureQty = 0
      let sUnlockedExposureProprietaryProfit = 0
      let sFuturesHedgingQty = 0
      let sFuturesProfit = 0
      let sFuturesHedgingSurQty = 0
      let sFuturesHedgingQtyProfit = 0
      let sSpotUnPricedQty = 0

      this.rowData.map((item) => {
        sUnlockedExposureQty += Number(item.sUnlockedExposureQty || 0)
        sUnlockedExposureProprietaryProfit += Number(
          item.sUnlockedExposureProprietaryProfit || 0
        )
        sFuturesHedgingQty += Number(item.sFuturesHedgingQty || 0)
        sFuturesProfit += Number(item.sFuturesProfit || 0)
        sFuturesHedgingSurQty += Number(item.sFuturesHedgingSurQty || 0)
        sFuturesHedgingQtyProfit += Number(item.sFuturesHedgingQtyProfit || 0)
        sSpotUnPricedQty += Number(item.sSpotUnPricedQty || 0)
      })
      return {
        sBusinessCategory: '合计',
        sUnlockedExposureQty,
        sUnlockedExposureProprietaryProfit,
        sFuturesHedgingQty,
        sFuturesProfit,
        sFuturesHedgingSurQty,
        sFuturesHedgingQtyProfit,
        sSpotUnPricedQty,
        _selected: false,
        _hiddenCheckbox: true
      }
    },
    openExportPage() {
      console.log('🚀 ~ openExportPage ~ this.basicData:', this.basicData)

      this.$router.push({
        path: `/basisSelfCollectionSheetExport/${this.selectId}`,
        query: {
          Id: this.selectId,
          type: 'read',
          name: '基差自营统计报表导出' + `【${this.basicData.sCode}】`,
          activeId: localStorage.getItem('menuId')
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import url('~@/styles/basisSelfCollectionSheet/detail.scss');
</style>
