/*
 * @Author: dqr
 * @Date: 2024-12-23 17:35:47
 * @LastEditors: D <NAME_EMAIL>
 * @LastEditTime: 2025-01-06 14:31:55
 * @FilePath: /egl-web/src/views/steelInfoManage/basisSelfCollectionSheet/Index.js
 * @Description:
 *
 */
/*
 * @Description:
 */
import { SteelFormat } from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
import basisSelfCollectionSheet from '@/api/basisSelfCollectionSheet'

import steelTradeAggrid from '@/components/steelTradeAggrid'
import { getCnDitc } from '@/utils/common'
import agreement from '@/api/agreement'
import TableBtn from './components/TableBtn'
import businessMixin from '@/utils/businessMixin'
export default {
  name: 'BasisSelfCollectionSheet',
  mixins: [businessMixin],
  components: {
    steelTradeAggrid,
    TableBtn
  },
  data() {
    return {
      selectOps: {
        'collection.state': null,
        'query.common.sheet.status': null
      },
      searchInfo: null,
      formItems: [
        {
          // 单据状态
          label: this.$t('grid.others.documentStatus'),
          value: 'sSheetStatus',
          type: 'elSelect',
          dict: 'query.common.sheet.status',
          // 请选择单据状态
          placeholder: '请选择单据状态'
        },
        {
          label: '收集状态',
          value: 'sCollectionStatus',
          type: 'elSelect',
          dict: 'collection.state',
          placeholder: '请选择收集状态'
        },
        {
          label: '周度',
          type: 'elInput',
          value: 'sCollectionWeek'
        },
        {
          label: '年度',
          type: 'elInput',
          value: 'sCollectionYear'
          // value: ['sCollectionYear'],
          // placeholder: [this.$t('grid.others.startDate')],
          // default: '',
          // type: 'elDatePicker',
          // dateType: 'year',
          // pickerOptions: {}
        },
        // 创建时间
        {
          label: this.$t('grid.others.creationDate'),
          value: ['sCreateTimeStart', 'sCreateTimeEnd'],
          placeholder: [
            this.$t('grid.others.startDate'),
            this.$t('grid.others.deadlineTag')
          ],
          type: 'elDatePicker'
        },
        // 部门
        {
          label: this.$t('grid.others.department'),
          value: 'sDepartmentId',
          type: 'cndInputDialog',
          dialogType: 'depart',
          placeholder: this.$t('grid.others.pleaseSelectTheDepartment')
        },
        // 人员
        {
          label: this.$t('grid.title.personnel'),
          value: 'sStaffId',
          type: 'cndInputDialogItem',
          option: { valueKey: 'sPath' },
          dialogType: 'staff',
          placeholder: this.$t('grid.others.pleaseSelectPersonnel')
        },
        {
          // 公司
          label: this.$t('grid.title.company'),
          value: 'sCompanyId',
          type: 'cndInputDialog',
          dialogType: 'company',
          // 请选择公司
          placeholder: this.$t('grid.others.pleaseSelectCompany')
        }
      ],
      columnDefs: [
        {
          headerName: this.$t('grid.others.operation'),
          width: 100,
          pinned: 'left',
          onCellClicked: (params) => {
            if (!this.isBusinessDisabled('save', params.data.sSheetStatus)) {
              this.editDetail(params)
            } else {
              this.readDetail(params)
            }
          },
          cellRenderer: (params) => {
            const sEditHtml = '<span style=color:blue;cursor:pointer>编辑</span>'
            const sDetailHtml = '<span style=color:blue;cursor:pointer>填报情况</span>'
            if (params.data.sSheetStatus === '10' || params.data.sSheetStatus === '15') {
              return sEditHtml
            } else {
              return sDetailHtml
            }
          }
        },
        {
          field: 'sCode',
          headerName: this.$t('grid.others.code'),
          cellStyle: { textAlign: 'left' },
          width: 150
        },
        {
          field: 'sCollectionWeek',
          headerName: '周度',
          cellStyle: { textAlign: 'left' }
        },
        {
          field: 'sCollectionYear',
          headerName: '年度',
          cellStyle: { textAlign: 'left' }
        },
        {
          field: 'sTitle',
          headerName: this.$t('grid.others.title'),
          cellStyle: { textAlign: 'left' },
          width: 150
        },
        {
          field: 'sSheetStatus',
          headerName: this.$t('grid.others.documentStatus'),
          cellStyle: { textAlign: 'left' },
          valueFormatter: (params) => {
            return getCnDitc(
              params,
              this.selectOps['query.common.sheet.status'],
              'sSheetStatus'
            )
          }
        },
        {
          field: 'sCollectionStatus',
          headerName: '收集状态',
          cellStyle: { textAlign: 'left' },
          valueFormatter: (params) => {
            return getCnDitc(
              params,
              this.selectOps['collection.state'],
              'sCollectionStatus'
            )
          }
        },
        {
          field: 'sCollectionStartDate',
          headerName: '开始填表日期',
          cellStyle: { textAlign: 'left' },
          valueGetter(params) {
            return Moment.time('YYYY-MM-DD', params.data.sCollectionStartDate)
          }
        },
        {
          field: 'sCollectionEndDate',
          headerName: '结束填表日期',
          cellStyle: { textAlign: 'left' },
          valueGetter(params) {
            return Moment.time('YYYY-MM-DD', params.data.sCollectionEndDate)
          }
        },
        {
          field: 'vStaffName',
          headerName: this.$t('grid.title.personnel'),
          cellStyle: { textAlign: 'left' }
        },
        {
          field: 'vDepartmentName',
          headerName: this.$t('grid.others.department'),
          cellStyle: { textAlign: 'left' }
        },
        {
          field: 'vCompanyName',
          headerName: this.$t('grid.title.company'),
          cellStyle: { textAlign: 'left' }
        },
        {
          field: 'vCreatorName',
          headerName: this.$t('grid.title.createdBy'),
          cellStyle: { textAlign: 'left' }
        },
        {
          field: 'sCreateTimeStart',
          headerName: this.$t('grid.title.createdAt'),
          cellStyle: { textAlign: 'left' },
          minWidth: 150,
          valueGetter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
          }
        }

      ],
      headerCount: null,
      footerCount: null,
      // 表头定义 end
      reload: true, // 重载
      rowData: [],
      totalRowData: '',
      // 弹窗控制值
      dialogVisible: {
        detail: false // 详情信息
      },
      detail: null,
      rowId: '',
      rowSCode: '',
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      defaultPageSize: 200
    }
  },
  beforeMount() {
    agreement
      .getDict(['collection.state', 'query.common.sheet.status'])
      .then((result) => {
        this.selectOps['collection.state'] = result.data[0].dicts // 状态
        this.selectOps['query.common.sheet.status'] = result.data[1].dicts // 状态
      })
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    setCount(vCount = 0, vSumQty = 0, vSumAmt = 0, flag) {
      this[flag] = [
        { count: vCount, key: 'count' },
        {
          title: this.$t('grid.title.quantity'),
          count: SteelFormat.formatThousandthSign(vSumQty, 4),
          unit: this.$t('grid.others.ton')
        },
        {
          title: this.$t('grid.title.amount'),
          count: SteelFormat.formatPrice(vSumAmt, 3),
          unit: this.$t('grid.others.yuan')
        }
      ]
    },
    handleFooterCount(rowData) {
      const details = rowData.filter((item) => item._selected)
      const vCount = details.length
      let count = 0
      let m = 0
      this.rowData.map((item) => {
        if (item._selected) {
          count += Number(item.vSumQty || 0)
          m += Number(item.sOriginalAmt || 0)
        }
      })
      this.setCount(vCount, count, m, 'footerCount')
    },
    loadData(pagination = { page: 0, limit: 30 }) {
      return new Promise((resolve, reject) => {
        this.$refs.searchForm.validate().then((valid) => {
          if (valid) {
            basisSelfCollectionSheet
              .getMsbBasisProprietaryHedgingCollectionPage(
                this.$refs.searchForm.getSearchData(),
                pagination
              )
              .then((res) => {
                this.rowData = res.data.content.map((item) => {
                  item._selected = false
                  item._selectedKeys = []
                  return item
                })
                resolve(res.data)
              })
              .catch(() => {
                reject(0)
              })
          } else {
            resolve(0)
          }
        })
      })
    },
    onClose() {
      this.dialogVisible.detail = false
      this.$refs.aggrid.reloadTableData()
    },
    addContract() {
      this.$router.push({
        path: `/basisSelfCollectionSheetDetail/add`,
        query: {
          type: 'add',
          name: this.$t('btns.add') + '基差自营收集单',
          activeId: localStorage.getItem('menuId')
        }
      })
    },
    editDetail(d) {
      this.$router.push({
        path: `/basisSelfCollectionSheetDetail/${d.data.sId}`,
        query: {
          Id: d.data.sId,
          status: d.data.sSheetStatus,
          type: 'edit',
          name: '基差自营收集单' + `【${d.data.sCode}】`,
          activeId: localStorage.getItem('menuId')
        }
      })
    },
    readDetail(d) {
      this.$router.push({
        path: `/basisSelfTrackingSheetDetail/${d.data.sId}`,
        query: {
          Id: d.data.sId,
          status: d.data.sSheetStatus,
          type: 'read',
          name: '基差自营跟踪单' + `【${d.data.sCode}】`,
          activeId: localStorage.getItem('menuId')
        }
      })
    }
  }
}
