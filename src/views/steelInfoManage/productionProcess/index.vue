<template>
  <div class="page-container">
    <p class="page-title">钢厂生产进程</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form ref="searchForm" :form-items="formItems" @search="onSearch" @searchValue="onSearchValue" />
      <div class="btn-group mt-10">
        <div class="text">
          钢厂生产进程{{ $t('grid.others.list') }}
        </div>
        <div>
          <export-btn
            file-name="钢厂生产进程"
            api-url="/esc/steel/factory/process/export"
            :post-params="searchInfo"
            :export-config="exportConfig"
            :get-grid-api="getGridApi"
            :isload-detail="true"
            :detail-params="detailParams"
          />
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        row-key="sId"
        table-selection="multiple"
        @rowClicked="rowClicked"
        @selectedChange="handleFooterCount"
        @rowDoubleClicked="onRowDoubleClicked"
      />
      <div class="btn-group mt-10">
        <div class="text">
          钢厂生产进程明细
        </div>
      </div>
      <steelTradeAggrid
        ref="detailAggrid"
        :load-data="loadDetail"
        :column-defs="childColumnDefs"
        :row-data="rowDataChild"
        :header-total="headerCount"
        :footer-total="footerCount"
        :auto-load-data="false"
        table-selection="multiple"
        @selectedChange="handleChildFooterCount"
      />
    </div>
  </div>
</template>

<script>
var Decimal = window.Decimal

import { Moment } from 'cnd-utils'
import { SteelFormat } from 'cnd-horizon-utils'
// import { handleDict } from '@/utils/common'
import {
  getDictet,
  getCnDitc
} from '@/api/logistics/saleDelivery/saleorder'
import {
  factProcessPage,
  factProcessPageChild
} from '@/api/steelInfoManage/productionProcess.js'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import exportBtn from '@/components/exportBtnV2'
import mixins from '../mixins'
export default {
  name: 'ProductionProcess',
  components: { steelTradeAggrid, exportBtn },
  mixins: [mixins],
  data() {
    const childrenColumns = [
      {
        headerName: '钢厂销售合同号',
        field: 'sFactoryScontCode'
      },
      {
        headerName: this.$t('grid.others.item'),
        field: 'sArtName'
      },
      {
        headerName: '规格',
        field: 'sSpec'
      },
      {
        headerName: '厚',
        field: 'sHigh'
      },
      {
        headerName: '宽',
        field: 'sWidth'
      },
      {
        headerName: '长',
        field: 'sLength'
      },
      {
        headerName: '订货件数',
        field: 'sQty',
        cellStyle: { textAlign: 'right' },
        valueFormatter: (params) => {
          return SteelFormat.formatThousandthSign(params.value)
        }
      },
      {
        headerName: '件重(吨)',
        field: 'sWeight',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatThousandthSign(params.value, 4)
        }
      },
      {
        headerName: '订货重量(吨)',
        field: 'sContractQty',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatThousandthSign(params.value, 4)
        }
      },
      {
        headerName: '交期日期',
        field: 'sDeliveryDate',
        cellStyle: { textAlign: 'right' },
        minWidth: 150,
        valueFormatter(params) {
          return Moment.time('YYYY-MM-DD', params.data.sDeliveryDate)
        }
      },
      {
        headerName: '到达港',
        field: 'sArrivePort'
      },
      {
        headerName: '未排产量(件/吨)',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return `${SteelFormat.formatThousandthSign(params.data.sLeftQty, 0)}/${SteelFormat.formatThousandthSign(params.data.sLeftWeight, 4)}`
        }
      },
      {
        headerName: '已排产量(件/吨)',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return `${SteelFormat.formatThousandthSign(params.data.sFinishQty, 0)}/${SteelFormat.formatThousandthSign(params.data.sFinishWeight, 4)}`
        }
      },
      // {
      //   headerName: '炼钢在库量(件/吨)',
      //   cellStyle: { textAlign: 'right' },
      //   valueFormatter: params => {
      //     return `${SteelFormat.formatThousandthSign(params.data.sSteelQty, 0)}/${SteelFormat.formatThousandthSign(params.data.sSteelWeight, 4)}`
      //   }
      // },
      // {
      //   headerName: '轧制在库量(件/吨)',
      //   cellStyle: { textAlign: 'right' },
      //   valueFormatter: params => {
      //     return `${SteelFormat.formatThousandthSign(params.data.sRollQty, 0)}/${SteelFormat.formatThousandthSign(params.data.sRollWeight, 4)}`
      //   }
      // },
      // {
      //   headerName: '可发运量(件/吨)',
      //   cellStyle: { textAlign: 'right' },
      //   valueFormatter: params => {
      //     return `${SteelFormat.formatThousandthSign(params.data.sAllowQty, 0)}/${SteelFormat.formatThousandthSign(params.data.sAllowWeight, 4)}`
      //   }
      // },
      {
        headerName: '工序名称',
        field: 'sExtend2',
        valueGetter: (params) => {
          return getCnDitc(params, this.options['steel.factory.process.working.procedure'], 'sExtend2')
        }
      },
      {
        headerName: '已完成数量(件/吨)',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return `${SteelFormat.formatThousandthSign(params.data.sSteelQty, 0)}/${SteelFormat.formatThousandthSign(params.data.sSteelWeight, 4)}`
        }
      },
      {
        headerName: '未完成数量(件/吨)',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return `${SteelFormat.formatThousandthSign(params.data.sRollQty, 0)}/${SteelFormat.formatThousandthSign(params.data.sRollWeight, 4)}`
        }
      },
      {
        headerName: '已出厂量(件/吨)',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return `${SteelFormat.formatThousandthSign(params.data.sOutQty, 0)}/${SteelFormat.formatThousandthSign(params.data.sOutWeight, 4)}`
        }
      },
      {
        headerName: this.$t('grid.title.createdAt'),
        field: 'sExtend10',
        cellStyle: { textAlign: 'right' },
        minWidth: 150,
        valueFormatter(params) {
          return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sExtend10)
        }
      }
    ]
    const statusDict = [
      {
        'sCodeValue': '70',
        'sCodeName': '执行'
      },
      {
        'sCodeValue': '75',
        'sCodeName': '完结'
      },
      {
        'sCodeValue': '90',
        'sCodeName': '作废'

      }
    ]
    return {
      disRemove: true,
      options: {
        'dev.common.sheet.status': [],
        'steel.factory.process.working.procedure': []
      },
      headerCount: null,
      footerCount: null,
      searchInfo: null,
      formItems: [
        {
          label: this.$t('grid.title.purchaseContractNumber'),
          value: 'sPurContractCode',
          type: 'elInput'
        },
        {
          label: '系列',
          value: 'sUpSourceType',
          type: 'elSelect',
          required: true,
          allHide: true,
          dict: 'steel.factory.field.config'
        },
        {
          label: this.$t('grid.others.supplier'),
          value: 'sSupplierId',
          type: 'cndInputDialogItem',
          defaultUrl: '/customer/series/pageSteel',
          otherOptions: {
            sUpSourceType: ''
          },
          option: { valueKey: 'sPath' }
        },
        // {
        //   label: this.$t('grid.others.customer'),
        //   value: 'sCustomerId',
        //   type: 'cndInputDialogItem',
        //   dialogType: 'customer',
        //   defaultUrl: '/esc/customer/page',
        //   option: { valueKey: 'sPath' }
        // },
        {
          label: this.$t('grid.others.itemNumberTag'),
          value: 'sProjectCode',
          type: 'elInput'
        },
        {
          label: '钢厂' + this.$t('grid.title.salesContractNumber'),
          value: 'sFactoryScontCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.title.accountingGroup'),
          value: 'sCheckGroupId',
          type: 'cndInputDialog',
          dialogType: 'cost',
          placeholder: this.$t('grid.others.pleaseSelectAccountingGroup')
        },
        {
          label: this.$t('grid.others.department'),
          value: 'sDepartmentId',
          type: 'cndInputDialog',
          dialogType: 'depart',
          placeholder: this.$t('grid.others.pleaseSelectTheDepartment')
        },
        {
          label: this.$t('grid.title.personnel'),
          value: 'sStaffId',
          type: 'cndInputDialogItem',
          option: { valueKey: 'sPath' },
          dialogType: 'staff',
          placeholder: this.$t('grid.others.pleaseSelectPersonnel')
        },
        {
          label: this.$t('grid.title.status'),
          value: 'sSheetStatus',
          type: 'elSelect',
          dict: statusDict,
          itemType: 'occultation',
          placeholder: this.$t('grid.others.pleaseSelectStatus')
        },
        {
          label: this.$t('grid.title.company'),
          value: 'sCompanyId',
          type: 'cndInputDialog',
          dialogType: 'company',
          placeholder: this.$t('grid.others.pleaseSelectCompany')
        },
        {
          label: this.$t('grid.title.createdAt'),
          value: ['sStartTime', 'sEndTime'],
          placeholder: [this.$t('grid.others.pleaseSelectTheDate'), this.$t('grid.others.pleaseSelectTheDate')],
          unlinkPanels: true,
          type: 'elDatePicker'
        },
        {
          label: '经营单位',
          value: 'sManagementId',
          type: 'cndInputDialog',
          dialogType: 'escOrg',
          itemType: 'occultation'
        },
        {
          label: this.$t('grid.title.createdBy'),
          value: 'sCreator',
          type: 'cndInputDialog',
          placeholder: this.$t('grid.others.pleaseSelectTheCreator'),
          dialogType: 'applicant'
        }

      ],
      columnDefs: [
        {
          field: 'sPurContractCode',
          headerName: this.$t('grid.title.purchaseContractNumber')
        },
        {
          headerName: this.$t('grid.others.supplier'),
          field: 'sSupplierName'
        },
        // {
        //   headerName: this.$t('grid.others.customer'),
        //   field: 'sCustomerName'
        // },
        {
          headerName: this.$t('grid.others.item'),
          field: 'sGoodsDetailName'
        },
        {
          headerName: this.$t('grid.others.itemNumberTag'),
          field: 'sProjectCode'
        },
        {
          headerName: this.$t('grid.title.company'),
          field: 'sCompanyName'
        },
        {
          headerName: this.$t('grid.title.accountingGroup'),
          field: 'sCheckGroupName'
        },
        {
          headerName: this.$t('grid.title.personnel'),
          field: 'sStaffName'
        },
        {
          headerName: this.$t('grid.title.status'),
          field: 'sSheetStatus',
          valueGetter: (params) => {
            return getCnDitc(params, this.options['dev.common.sheet.status'], 'sSheetStatus')
          }
        },
        {
          headerName: this.$t('grid.title.createdBy'),
          field: 'sCreatorName'
        },
        {
          field: 'sManagementName',
          headerName: '经营单位'
        },
        {
          field: 'sRemark',
          headerName: this.$t('grid.title.remarks')
        }
      ],
      childColumnDefs: childrenColumns,
      childrenColumns,
      rowData: [],
      rowDataChild: [],
      selectId: '',
      exportConfig: [
        { label: this.$t('grid.title.purchaseContractNumber'), value: 'sPurContractCode' },
        { label: '钢厂销售合同号', value: 'sFactoryScontCode' },
        { label: '车船号', value: 'sVehicleNo' },
        { label: '起运地(码头)', value: 'sLoadName' },
        { label: '目的地(码头)', value: 'sDestName' },
        { label: '钢卷号', value: 'sMetalNo' },
        { label: '收货地', value: 'sConsignAddr' },
        { label: '物流状态', value: 'sLogisticsStatus' },
        { label: '发运时间', value: 'sDispatchTime',
          setValue: (value) => {
            return Moment.time('YYYY-MM-DD HH:mm:ss', value)
          }
        }
        // { label: this.$t('grid.title.createdAt'), value: 'sCreateTime',
        //   setValue: (value) => {
        //     return Moment.time('YYYY-MM-DD HH:mm:ss', value)
        //   }
        // },
        // { label: this.$t('grid.title.purchaseContractNumber'), value: 'sPurContractCode' },
        // { label: this.$t('grid.others.supplier'), value: 'sSupplierName' },
        // { label: this.$t('grid.others.customer'), value: 'sCustomerName' },
        // { label: this.$t('grid.others.item'), value: 'sGoodsDesc' },
        // { label: this.$t('grid.others.itemNumberTag'), value: 'sProjectCode' },
        // { label: this.$t('grid.title.company'), value: 'vCompanyName' },
        // { label: this.$t('grid.title.accountingGroup'), value: 'vCheckGroupName' },
        // { label: this.$t('grid.title.personnel'), value: 'vStaffName' },
        // { label: this.$t('grid.title.status'), value: 'sSheetStatus',
        //   setValue: value => {
        //     return handleDict(value, this.options['dev.common.sheet.status'])
        //   }
        // },
        // { label: this.$t('grid.title.createdBy'), value: 'sCreatorName' },
        // { label: '经营单位', value: 'vManagementName' },
        // { label: this.$t('grid.title.remarks'), value: 'sRemark' }
      ],
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      }
    }
  },
  computed: {
    detailParams() {
      const sIds = []
      const selectIds = []
      this.rowData.forEach(item => {
        sIds.push(item.sId)
        if (item && item._selected) {
          selectIds.push(item.sId)
        }
      })
      return {
        sIds: sIds.toString(),
        selectIds: selectIds.toString()
      }
    }
  },
  beforeCreate() {
    getDictet([
      'dev.common.sheet.status',
      'steel.factory.process.working.procedure'
    ]).then(result => {
      this.options['dev.common.sheet.status'] = result.data[0].dicts.map(item => {
        if (item.sCodeValue === '70') {
          item.sCodeName = '执行'
        }
        return item
      })
      this.options['steel.factory.process.working.procedure'] = result.data[1].dicts
    }).catch(() => {
    })
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      const { sUpSourceType } = searchInfo
      if (load && sUpSourceType) {
        this.selectId = null
        this.$refs.aggrid.loadTableData()
        const headerConfig = {
          sUpSourceType: this.searchInfo.sUpSourceType,
          sMenuCode: '02',
          aggridRefs: this.$refs.detailAggrid,
          defaultColumns: this.childrenColumns
        }
        this.getHeader(headerConfig)
      } else {
        this.selectId = null
        this.rowData = []
        this.rowDataChild = []
        this.headerCount = null
        this.footerCount = null
      }
    },
    rowClicked({ data }) {
      if (!data) {
        this.selectId = null
        this.$refs.detailAggrid.loadTableData()
      } else if (data && this.selectId !== data.sId) {
        this.selectId = data.sId
        this.$refs.detailAggrid.loadTableData()
      }
    },
    handleFooterCount() {
      // this.$refs.aggrid.getSelectedData(res => {
      //   this.selectId = res.sId
      //   this.$refs.detailAggrid.loadTableData()
      // })
    },
    // 点击 表格
    onRowDoubleClicked(params) {
    },
    setCount(vCount = 0, vSumLeftWeight = 0, vSumLeftQty = 0, vSumFinishWeight = 0, vSumFinishQty = 0, flag) {
      this[flag] = [
        { count: vCount, key: 'count' },
        { title: '未排产量', count: SteelFormat.formatThousandthSign(vSumLeftWeight, 4), unit: this.$t('grid.others.ton') },
        { title: '未排件数', count: SteelFormat.formatThousandthSign(vSumLeftQty), unit: this.$t('grid.others.pieces') },
        { title: '已排产量', count: SteelFormat.formatThousandthSign(vSumFinishWeight, 4), unit: this.$t('grid.others.ton') },
        { title: '已排产量', count: SteelFormat.formatThousandthSign(vSumFinishQty), unit: this.$t('grid.others.pieces') }
      ]
    },
    handleChildFooterCount(rowData) {
      this.$refs.detailAggrid.getSelectedData(res => {
        const vCount = res.length
        let sLeftWeight = 0; let sLeftQty = 0; let sFinishWeight = 0; let sFinishQty = 0
        res.forEach(el => {
          sLeftWeight = +new Decimal(sLeftWeight).add(+el.sLeftWeight)
          sLeftQty = +new Decimal(sLeftQty).add(+el.sLeftQty)
          sFinishWeight = +new Decimal(sFinishWeight).add(+el.sFinishWeight)
          sFinishQty = +new Decimal(sFinishQty).add(+el.sFinishQty)
        })
        this.setCount(vCount, sLeftWeight, sLeftQty, sFinishWeight, sFinishQty, 'footerCount')
      })
    },
    // 主表
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        factProcessPage(this.$refs.searchForm.getSearchData(), {
          ...pagination
        }).then(res => {
          this.rowData = res.data.content.map((item, index) => {
            if (this.selectId) {
              item._selected = this.selectId === item?.sId
            } else {
              item._selected = index === 0
            }
            item._selectedKeys = []
            return item
          })
          resolve(res.data.totalElements)
          this.rowClicked({ data: res.data.content[0] })
        }).catch(() => {
          reject(0)
        })
      })
    },
    loadDetail(pagination) {
      return new Promise((resolve, reject) => {
        factProcessPageChild(this.selectId, pagination).then(res => {
          this.rowDataChild = res.data.page.content.map(item => {
            item._selected = false
            item._selectedKeys = []
            return item
          })
          const { vCount, vSumLeftWeight, vSumLeftQty, vSumFinishWeight, vSumFinishQty } = res.data
          this.setCount(vCount, vSumLeftWeight, vSumLeftQty, vSumFinishWeight, vSumFinishQty, 'headerCount')
          resolve(res.data.page)
        }).catch(() => {
          reject([])
        })
      })
    }
  }
}
</script>
