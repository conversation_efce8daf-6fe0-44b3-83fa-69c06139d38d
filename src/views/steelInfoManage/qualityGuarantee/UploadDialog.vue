<template>
  <cnd-dialog
    :visible.sync="visible"
    :title="'上传质保书'"
    :width="'700px'"
    :fullscreen="false"
    :show-close="true"
    :append-to-body="true"
    @close="handleCancel"
  >
    <template slot="content">
      <div class="btn-group">
        <div class="upload-group">
          <el-upload
            :file-list="[]"
            :auto-upload="false"
            multiple
            :show-file-list="false"
            :before-upload="handleFileChange"
            :on-change="handleFileChange"
            accept=".pdf,.jpg,.jpeg,.png"
          >
            <el-button type="primary" size="mini">添加文件</el-button>
          </el-upload>
          <div class="tips-text">单个文件大小不超过2M，总文件不超过50M。</div>
        </div>
      </div>
      <auto-wrap>
        <steelTradeAggrid
          :column-defs="columnDefs"
          :row-data="fileList"
          :paginationinif="false"
          :auto-load-data="false"
          row-key="fileName"
        />
      </auto-wrap>
    </template>
    <template slot="footer">
      <el-button size="mini" @click="handleCancel">取消</el-button>
      <el-button type="primary" size="mini" :loading="loading" @click="handleUpload">确认</el-button>
    </template>
  </cnd-dialog>
</template>

<script>

import steelTradeAggrid from '@/components/steelTradeAggrid'
import { uploadQualityGuarantee } from '@/api/steelInfoManage/qualityGuarantee'

function formatFileSize(bytes) {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

export default {
  components: { steelTradeAggrid },
  props: {
    visible: Boolean
  },
  data() {
    return {
      fileList: [],
      loading: false,
      columnDefs: [
        {
          headerName: '文件名',
          field: 'fileName',
          width: 350
        },
        {
          headerName: '大小',
          field: 'size',
          width: 100,
          valueFormatter: (params) => {
            return formatFileSize(params.value)
          }
        },
        {
          headerName: '操作',
          field: 'action',
          cellRenderer: () => '<span style="color:#F56C6C;cursor:pointer;">删除</span>',
          onCellClicked: (params) => {
            this.removeFile(params.rowIndex)
          },
          width: 80
        }
      ]
    }
  },
  methods: {
    handleFileChange(file, fileList) {
      let files = fileList || (file && file.raw ? [file] : [])
      if (file && file.raw) files = [file]
      files.forEach(f => {
        if (!this.fileList.find(item => item.file && item.file.name === f.name)) {
          this.fileList.push({
            file: f.raw || f,
            size: f.size,
            fileName: f.name
          })
        }
      })
    },
    async handleUpload() {
      if (!this.fileList.length) {
        this.$message.error('请添加文件')
        return
      }
      this.loading = true
      try {
        const formData = new FormData()
        for (const item of this.fileList) {
          console.log('item: ', item)
          formData.append('files', item.file)
        }
        await uploadQualityGuarantee(formData)
        this.$message.success('全部上传成功')
        this.$emit('success')
        this.handleCancel()
      } catch (e) {
        if (e.message.includes('已存在')) {
          this.$message.closeAll()
          this.handleCancel()
          this.$confirm(e.message + '，请重新选择。<br/>其余文件上传成功', '提示', {
            confirmButtonText: '确定',
            dangerouslyUseHTMLString: true,
            showCancelButton: false,
            type: 'warning'
          }).finally(() => {
            this.$emit('close')
          })
        }
      } finally {
        this.loading = false
      }
    },
    handleCancel() {
      this.fileList = []
      this.$emit('update:visible', false)
    },
    removeFile(index) {
      this.fileList.splice(index, 1)
    }
  }
}
</script>
<style lang="scss" scoped>
.upload-group {
  display: flex;
}
.tips-text {
  color: red;
  font-size: 12px;
  margin-left: 10px;
}
</style>
