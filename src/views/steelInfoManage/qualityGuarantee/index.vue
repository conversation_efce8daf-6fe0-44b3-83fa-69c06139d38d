<template>
  <div class="page-container">
    <p class="page-title">质保书管理</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form
        ref="searchForm"
        :form-items="formItems"
        @search="onSearch"
      />
      <div class="btn-group mt-10">
        <div class="text">
          质保书上传记录
        </div>
        <div>
          <el-button
            v-has:esc_steel_factory_logistics_escUpload
            type="primary"
            size="mini"
            @click="uploadFile"
          >
            上传质保书
          </el-button>
          <el-button
            v-has:esc_steel_factory_logistics_deleteFile
            type="danger"
            size="mini"
            class="mr-10"
            :disabled="delDisable"
            @click="removes"
          >
            删除
          </el-button>
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        table-selection="multiple"
        row-key="sId"
        @selectedChange="selectedChange"
      />
    </div>
    <UploadDialog :visible.sync="uploadDialogVisible" @success="handleUploadSuccess" @close="handleUploadClose" />
  </div>
</template>

<script>
import { Moment } from 'cnd-utils'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import { deleteQualityGuarantee, getQualityGuaranteeList } from '@/api/steelInfoManage/qualityGuarantee'
import { downFilePost } from '@/api/contract'
import { getFileType } from '@/utils/common'
import UploadDialog from './UploadDialog.vue'
import { mapGetters } from 'vuex'
export default {
  name: 'QualityGuarantee',
  components: { steelTradeAggrid, UploadDialog },
  data() {
    return {
      formItems: [
        {
          label: '文件名',
          value: 'steelNo',
          type: 'elInput'
        },
        {
          label: '创建人',
          value: 'createBy',
          type: 'cndInputDialog',
          itemType: 'occultation',
          placeholder: this.$t('grid.others.pleaseSelectTheCreator'),
          dialogType: 'applicant'
        },
        {
          label: '上传日期',
          value: ['createTime', 'createTimeTo'],
          placeholder: [this.$t('grid.others.startDate'), this.$t('grid.others.endDate')],
          unlinkPanels: true,
          type: 'elDatePicker'
        },
        {
          label: '经营单位',
          value: 'managementCode',
          type: 'cndInputDialogItem',
          option: {
            valueKey: 'sCode',
            seledLabel: 'sName',
            value: 'sCode'
          },
          dialogType: 'escOrg',
          itemType: 'occultation'
        }
      ],
      searchInfo: null,
      columnDefs: [
        {
          headerName: '文件名',
          field: 'fileName',
          width: 350
        },
        {
          headerName: '创建人',
          field: 'vCreatorName',
          width: 150
        },
        {
          headerName: '上传日期',
          field: 'createTime',
          cellStyle: { textAlign: 'right' },
          minWidth: 150,
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.createTime)
          }
        },
        {
          headerName: '经营单位',
          field: 'vManagementName'
        },
        {
          headerName: this.$t('grid.others.operation'),
          width: 150,
          cellStyle: { textAlign: 'center' },
          onCellClicked: (params) => {
            const event = params.event
            const target = event.target
            if (target.textContent === '查看文件') {
              this.viewFile(params.data)
            } else if (target.textContent === '删除') {
              this.deleteFile(params.data)
            }
          },
          cellRenderer: (params) => {
            let sHtml = '<div style="display:flex;justify-content:center;align-items:center;">'
            if (this.canView) {
              sHtml += '<span style="color:#66B1FF;cursor:pointer;margin-right:10px;">查看文件</span>'
            }
            if (this.canDelete) {
              sHtml += '<span style="color:#66B1FF;cursor:pointer;">删除</span>'
            }
            sHtml += '</div>'
            return sHtml
          }
        }
      ],
      rowData: [],
      headerTotal: null,
      footerTotal: null,
      delDisable: true,
      uploadDialogVisible: false
    }
  },
  computed: {
    ...mapGetters(['curBtnAuthList']),
    canView() {
      return this.curBtnAuthList.includes('esc_steel_factory_logistics_viewFile')
    },
    canDelete() {
      return this.curBtnAuthList.includes('esc_steel_factory_logistics_deleteFile')
    }
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      searchInfo.serviceType = '1'
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        const params = {
          pageNo: pagination.page,
          pageSize: pagination.limit
        }
        getQualityGuaranteeList(this.searchInfo, params).then(res => {
          this.rowData = res.data.content.map(item => {
            item._selected = false
            return item
          })
          resolve(res.data)
        }).catch(() => {
          reject([])
        })
      })
    },
    viewFile(data) {
      downFilePost([data.id], '/esc/steel/factory/logistics/list/batchDownload').then(res => {
        if (res.type === 'application/json') {
          const fileReader = new FileReader()
          fileReader.readAsText(res, 'utf-8')
          fileReader.onload = () => {
            const result = JSON.parse(fileReader.result)
            if (!result.data && result.message === 'ok') {
              this.$message.error('暂无文件')
            } else {
              this.$message.error(result.message)
            }
          }
        } else {
          const fileType = getFileType(data.fileName)
          const blob = new Blob([res], {
            type: fileType
          })
          const fileURL = URL.createObjectURL(blob)
          var win = window.open(fileURL)
          setTimeout(() => {
            win.document.title = data.fileName
          }, 1000)
        }
      })
    },
    removes() {
      this.$refs.aggrid.getSelectedData(list => {
        if (list.length === 0) {
          this.$message.error('请选择要删除的质保书')
          return
        }
        this.deleteFileFn(list.map(item => item.id))
      })
    },
    deleteFile(data) {
      this.deleteFileFn([data.id])
    },
    deleteFileFn(ids) {
      this.$confirm('确认删除该质保书记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteQualityGuarantee(ids).then(() => {
          this.$message.success('删除成功')
          this.$refs.aggrid.loadTableData()
        })
      })
    },
    selectedChange(rowData) {
      const details = rowData.filter((item) => item._selected)
      this.delDisable = details.length === 0
    },
    uploadFile() {
      this.uploadDialogVisible = true
    },
    handleUploadSuccess() {
      this.uploadDialogVisible = false
      this.$refs.aggrid.loadTableData()
    },
    handleUploadClose() {
      this.$refs.aggrid.loadTableData()
    }
  }
}
</script>

<style scoped>

</style>
