import {
  getPreContractList,
  getSheetStatus
} from '@/api/preContract/aggregateMethodContract'
import { Moment } from 'cnd-utils'
import moment from 'moment'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import { SteelFormat, DictUtil } from 'cnd-horizon-utils'
import { getCnDitc } from '@/utils/common'

// /total/msbpretotalcontractbasic/page/
export default {
  name: 'AggregateMethodContract',
  components: {
    steelTradeAggrid
  },
  data() {
    // const statusDict = [
    //   {
    //     sCodeValue: '10',
    //     sCodeName: '暂存',
    //     sSort: 10,
    //     sFilter: null,
    //     sIsEnabled: '1',
    //     sLanguage: 'zh_CN'
    //   },
    //   {
    //     sCodeValue: '20',
    //     sCodeName: '提交成功',
    //     sSort: '20',
    //     sFilter: null,
    //     sIsEnabled: '1',
    //     sLanguage: 'zh_CN'
    //   },
    //   {
    //     sCodeValue: 30,
    //     sCodeName: '提交失败',
    //     sSort: 30,
    //     sFilter: null,
    //     sIsEnabled: '1',
    //     sLanguage: 'zh_CN'
    //   },
    //   {
    //     sCodeValue: 40,
    //     sCodeName: '待审',
    //     sSort: 40,
    //     sFilter: null,
    //     sIsEnabled: '1',
    //     sLanguage: 'zh_CN'
    //   },
    //   {
    //     sCodeValue: 50,
    //     sCodeName: '驳回',
    //     sSort: 50,
    //     sFilter: null,
    //     sIsEnabled: '1',
    //     sLanguage: 'zh_CN'
    //   },
    //   {
    //     sCodeValue: 60,
    //     sCodeName: '已审核',
    //     sSort: 60,
    //     sFilter: null,
    //     sIsEnabled: '1',
    //     sLanguage: 'zh_CN'
    //   }
    // ]

    const startDate = moment()
      .subtract(90, 'day')
      .startOf('day')
      .format('YYYY-MM-DDTHH:mm:ss')
    const endDate = moment().endOf('day').format('YYYY-MM-DDTHH:mm:ss')
    return {
      statusDict: [],
      statusDict1: [],
      formItems: [
        {
          // 销售合同号
          label: '销售合同号',
          value: 'sSaleContractNumber',
          type: 'elInput',
          // 请输入销售合同号
          placeholder: '请输入销售合同号'
        },
        // 状态
        {
          label: '状态',
          value: 'sSheetStatus',
          type: 'elSelect',
          dict: 'msb.gross.contract.status',
          itemType: 'occultation',
          placeholder: this.$t('grid.others.pleaseSelectStatus')
        },
        {
          // 客户
          label: this.$t('grid.others.customer'),
          value: 'sCustomerId',
          type: 'cndInputDialogItem',
          dialogType: 'customer',
          defaultUrl: '/esc/customer/page',
          option: { valueKey: 'sPath' },
          // 请选择客户
          placeholder: this.$t('grid.others.pleaseSelectTheCustomer')
        },
        {
          // 供应商
          label: this.$t('grid.others.supplier'),
          value: 'sSupplierId',
          type: 'cndInputDialogItem',
          dialogType: 'customer',
          defaultUrl: '/esc/customer/page',
          option: { valueKey: 'sPath' },
          // 请选择供应商
          placeholder: this.$t('grid.others.pleaseSelectSupplier')
        },
        // {
        //   // 公司
        //   label: this.$t('grid.title.company'),
        //   value: 'sCompanyId',
        //   type: 'cndInputDialog',
        //   dialogType: 'company',
        //   // 请选择公司
        //   placeholder: this.$t('grid.others.pleaseSelectCompany')
        // },
        {
          // 人员
          label: this.$t('grid.title.personnel'),
          value: 'sStaffId',
          type: 'cndInputDialogItem',
          option: { valueKey: 'sPath' },
          dialogType: 'staff',
          // 请选择人员
          placeholder: this.$t('grid.others.pleaseSelectPersonnel')
        },
        // 部门
        {
          label: this.$t('grid.others.department'),
          value: 'sDepartmentId',
          type: 'cndInputDialog',
          dialogType: 'depart',
          // 请选择部门
          placeholder: this.$t('grid.others.pleaseSelectTheDepartment')
        },

        {
          // 核算组
          label: this.$t('grid.title.accountingGroup'),
          value: 'sCheckGroupId',
          type: 'cndInputDialog',
          dialogType: 'cost',
          // 请选择核算组
          placeholder: this.$t('grid.others.pleaseSelectAccountingGroup')
        },
        {
          // 公司
          label: this.$t('grid.title.company'),
          value: 'sCompanyId',
          type: 'cndInputDialog',
          dialogType: 'company',
          // 请选择公司
          placeholder: this.$t('grid.others.pleaseSelectCompany')
        },
        {
          label: '创建人',
          value: 'sCreator',
          type: 'cndInputDialog',
          itemType: 'occultation',
          placeholder: this.$t('grid.others.pleaseSelectTheCreator'),
          dialogType: 'applicant'
        },
        {
          label: '创建日期',
          value: ['sCreateTime', 'vCreateTimeTo'],
          placeholder: [
            this.$t('grid.others.pleaseSelectTheDate'),
            this.$t('grid.others.pleaseSelectTheDate')
          ],
          default: ['', ''],
          unlinkPanels: true,
          type: 'elDatePicker',
          itemType: 'occultation'
        },
        {
          // 签约日期
          label: '签约日期',
          value: ['sContractDate', 'vContractDateTo'],
          type: 'elDatePicker',
          default: [startDate, endDate],
          unlinkPanels: true,
          itemType: 'occultation'
        }
      ],
      columnDefs: [
        // 销售合同号
        {
          field: 'sSaleContractNumber',
          headerName: '销售合同号',
          cellStyle: { textAlign: 'left' }
        },
        // 客户
        {
          field: 'vCustomerName',
          headerName: '客户',
          cellStyle: { textAlign: 'left' }
        },
        // 供应商
        {
          field: 'vSupplierName',
          headerName: this.$t('grid.others.supplier'),
          cellStyle: { textAlign: 'left' }
        },
        // 签约日期
        {
          field: 'sContractDate',
          headerName: '签约日期',
          cellStyle: { textAlign: 'left' },
          minWidth: 150,
          valueGetter(params) {
            return Moment.time('YYYY-MM-DD', params.data.sContractDate)
          }
        },

        // 数量
        {
          field: 'sNumberAll',
          headerName: '数量',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value, 2)
          }
        },
        // 销售金额
        {
          field: 'sSaleAmountAll',
          headerName: '销售金额',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value, 2)
          }
        },
        // 采购金额 没有绑定字段
        {
          field: 'sPurchaseAmountAll',
          headerName: '采购金额',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value, 2)
          }
        },
        // 状态
        {
          headerName: '状态',
          field: 'sSheetStatus',
          valueGetter: (params) => {
            return getCnDitc(params, this.statusDict, 'sSheetStatus')
          }
        },
        {
          headerName: '云钢合同状态',
          field: 'conSaleContractSheetStatus',
          valueGetter: (params) => {
            return getCnDitc(
              params,
              this.statusDict1,
              'conSaleContractSheetStatus'
            )
          }
        },
        // 部门
        {
          field: 'vDepartmentName',
          headerName: this.$t('grid.others.department'),
          cellStyle: { textAlign: 'left' }
        },
        // 人员
        {
          field: 'vStaffName',
          headerName: this.$t('grid.title.personnel'),
          cellStyle: { textAlign: 'left' }
        },
        // 公司
        {
          field: 'vCompanyName',
          headerName: this.$t('grid.title.company'),
          cellStyle: { textAlign: 'left' }
        },
        // 核算组
        {
          field: 'vCheckGroupName',
          headerName: this.$t('grid.title.accountingGroup'),
          cellStyle: { textAlign: 'left' }
        },
        // 经营单位
        {
          field: 'vManagementName',
          headerName: '经营单位',
          cellStyle: { textAlign: 'left' },
          valueGetter: (params) => {
            return getCnDitc(params, this.businessTypeList, 'vManagementName')
          }
        },

        // 购销方式
        // {
        //   field: 'sPurchaseType',
        //   headerName: '购销方式',
        //   cellStyle: { textAlign: 'left' },
        //   valueGetter: (params) => {
        //     return getCnDitc(params, this.purchaseTypeList, 'sPurchaseType')
        //   }
        // },

        // 合同模版
        // {
        //   field: 'sContractTemplateType',
        //   headerName: '合同模版',
        //   cellStyle: { textAlign: 'left' },
        //   valueGetter: (params) => {
        //     return getCnDitc(
        //       params,
        //       this.contractTemplateTypeList,
        //       'sContractTemplateType'
        //     )
        //   }
        // },

        // 自动生成项目、合同
        {
          field: 'sAutoGenerateProject',
          headerName: '自动生成项目、合同',
          cellStyle: { textAlign: 'left' },
          valueGetter: (params) => {
            return params.data.sAutoGenerateProject === '1' ? '是' : '否'
          }
        },

        // 自动生成待审合同
        {
          field: 'sAutoGeneratePengingContract',
          headerName: '自动生成待审合同',
          cellStyle: { textAlign: 'left' },
          valueGetter: (params) => {
            return params.data.sAutoGeneratePengingContract === '1'
              ? '是'
              : '否'
          }
        },

        // 备案仓库列表
        {
          field: '',
          headerName: '备案仓库列表',
          cellStyle: { textAlign: 'left' }
        },

        // 创建人
        {
          field: 'vCreatorName',
          headerName: '创建人',
          cellStyle: { textAlign: 'left' }
        },
        // 创建时间
        {
          field: 'sCreateTime',
          headerName: '创建时间',
          cellStyle: { textAlign: 'left' },
          minWidth: 150,
          valueGetter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
          }
        },
        // 修改人
        {
          field: 'vModifierName',
          headerName: '修改人',
          cellStyle: { textAlign: 'left' }
        },
        // 修改时间
        {
          field: 'sModifyTime',
          headerName: '修改时间',
          cellStyle: { textAlign: 'left' },
          minWidth: 150,
          valueGetter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sModifyTime)
          }
        }
      ],
      rowData: [],
      headerCount: null,
      footerCount: null,
      searchInfo: null,
      purchaseTypeList: [
        {
          sCodeValue: '10',
          sCodeName: '以购定销',
          sSort: '0',
          sFilter: null,
          sisEnabled: '1',
          sLanguage: 'zh_CN'
        },
        {
          sCodeValue: '20',
          sCodeName: '以销定购',
          sSort: '0',
          sFilter: null,
          sisEnabled: '1',
          sLanguage: 'zh_CN'
        },
        {
          sCodeValue: '30',
          sCodeName: '统购分销',
          sSort: '0',
          sFilter: null,
          sisEnabled: '1',
          sLanguage: 'zh_CN'
        }
      ],
      businessTypeList: [
        {
          sCodeValue: 'ZY',
          sCodeName: '自营',
          sSort: '0',
          sFilter: null,
          sisEnabled: '1',
          sLanguage: 'zh_CN'
        },
        {
          sCodeValue: 'HZ',
          sCodeName: '合作',
          sSort: '0',
          sFilter: null,
          sisEnabled: '1',
          sLanguage: 'zh_CN'
        }
      ],
      contractTemplateTypeList: [
        {
          sCodeValue: 'GXXY',
          sCodeName: '购销协议',
          sSort: '0',
          sFilter: null,
          sisEnabled: '1',
          sLanguage: 'zh_CN'
        },
        {
          sCodeValue: 'DLXY',
          sCodeName: '代理协议',
          sSort: '0',
          sFilter: null,
          sisEnabled: '1',
          sLanguage: 'zh_CN'
        }
      ],
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      tableSelectData: [],
      isPopup: false,
      sSheetStatus: 1,
      transportModeList: [],
      costBearingList: []
    }
  },

  mounted() {
    // 获取字典表
    DictUtil.getDict(
      [
        'msb.contract.status',
        'msb.gross.contract.status',
        'msb.transport.mode',
        'msb.cost.bearing'
      ],
      (e) => {
        this.statusDict = e[0].dicts
        this.statusDict1 = e[1].dicts
        this.transportModeList = e[2].dicts
        this.costBearingList = e[3].dicts
      }
    )
  },
  methods: {
    closePopup() {
      this.isPopup = false
    },
    // 提交
    submit() {
      this.sSheetStatus
      if (!this.sSheetStatus) {
        this.$message.success('请选择需要修改的状态')
        return
      }
      const searchInfo = {
        sSheetStatus: this.sSheetStatus,
        sId: this.tableSelectData[0].sId
      }
      return new Promise((resolve, reject) => {
        getSheetStatus(searchInfo)
          .then((res) => {
            this.closePopup()
            this.$message.success('修改成功')
            this.onSearch()
          })
          .catch(() => {
            reject([])
          })
      })
    },
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },

    filteringTime(time) {
      if (time) {
        return time.replace('T', ' ')
      }
    },
    loadData(pagination) {
      this.searchInfo.sCreateTime = this.filteringTime(
        this.searchInfo.sCreateTime
      )
      this.searchInfo.vCreateTimeTo = this.filteringTime(
        this.searchInfo.vCreateTimeTo
      )
      this.searchInfo.sContractDate = this.filteringTime(
        this.searchInfo.sContractDate
      )
      this.searchInfo.vContractDateTo = this.filteringTime(
        this.searchInfo.vContractDateTo
      )
      return new Promise((resolve, reject) => {
        getPreContractList(this.searchInfo, pagination)
          .then((res) => {
            this.rowData = res.data.content.map((item) => {
              item._selected = false
              item._selectedKeys = []
              item.details = []
              return item
            })
            this.setCount(res.data.totalElements, 'headerCount')
            resolve(res.data.totalElements)
          })
          .catch(() => {
            reject([])
          })
      })
    },

    btnDisabled() {
      if (this.tableSelectData.length >= 1) {
        return false
      } else {
        return true
      }
    },
    // 合计
    handleFooterCount(rowData) {
      const details = rowData.filter((item) => item._selected)
      console.log(details, 'details')
      this.tableSelectData = details
      // const vCount = details.length
      // let count = 0
      // let m = 0
      // this.rowData.map((item) => {
      //   if (item._selected) {
      //     count += Number(item.vNumber || 0)
      //     m += Number(item.vAmount || 0)
      //   }
      // })
      // this.setCount(vCount, count, m, 'footerCount')
    },
    // 双击事件
    rowDoubleClicked(d) {
      this.$router.push({
        path: `/aggregateMethodContractDetail/${d.data.sId}`,
        query: {
          Id: d.data.sId,
          // status: d.data.sSheetStatus,
          type: 'edit',
          name: '预录入合同详情',
          activeId: localStorage.getItem('menuId')
        }
      })
    },
    setCount(vCount = 0, vSumQty = 0, vSumAmt = 0, flag) {
      // this[flag] = [
      //   { count: vCount, key: 'count' },
      //   {
      //     title: this.$t('grid.title.quantity'),
      //     count: SteelFormat.formatThousandthSign(vSumQty, 4),
      //     unit: this.$t('grid.others.ton')
      //   },
      //   {
      //     title: this.$t('grid.title.amount'),
      //     count: SteelFormat.formatPrice(vSumAmt, 3),
      //     unit: this.$t('grid.others.yuan')
      //   }
      // ]
    }
  }
}
