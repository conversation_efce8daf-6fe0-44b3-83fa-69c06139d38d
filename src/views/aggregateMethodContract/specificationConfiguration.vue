<template>
  <div class="page-container">
    <p class="page-title">材质规格配置</p>
    <div class="layout-content auto-page-title flexV">
      <div class="btn-group mt-10">
        <span>商品信息</span>
        <span>
          <el-button
            type="primary"
            size="mini"
            :disabled="!goodsClassId"
            @click="addRow"
          >{{ $t('btns.add') }}</el-button>

          <el-button
            type="primary"
            size="mini"
            :disabled="!goodsClassId"
            @click="saveForm"
          >{{ $t('btns.save') }}</el-button>

          <el-button
            type="danger"
            size="mini"
            @click="deleteRow"
          >{{ $t('btns.delete') }}</el-button>
        </span>
      </div>
      <cnd-layout
        class="grid-class"
        left-width="20%"
        left-border
        right-border
        right-bg-color="#ffffff"
      >
        <div slot="leftContent" class="h100">
          <div>
            <el-input
              v-model="searchKey"
              :placeholder="$t('grid.others.searchByCategoryName')"
              size="mini"
              clearable
              @keyup.native.stop.enter="searchGoodsCategory"
            >
              <i
                slot="suffix"
                class="el-icon-search el-input__icon"
                @click="searchGoodsCategory"
              />
            </el-input>
            <el-tree
              ref="leftTree"
              class="h100"
              node-key="id"
              :data="goodsTree"
              :default-expand-all="false"
              :expand-on-click-node="true"
              :props="treetProps"
              :filter-node-method="filterTree"
              @node-click="clickTree"
              @node-contextmenu="rightClick"
            />
          </div>
        </div>
        <div slot="rightContent" class="flexV h100">
          <ag-grid-wrap>
            <ag-grid-vue
              :suppress-copy-rows-to-clipboard="true"
              class="ag-theme-balham grid-class border-none"
              :row-drag-managed="true"
              :grid-options="gridData.gridOptions"
              :row-data="gridData.gridData"
              :modules="aGGridAllModules"
              @selection-changed="handleSelectChange"
            />
            <cnd-pagination
              v-if="gridData && gridData.pagination"
              :total="pagination.total"
              :page="pagination.pageNo"
              :limit="pagination.pageSize"
              :event="pageChange"
            />
          </ag-grid-wrap>
        </div>
      </cnd-layout>
    </div>
    <cnd-right-menu
      :target="contextMenuTarget"
      :show="contextMenuVisible"
      class="right-menu-tree"
      @update:show="(show) => contextMenuVisible = show"
    >
      <span @click="setAllExpand(true)">{{ $t('grid.others.thisNodeIsFullyExpanded') }}</span>
      <span @click="setAllExpand(false)">{{ $t('grid.others.thisNodeIsAllCollapsed') }}</span>
    </cnd-right-menu>
  </div>
</template>

<script>
import { GridPageMixin } from '@/utils/page-mixins'
import {
  msbproductspecRemove,
  queryGoodsDetailsTree,
  msbproductspecPage,
  msbproductspecModify
} from '@/api/preContract/preContract'
import { MessageUtil } from 'cnd-horizon-utils'

export default {
  name: 'SpecificationConfiguration',
  mixins: [GridPageMixin],
  data() {
    return {
      goodsClassId: '',
      vGoodsDetailName: '',
      treeNodeData: '', // 存当前数据
      treeNode: '', // 存当前节点信息
      defaultExpandAll: false,
      contextMenuVisible: false,
      contextMenuTarget: null,
      menuVisible: true,
      currentOperSIsEnable: '', // 当前操作
      goodsTree: [],
      treetProps: {
        label: 'name'
      },
      pagination: {
        pageNo: 0,
        pageSize: 20,
        total: 0
      },
      searchKey: '',
      searchTemp: [] // 用于存储过滤商品分类的临时数据
    }
  },
  watch: {
  },
  created() {
    queryGoodsDetailsTree().then(res => {
      this.goodsTree = res.data || []
    })
  },
  methods: {
    saveForm() {
      this.gridData.gridOptions.api.clearFocusedCell()
      const gridData = this.gridData.gridData.filter(item => {
        if (item.sMaterial || item.sSpec) {
          return item
        }
      })
      msbproductspecModify(gridData)
        .then(result => {
          if (result.code === '0000') {
            MessageUtil.success('操作成功')
            this.query()
          }
        })
    },
    addRow() {
      const rowTemplate = {
        sMaterial: '',
        sSpec: '',
        sProductId: this.goodsClassId
      }
      this.gridData.gridData.push(rowTemplate)
      const rowIndex = this.gridData.gridData.length - 1
      this.$nextTick(() => {
        this.gridData.gridOptions.api.setFocusedCell(rowIndex, 'sMaterial')
        this.gridData.gridOptions.api.startEditingCell({
          rowIndex,
          colKey: 'sMaterial'
        })
      })
    },
    rightClick(e, data, node) {
      this.treeNodeData = data // 存当前数据
      this.treeNode = node // 存当前节点信息
      this.contextMenuVisible = true // 让菜单显示
      const ele = document.querySelector('.right-menu-banner')
      ele.style.position = 'fixed'
      ele.style.top = `${e.clientY}px`
      ele.style.left = `${e.clientX + 10}px` // 根据鼠标点击位置设置菜单出现
    },
    childNodes(nodes, val) {
      nodes.expanded = val
      if (nodes.childNodes.length > 0) {
        for (var i in nodes.childNodes) {
          this.childNodes(nodes.childNodes[i], val)
        }
      }
    },
    setAllExpand(val) {
      this.childNodes(this.treeNode, val)
      this.contextMenuVisible = false
    },
    handleSelectChange() {
      const rows = this.gridData.gridOptions.api
        .getSelectedNodes()
        .map(item => item.data)
      this.currentOperSIsEnable = rows[0]?.sIsEnable
      this.gridOnSelectionChanged()
    },
    query() {
      msbproductspecPage({ sProductId: this.goodsClassId, ...this.pagination })
        .then(result => {
          this.setGridResult(result)
          this.pagination.total = result.data.totalElements
          this.clearSelRowData()
        })
    },
    clickTree(data) {
      if (data.type === '1') {
        this.goodsClassId = data.id
        this.query()
      }
    },
    deleteRow() {
      const getSelectedNodes = this.gridData.gridOptions.api.getSelectedNodes()
      if (!getSelectedNodes || getSelectedNodes.length === 0) {
        MessageUtil.info(this.$t('grid.others.pleaseSelectOperationData'))
        return
      }
      this.$confirm(`${this.$t('tips.isDeletionConfirmed')}`, this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'warning'
      })
        .then(() => {
          const indexAry = []
          const idAry = []
          getSelectedNodes.map(item => {
            indexAry.push(item.rowIndex)
            if (item.sId) {
              idAry.push(item.sId)
            }
          })
          if (idAry.length) {
            msbproductspecRemove(idAry)
              .then(result => {
                MessageUtil.success(this.$t('grid.tips.deletionSuccess'))
                this.query()
              })
              .catch(err => {
                console.log(err.massage)
              })
          } else {
            this.gridData.gridData = this.gridData.gridData.filter((item, index) => {
              return indexAry.indexOf(index) === -1
            })
          }
        })
        .catch(action => {
          console.log('取消--', action)
        })
    },
    pageChange(obj) {
      this.pagination.pageNo = obj.page
      this.pagination.pageSize = obj.limit
      this.query()
    },
    // 获取grid配置参数（列的定义....）
    getDefaultGridOptions() {
      const gridOptions = {
        columnDefs: [
          {
            field: 'index',
            headerName: '',
            width: 50,
            minWidth: 40,
            maxWidth: 40,
            headerCheckboxSelection: true,
            checkboxSelection: true,
            cellStyle: { 'justify-content': 'Center', textAlign: 'center' },
            cellClass: 'grid-cell-centered',
            pinned: 'left'
          },
          {
            field: 'sMaterial',
            headerName: '材质',
            editable: true,
            minWidth: 150,
            width: 150
          },
          {
            field: 'sSpec',
            headerName: '规格',
            editable: true,
            minWidth: 150,
            width: 150
          }
        ],
        rowSelection: 'multiple',
        onGridReady: function(params) {
        },
        onRowDoubleClicked: e => {

        }
      }
      return gridOptions
    },
    // 查找商品分类
    searchGoodsCategory() {
      this.searchTemp = [] // 置空
      this.$refs.leftTree.filter(this.searchKey.trim())
      // 搜索关键字为空时，折叠所有节点
      if (!this.searchKey) {
        this.changeTreeNodeStatus(this.$refs.leftTree.store.root, false)
      }
    },
    // 改变树节点状态
    changeTreeNodeStatus(node, status) {
      node.expanded = status
      for (let i = 0; i < node.childNodes.length; i++) {
        node.childNodes[i].expanded = status
        if (node.childNodes[i].childNodes.length > 0) {
          this.changeTreeNodeStatus(node.childNodes[i])
        }
      }
    },
    // 过滤数据--保留符合条件节点的子节点
    filterTree(searchKey, data) {
      if (!searchKey) return true
      // 根据关键字过滤节点
      if (data.name.indexOf(searchKey) !== -1) {
        this.searchTemp.push(data.id)
        return true
      }
      // 判断是否为满足条件节点的子/孙节点
      let sign = false
      this.searchTemp.forEach(item => {
        if (data.path) {
          if (data.path?.indexOf(item) !== -1) {
            sign = true
          }
        }
      })
      return sign
    }
  }
}
</script>
