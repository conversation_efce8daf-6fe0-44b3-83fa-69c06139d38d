<template>
  <div>
    <cnd-dialog
      :title="$t('grid.tabs.basicInformation')"
      :visible="true"
      @close="onClose"
    >
      <template slot="leftBtn">
        <el-button
          type="primary"
          size="mini"
          @click="dialogVisible.annex = true"
        >{{ $t('btns.attachmentManagement') }}
        </el-button>
        <!-- <el-button v-has:ESC_PURCONT_process_query type="primary" size="mini" @click="dialogVisible.processQuery = true">{{$t('grid.others.processInquiry')}}</el-button> -->
        <!-- <el-button
          type="primary"
          size="mini"
          @click="dialogVisible.approvalN8 = true"
        >ERP{{ $t('grid.others.approvalInquiry') }}</el-button> -->
      </template>

      <template slot="content">
        <!-- tab -->
        <el-tabs v-model="activeName">
          <!-- 基础信息 -->
          <el-tab-pane :label="$t('grid.tabs.basicInformation')" name="basic">
            <pre-contract-basic-detail
              v-if="activeName === 'basic'"
              :id="selectId"
              :form-data="basicData"
            />
          </el-tab-pane>
          <!-- 商品明细 -->
          <template v-if="selectId">
            <el-tab-pane
              :label="$t('grid.tabs.commodityDetails')"
              name="purchaseGoods"
            >
              <pre-contract-goods-detail
                v-if="activeName === 'purchaseGoods'"
                :id="selectId"
              />
            </el-tab-pane>
            <!-- 结算信息 -->
            <el-tab-pane
              :label="$t('grid.tabs.settlementInformation')"
              name="settlement"
            >
              <pre-contract-settlement-detail
                v-show="activeName === 'settlement'"
                :id="selectId"
                ref="settlement"
                :s-date-type="sDateType"
              />
            </el-tab-pane>

            <el-tab-pane label="条款信息" name="clauseInformation">
              <clause-information
                v-if="activeName === 'clauseInformation'"
                :id="selectId"
              />
            </el-tab-pane>
          </template>
        </el-tabs>
      </template>
    </cnd-dialog>
    <steel-annex-dialog
      :visible="dialogVisible.annex"
      append-to-body
      :biz-id="selectId"
      :disabled-btn="{
        scan: isBusinessDisabled(
          'save',
          basicData.msbPreTotalContractAttachmentList
        ),
        del: isBusinessDisabled(
          'save',
          basicData.msbPreTotalContractAttachmentList
        )
      }"
      @onSelect="dialogVisible.annex = false"
    />
    <!-- 弹出层 -->
    <horizon-approval-dialog
      :id="selectId"
      :type="'n8'"
      :solt-btn="false"
      :visible.sync="dialogVisible.approvalN8"
      @handleClose="dialogVisible.approvalN8 = false"
    />
  </div>
</template>

<script>
import businessMixin from '@/utils/businessMixin'
import { getPreContractDetail } from '@/api/preContract/aggregateMethodContract'
import PreContractBasicDetail from './PreContractBasicDetail'
import PreContractGoodsDetail from './PreContractGoodsDetail'
import preContractSettlementDetail from './PreContractSettlementDetail'
import clauseInformation from './clauseInformation'
export default {
  name: 'PreContractDetail',
  components: {
    PreContractBasicDetail,
    PreContractGoodsDetail,
    preContractSettlementDetail,
    clauseInformation
  },
  mixins: [businessMixin],
  data() {
    return {
      dialogVisible: {
        annex: false, // 附件管理
        processQuery: false, // 进程查询
        approval: false, // 审批情况
        approvalN8: false
      },
      selectId: this.$route.query.Id,
      basicData: {
        sStaffName: undefined,
        sDepartmentName: undefined,
        sCheckGroupName: undefined,
        sCompanyName: undefined,
        sCrossCompany: undefined,
        sCrossCompanyName: undefined,
        sSupplierName: undefined,
        sCustomerName: undefined,
        sContractTemplateType: undefined,
        sRemark: undefined,
        sErpProjectName: undefined,
        sPurchaseType: undefined,
        sBusinessType: undefined,
        sPartnerName: undefined,
        sCalcInterest: undefined,
        sLogisticsExpenseRate: undefined,
        sSaleContractNumber: undefined,
        sContractDate: undefined,
        sContractLocation: undefined,
        sAnnualAssociationNumber: undefined,
        sPurchaseContractName: undefined,
        sPurchaseContractCode: undefined,
        sPriceRemark: undefined,
        sPriceWay: undefined,
        sPriceFixType: undefined,
        sWeighRecordType: undefined,
        sTicketType: undefined,
        sPayType: undefined,
        sPortSurcharge: undefined,
        sOceanFreight: undefined,
        sDrayage: undefined,
        sSupervision: undefined,
        sAgentWharf: undefined,
        sAgentWarehouse: undefined,
        sDeliveryLocation: undefined,
        sInsurance: undefined,
        sPremium: undefined,
        sCreator: undefined,
        sCreateTime: undefined,
        sModifier: undefined,
        sModifyTime: undefined,
        sStatus: undefined,
        sStatusUpdateTime: undefined
      },
      activeName: 'basic', // active tab
      sDateType: ''
    }
  },
  watch: {
    activeName(nv, ov) {
      if (nv === 'settlement') {
        setTimeout(() => {
          this.$refs.settlement.getDictetInfo()
        }, 200)
      }
    }
  },
  created() {
    if (this.selectId) {
      this.getDetail()
    }
  },
  methods: {
    // 获取基本信息
    getDetail() {
      getPreContractDetail({
        id: this.selectId
      }).then((res) => {
        this.basicData = res.data || {}
        console.log('this.basicData', this.basicData)
      })
    }
  }
}
</script>
