<template>
  <div>
    <el-form
      ref="form"
      class="el-form-w100"
      label-width="100px"
      :inline="true"
      :model="form"
      size="small"
      :disabled="true"
    >
      <cnd-form-card-list :active-panel="activeCollapseName">
        <!-- 基础信息 -->
        <cnd-form-card :title="$t('grid.tabs.basicInformation')" name="1">
          <el-row>
            <cnd-form-item label="人员" prop="sStaffName">
              <el-input v-model="form.vStaffName" clearable />
            </cnd-form-item>
            <cnd-form-item label="部门" prop="sDepartmentName">
              <el-input v-model="form.vDepartmentName" clearable />
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.title.accountingGroup')" prop="sCheckGroupName">
              <el-input v-model="form.vCheckGroupName" clearable />
            </cnd-form-item>
            <cnd-form-item :label="$t('grid.title.company')" prop="sCompanyName">
              <el-input v-model="form.vCompanyName" clearable />
            </cnd-form-item>

            <cnd-form-item label="客户" prop="sCustomerName">
              <el-input v-model="form.vCustomerName" clearable />
            </cnd-form-item>

            <cnd-form-item :label="$t('grid.others.supplier')" prop="sSupplierName">
              <el-input v-model="form.vSupplierName" clearable />
            </cnd-form-item>

            <cnd-form-item label="跟单" prop="vDocumentaryStaffName">
              <el-input v-model="form.msbPreTotalContractClause.vDocumentaryUesrName" clearable />
            </cnd-form-item>

            <cnd-form-item label="经营单位" prop="sSupplierName">
              <el-input v-model="form.vManagementName" clearable />
            </cnd-form-item>

            <cnd-form-item label="备选供应商" prop="sSupplierName" :custom-width="12">
              <el-input
                :value="
                  msbPreTotalContractSupplierList(
                    form.msbPreTotalContractSupplierList
                  )
                "
                clearable
              />
            </cnd-form-item>
            <cnd-form-item label="跨平台" prop="sCrossCompany">
              <el-select
                v-model="form.sCrossCompany"
                :placeholder="$t('components.pleaseSelect')"
                clearable
                size="mini"
              >
                <el-option
                  v-for="item in options['base.yes-no']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item label="跨平台公司" prop="sCrossCompanyName">
              <el-input v-model="form.vCrossCompanyName" clearable />
            </cnd-form-item>
            <!-- <cnd-form-item label="合同模版" prop="sContractTemplateType">
              <el-select
                v-model="form.sContractTemplateType"
                :placeholder="$t('components.pleaseSelect')"
                clearable
                size="mini"
              >
                <el-option
                  v-for="item in options['msb.contact.template']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>-->
            <cnd-form-item label="自动生成项目、合同" prop="sAutoGenerateProject">
              <el-select
                v-model="form.sAutoGenerateProject"
                :placeholder="$t('components.pleaseSelect')"
                clearable
                size="mini"
              >
                <el-option
                  v-for="item in options['base.yes-no']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item label="自动生成待审合同" prop="sAutoGeneratePengingContract">
              <el-select
                v-model="form.sAutoGeneratePengingContract"
                :placeholder="$t('components.pleaseSelect')"
                clearable
                size="mini"
              >
                <el-option
                  v-for="item in options['base.yes-no']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item label="备注" prop="sRemark" :custom-width="12">
              <el-input v-model="form.msbPreTotalContractClause.sRemark" clearable />
            </cnd-form-item>
          </el-row>
        </cnd-form-card>
        <!-- 项目信息 -->
        <cnd-form-card title="项目信息" name="1">
          <el-row>
            <cnd-form-item label="利息测算方式" prop="sCalculationInterestMode">
              <el-select
                v-model="form.sCalculationInterestMode"
                :placeholder="$t('components.pleaseSelect')"
                clearable
                size="mini"
              >
                <el-option
                  v-for="item in options['msb.pact.interest.type']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item label="物流费用预估(单吨)" prop="sLogisticsFeeEstimate">
              <el-input v-model="form.sLogisticsFeeEstimate" clearable />
            </cnd-form-item>
          </el-row>
        </cnd-form-card>
        <!-- 合同基础信息 -->
        <cnd-form-card title="合同基础信息" name="3">
          <el-row>
            <cnd-form-item label="销售合同号" prop="sSaleContractNumber">
              <el-input v-model="form.sSaleContractNumber" />
            </cnd-form-item>
            <cnd-form-item label="年协号" prop="sAnnualAssociationNumber">
              <el-input v-model="form.sAnnualAssociationNumber" />
            </cnd-form-item>
            <cnd-form-item label="销售业务分类" prop="sSaleBusinessTypeName">
              <el-input v-model="form.sSaleBusinessTypeName" />
            </cnd-form-item>
            <cnd-form-item label="采购业务分类" prop="sPurBusinessTypeName">
              <el-input v-model="form.sPurBusinessTypeName" />
            </cnd-form-item>

            <cnd-form-item label="采购合同名称" prop="sPurchaseContractCode">
              <el-input v-model="form.sPurchaseContractCode" />
            </cnd-form-item>

            <cnd-form-item label="签约日期" prop="sContractDate">
              <el-input v-model="form.sContractDate" />
            </cnd-form-item>
            <cnd-form-item label="签约地点" prop="sContractLocation">
              <el-input v-model="form.sContractLocation" />
            </cnd-form-item>
            <!-- <cnd-form-item label="采购合同号" prop="sPurchaseContractCode">
              <el-input v-model="form.sPurchaseContractCode" />
            </cnd-form-item>-->
          </el-row>
        </cnd-form-card>
        <!-- 价格条款 -->
        <cnd-form-card v-if="false" title="价格条款" name="4">
          <el-row>
            <cnd-form-item label="价格备注" prop="sPriceRemark">
              <el-select
                v-model="form.sPriceRemark"
                :placeholder="$t('components.pleaseSelect')"
                clearable
                size="mini"
              >
                <el-option
                  v-for="item in options['base.yes-no']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item label="价格方式" prop="sPriceWay">
              <el-select
                v-model="form.sPriceWay"
                :placeholder="$t('components.pleaseSelect')"
                clearable
                size="mini"
              >
                <el-option
                  v-for="item in options['msb.price.type']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item label="定价方式" prop="sPriceFixType">
              <el-select
                v-model="form.sPriceFixType"
                :placeholder="$t('components.pleaseSelect')"
                clearable
                size="mini"
              >
                <el-option
                  v-for="item in options['price.priceType']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item label="计重方式" prop="sWeighRecordType">
              <el-select
                v-model="form.sWeighRecordType"
                :placeholder="$t('components.pleaseSelect')"
                clearable
                size="mini"
              >
                <el-option
                  v-for="item in options['msb.weighting.type']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item label="一票两票" prop="sTicketType">
              <el-select
                v-model="form.sTicketType"
                :placeholder="$t('components.pleaseSelect')"
                clearable
                size="mini"
              >
                <el-option
                  v-for="item in options['msb.vote.method']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item label="支付方式" prop="sPayType">
              <el-select
                v-model="form.sPayType"
                :placeholder="$t('components.pleaseSelect')"
                clearable
                size="mini"
              >
                <el-option
                  v-for="item in options['msb.pay.type']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
          </el-row>
        </cnd-form-card>
        <!-- 费用承担方 -->
        <cnd-form-card v-if="false" title="费用承担方" name="5">
          <el-row>
            <cnd-form-item label="港杂费" prop="sPortSurcharge">
              <el-select
                v-model="form.sPortSurcharge"
                :placeholder="$t('components.pleaseSelect')"
                clearable
                size="mini"
              >
                <el-option
                  v-for="item in options['msb.cost.undertake']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item label="海运费" prop="sOceanFreight">
              <el-select
                v-model="form.sOceanFreight"
                :placeholder="$t('components.pleaseSelect')"
                clearable
                size="mini"
              >
                <el-option
                  v-for="item in options['msb.cost.undertake']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item label="短驳费" prop="sDrayage">
              <el-select
                v-model="form.sDrayage"
                :placeholder="$t('components.pleaseSelect')"
                clearable
                size="mini"
              >
                <el-option
                  v-for="item in options['msb.cost.undertake']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item label="监管费" prop="sSupervision">
              <el-select
                v-model="form.sSupervision"
                :placeholder="$t('components.pleaseSelect')"
                clearable
                size="mini"
              >
                <el-option
                  v-for="item in options['msb.cost.undertake']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
          </el-row>
        </cnd-form-card>
        <!-- 交货信息 -->
        <cnd-form-card v-if="false" title="交货信息" name="6">
          <el-row>
            <cnd-form-item label="代理方指定码头" prop="sAgentWharf">
              <el-input v-model="form.sAgentWharf" />
            </cnd-form-item>
            <cnd-form-item label="代理方指定仓库" prop="sAgentWarehouse">
              <el-input v-model="form.sAgentWarehouse" />
            </cnd-form-item>
            <cnd-form-item label="交货地点" prop="sDeliveryLocation">
              <el-input v-model="form.sDeliveryLocation" />
            </cnd-form-item>
            <cnd-form-item label="保险" prop="sInsurance">
              <el-select
                v-model="form.sInsurance"
                :placeholder="$t('components.pleaseSelect')"
                clearable
                size="mini"
              >
                <el-option
                  v-for="item in options['base.yes-no']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item label="保费" prop="sPremium">
              <el-input v-model="form.sPremium" />
            </cnd-form-item>
          </el-row>
        </cnd-form-card>
        <!-- 系统信息 -->
        <cnd-form-card title="系统信息" name="7">
          <el-row>
            <cnd-form-item label="创建人" prop="vCreatorName">
              <el-input v-model="form.vCreatorName" />
            </cnd-form-item>
            <cnd-form-item label="创建时间" prop="sCreateTime">
              <el-input v-model="form.sCreateTime" />
            </cnd-form-item>
            <cnd-form-item label="修改人" prop="vModifierName">
              <el-input v-model="form.vModifierName" />
            </cnd-form-item>
            <cnd-form-item label="修改时间" prop="sModifyTime">
              <el-input v-model="form.sModifyTime" />
            </cnd-form-item>
            <cnd-form-item label="状态" prop="sSheetStatus">
              <el-select
                v-model="form.sSheetStatus"
                :placeholder="$t('components.pleaseSelect')"
                clearable
                size="mini"
              >
                <el-option
                  v-for="item in options['msb.contract.status']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <!-- <cnd-form-item label="生效时间" prop="sStatusUpdateTime">
              <el-input v-model="form.sStatusUpdateTime" />
            </cnd-form-item>-->
          </el-row>
        </cnd-form-card>
      </cnd-form-card-list>
    </el-form>
  </div>
</template>

<script>
import businessMixin from '@/utils/businessMixin'
import { getDictet } from '@/api/logistics/saleDelivery/saleorder'
// import agreement from '@/api/agreement'

export default {
  name: 'PreContractBasicDetail',
  mixins: [businessMixin],
  props: {
    formData: {
      type: Object,
      default: null
    },
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      activeCollapseName: ['1', '2', '3', '4', '5', '6', '7'],
      options: {
        'base.yes-no': '',
        'msb.contact.template': '',
        'trade.pur.sal.mode': '',
        'base.comm.busi.type': '',
        'msb.measurement.interest.type': '',
        'msb.price.type': '',
        'price.priceType': '',
        'msb.weighting.type': '',
        'msb.vote.method': '',
        'msb.pay.type': '',
        'msb.cost.undertake': '',
        'dev.common.sheet.status': '',
        'msb.contract.status': '',
        'msb.pact.interest.type': ''
      }
    }
  },
  computed: {
    form() {
      return this.formData
    }
  },
  created() {
    getDictet([
      'base.yes-no',
      'msb.contact.template',
      'trade.pur.sal.mode',
      'base.comm.busi.type',
      'msb.measurement.interest.type',
      'msb.price.type',
      'price.priceType',
      'msb.weighting.type',
      'msb.vote.method',
      'msb.pay.type',
      'msb.cost.undertake',
      'dev.common.sheet.status',
      'msb.contract.status',
      'msb.pact.interest.type'
    ])
      .then((result) => {
        this.options['base.yes-no'] = result.data[0].dicts
        this.options['msb.contact.template'] = result.data[1].dicts
        this.options['trade.pur.sal.mode'] = result.data[2].dicts
        this.options['base.comm.busi.type'] = result.data[3].dicts
        this.options['msb.measurement.interest.type'] = result.data[4].dicts
        this.options['msb.price.type'] = result.data[5].dicts
        this.options['price.priceType'] = result.data[6].dicts
        this.options['msb.weighting.type'] = result.data[7].dicts
        this.options['msb.vote.method'] = result.data[8].dicts
        this.options['msb.pay.type'] = result.data[9].dicts
        this.options['msb.cost.undertake'] = result.data[10].dicts
        this.options['dev.common.sheet.status'] = result.data[11].dicts
        this.options['msb.contract.status'] = result.data[12].dicts
        this.options['msb.pact.interest.type'] = result.data[13].dicts
      })
      .catch(() => {})
  },
  methods: {
    msbPreTotalContractSupplierList(array) {
      let arr = []
      array.forEach((e) => {
        if (e.vSupplierName) {
          arr.push(e.vSupplierName)
        }
      })
      arr = arr.join(',')
      return arr
    }
  }
}
</script>
