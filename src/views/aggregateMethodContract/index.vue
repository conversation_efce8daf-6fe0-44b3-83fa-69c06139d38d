<template>
  <div class="page-container">
    <p class="page-title">总额法合同</p>
    <div class="layout-content auto-page-title flexV">
      <!-- 搜索条件 -->
      <steel-search-form
        ref="searchForm"
        :form-items="formItems"
        @search="onSearch"
      />
      <!-- 按钮组 -->
      <div class="btn-group mt-10">
        <div class="text">预录入合同列表</div>
        <div>
          <!-- v-has:esc_stock_delivery_retail_fixed_price_bill_enable -->
          <el-button
            v-has:esc_gross_contract_law_modified_state
            type="primary"
            size="mini"
            :disabled="btnDisabled()"
            @click="isPopup = true"
          >状态修改</el-button>
        </div>
      </div>

      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :header-total="headerCount"
        :footer-total="footerCount"
        :auto-load-data="false"
        table-selection="single"
        @selectedChange="handleFooterCount"
        @rowDoubleClicked="rowDoubleClicked"
      />

      <!-- 弹框 -->
      <cnd-dialog
        :visible="isPopup"
        class="addPopup"
        :fullscreen="false"
        title="修改状态"
        width="350px"
        height="fit-content"
        @close="closePopup"
      >
        <template slot="content">
          <div class="item">
            状态：
            <el-radio v-model="sSheetStatus" :label="20"> 暂存 </el-radio>
            <el-radio v-model="sSheetStatus" :label="21"> 提交成功 </el-radio>
          </div>
        </template>
        <template slot="footer">
          <el-button size="mini" @click="closePopup">取消</el-button>
          <el-button type="primary" size="mini" @click="submit">确定</el-button>
        </template>
      </cnd-dialog>
    </div>
  </div>
</template>

<script>
import Index from './Index.js'
export default Index
</script>
<style scoped>
.item {
  padding: 20px 28px;
  font-size: 16px;
}
</style>
