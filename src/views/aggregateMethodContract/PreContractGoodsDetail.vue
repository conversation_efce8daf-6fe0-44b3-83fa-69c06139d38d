<template>
  <div class="page-container">
    <div class="layout-content auto-page-title flexV">
      <steelTradeAggrid
        ref="aggrid"
        :heightinif="600"
        :column-defs="purchaseColumnDefs"
        :row-data="rowData"
        :header-total="headerCount"
        :footer-total="footerCount"
        table-selection
        :load-data="loadGoodsData"
      />
    </div>
  </div>
</template>

<script>
import { getPurchaseGoodsDetail } from '@/api/preContract/aggregateMethodContract'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import { SteelFormat, DictUtil } from 'cnd-horizon-utils'
export default {
  name: 'PurchaseGoodsDetail',
  components: {
    steelTradeAggrid
  },
  data() {
    return {
      searchInfo: null,
      headerCount: null,
      footerCount: null,
      rowData: [],
      purchaseColumnDefs: [
        // ERP品名
        {
          field: 'sCustomerPurchaseName',
          headerName: 'ERP品名',
          pinned: 'left',
          cellStyle: { textAlign: 'left' }
        },
        // 客户品名
        {
          field: 'sCustomerPurchaseName',
          headerName: '客户品名',
          pinned: 'left',
          cellStyle: { textAlign: 'left' }
        },
        // 材质
        {
          field: 'sTexture',
          headerName: '材质',
          pinned: 'left',
          cellStyle: { textAlign: 'left' }
        },
        // 规格
        {
          field: 'sSpecification',
          headerName: '规格',
          pinned: 'left',
          cellStyle: { textAlign: 'left' }
        },
        // 产地
        // {
        //   field: 'sOrigin',
        //   headerName: '产地',
        //   pinned: 'left',
        //   cellStyle: { textAlign: 'left' }
        // },
        // 数量
        {
          field: 'sNumber',
          headerName: '数量',
          pinned: 'left',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value, 2)
          }
        },
        // 采购单价
        {
          field: 'sPurchasePrice',
          headerName: '采购单价',
          pinned: 'left',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value, 2)
          }
        },
        // 采购金额
        {
          field: 'sPurchaseAmount',
          headerName: '采购金额',
          pinned: 'left',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value, 2)
          }
        },
        // 销售单价
        {
          field: 'sSalePrice',
          headerName: '销售单价',
          pinned: 'left',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value, 2)
          }
        },
        // 销售金额
        {
          field: 'sSaleAmount',
          headerName: '销售金额',
          pinned: 'left',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value, 2)
          }
        },
        {
          field: 'sTaxRevenuePrice',
          headerName: '暂定含税运费单价',
          pinned: 'left',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value, 2)
          }
        },
        {
          field: 'sTaxRevenuePrice',
          headerName: '是否品种钢',
          pinned: 'left',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return params.data.sIsVarietySteel === '1' ? '是' : '否'
          }
        },
        {
          field: 'sTaxRevenuePrice',
          headerName: '品种钢分类',
          pinned: 'left',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return this.steelTypeList.find(item => item.sCodeValue === params.data.sVarietySteelType)?.sCodeName || ''
          }
        }
      ],
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      steelTypeList: []
    }
  },
  mounted() {
    // 获取字典表
    DictUtil.getDict(
      [
        'msb.classification.of.steel.varieties'
      ],
      (e) => {
        this.steelTypeList = e[0].dicts
      }
    )
  },
  methods: {
    loadGoodsData(pagination) {
      return new Promise((resolve, reject) => {
        this.searchInfo = { sContractId: this.$route.query.Id }
        getPurchaseGoodsDetail(
          { sTotalContractBasicId: this.$route.query.Id },
          pagination
        )
          .then((res) => {
            this.rowData = res.data.content.map((item) => {
              item._selected = false
              item._selectedKeys = []
              item.details = []
              return item
            })
            // this.setCount(
            //   res.data.totalElements,
            //   res.data.vTotalNumber,
            //   res.data.vTotalPurchaseAmount,
            //   res.data.vTotalSaleAmount,
            //   'headerCount'
            // )
            resolve(res.data.content)
          })
          .catch(() => {
            reject([])
          })
      })
    },
    setCount(vCount = 0, vSumQty = 0, vSumAmt = 0, vSale, flag) {
      this[flag] = [
        { count: vCount, key: 'totalElements' },
        {
          title: this.$t('grid.title.quantity'),
          count: SteelFormat.formatThousandthSign(vSumQty, 4),
          unit: this.$t('grid.others.ton')
        },
        {
          title: '采购总金额',
          count: SteelFormat.formatPrice(vSumAmt, 3),
          unit: this.$t('grid.others.yuan')
        },
        {
          title: '销售总金额',
          count: SteelFormat.formatPrice(vSale, 3),
          unit: this.$t('grid.others.yuan')
        }
      ]
    }
  }
}
</script>
