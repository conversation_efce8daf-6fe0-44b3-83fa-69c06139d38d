<template>
  <!-- 拜访记录 -->
  <div class="page-container">
    <p class="page-title">拜访记录</p>
    <div class="layout-content auto-page-title flexV">
      <cnd-search-form
        ref="searchForm"
        :form-items="formItems"
        @search="onSearch"
        @searchValue="onSearchValue"
      />
      <div class="btn-group mt-10">
        <div class="text">列表</div>
        <el-button size="mini" type="primary" @click="getMaintenance('view')">查看</el-button>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        table-selection="single"
        @selectedChange="handleFooterCount"
        @rowDoubleClicked="onRowDoubleClicked"
        @rowSelected="onDefSelectfun"
      />
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import { Moment } from 'cnd-utils'
import { getCnDitc } from '@/utils/common'
import { getDictet } from '@/api/logistics/saleDelivery/saleorder'
import { logList } from '@/api/sysConfig/travellingMerchant'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import exportBtn from '@/components/exportBtnV2'
import mixins from '../mixins'
export default {
  name: 'BusinessPartnerProfile',
  components: { steelTradeAggrid, exportBtn },
  mixins: [mixins],
  data() {
    const startDate = moment()
      .subtract(90, 'day')
      .startOf('day')
      .format('YYYY-MM-DDTHH:mm:ss')
    const endDate = moment().endOf('day').format('YYYY-MM-DDTHH:mm:ss')
    return {
      searchInfo: null,
      formItems: [
        {
          // 人员
          label: this.$t('grid.title.personnel'),
          value: 'sStaffId',
          type: 'cndInputDialogItem',
          option: { valueKey: 'sPath' },
          dialogType: 'staff',
          // 请选择人员
          placeholder: this.$t('grid.others.pleaseSelectPersonnel')
        },
        {
          label: '客商名称',
          value: 'sCustomerName',
          type: 'elInput'
        },
        {
          label: '统一社会信用代码',
          value: 'sCreditNo',
          type: 'elInput',
          width: '180px'
        },
        {
          label: '拜访日期',
          value: ['sVisitDateFrom', 'sVisitDateTo'],
          placeholder: [
            this.$t('grid.others.pleaseSelectTheDate'),
            this.$t('grid.others.pleaseSelectTheDate')
          ],
          default: ['', ''],
          unlinkPanels: true,
          type: 'elDatePicker',
          itemType: 'occultation'
        },
        {
          label: '记录编码',
          value: 'sRecordCode',
          type: 'elInput'
        },
        {
          label: '创建人',
          value: 'sCreator',
          type: 'cndInputDialog',
          itemType: 'occultation',
          placeholder: this.$t('grid.others.pleaseSelectTheCreator'),
          dialogType: 'applicant'
        },
        {
          label: '创建时间',
          value: ['sCreateTimeFrom', 'sCreateTimeTo'],
          placeholder: [
            this.$t('grid.others.pleaseSelectTheDate'),
            this.$t('grid.others.pleaseSelectTheDate')
          ],
          default: [startDate, endDate],
          unlinkPanels: true,
          type: 'elDatePicker',
          itemType: 'occultation'
        }
      ],
      columnDefs: [
        {
          field: 'sRecordCode',
          headerName: '记录编码',
          width: '130px'
        },
        {
          field: 'sCustomerArchivesName',
          headerName: '客商名称'
        },
        {
          field: 'sCreditNo',
          headerName: '统一社会信用代码'
        },
        {
          field: 'sVisitDate',
          headerName: '拜访日期',
          width: '120px',
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD ', params.data.sVisitDate)
          }
        },

        {
          headerName: '拜访内容',
          field: 'msbCustomerVisitRecordContentVoList',
          width: '160px',
          valueGetter(params) {
            const contents =
              params.data.msbCustomerVisitRecordContentVoList.map((v) => {
                return v.sTopicTypeName + '：' + v.sTopicContent + '；'
              })
            return contents.join('，')
          }
        },
        // {
        //   headerName: '市场情绪',
        //   field: 'sMarketSentiment',
        //   width: '100px',
        //   valueGetter: (params) => {
        //     return getCnDitc(params, this.emotionList, 'sMarketSentiment')
        //   }
        // },
        {
          headerName: '人员',
          field: 'vStaffName'
        },
        {
          headerName: '部门',
          field: 'vDepartmentName'
        },
        {
          headerName: '经营单位',
          field: 'vManagementName'
        },
        {
          headerName: '来源类型',
          field: 'sUpType',
          width: '160px',
          valueGetter: (params) => {
            return getCnDitc(params, this.sourceList, 'sUpType')
          }
        },

        {
          headerName: '来源',
          field: 'sourceCode',
          width: '160px'
        },
        {
          headerName: '创建人',
          field: 'sCreatorName'
        },
        {
          headerName: '创建时间',
          field: 'sCreateTime',
          cellStyle: { textAlign: 'right' },
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
          },
          width: '160px'
        },
        {
          headerName: '修改人',
          field: 'sModifierName'
        },
        {
          headerName: '修改时间',
          field: 'sModifyTime',
          cellStyle: { textAlign: 'right' },
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sModifyTime)
          },
          width: '160px'
        }
      ],
      rowData: [],
      selectId: '',
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      detailsId: null,
      sourceList: [],
      emotionList: []
    }
  },
  computed: {
    detailParams() {
      const sIds = []
      const selectIds = []
      this.rowData.forEach((item) => {
        sIds.push(item.sId)
        if (item && item._selected) {
          selectIds.push(item.sId)
        }
      })
      return {
        sIds: sIds.toString(),
        selectIds: selectIds.toString()
      }
    }
  },
  beforeCreate() {
    getDictet([
      'msb.merchants.logSourceType',
      'msb.merchants.emotion'
    ])
      .then((result) => {
        this.sourceList = result.data[0].dicts
        this.emotionList = result.data[1].dicts
      })
      .catch(() => {})
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    getMaintenance(detailsId) {
      var detailsIdShow = detailsId === 'view' ? this.selectId : detailsId
      if (!detailsIdShow) {
        this.$message.error('请勾选列表需要查看的项')
        return
      }
      this.$router.push({
        path: `/VisitRecordDetails/${detailsIdShow}`,
        query: {
          id: detailsIdShow,
          type: 'edit',
          name: '拜访记录详情',
          status: 70,
          activeId: localStorage.getItem('menuId')
        }
      })
    },
    onSearch(load = true) {
      this.$refs.aggrid.loadTableData()
      this.detailsId = null
    },

    onDefSelectfun(r) {
      console.log(r.data, '单选中')
      if (this.selectId === r.data.sId) {
        this.selectId = ''
      } else {
        this.selectId = r.data.sId
      }
    },
    // 点击 表格
    onRowDoubleClicked(params) {
      this.getMaintenance(params.data.sId)
    },
    handleFooterCount() {
      // this.$refs.aggrid.getSelectedData(res => {
      // })
    },
    filteringTime(time) {
      return time.replace('T', ' ')
    },
    // 主表
    loadData(pagination) {
      const formData = {
        ...this.$refs.searchForm.getSearchData(),
        sVisitDateFrom: this.$refs.searchForm.getSearchData().sVisitDateFrom
          ? this.filteringTime(this.$refs.searchForm.getSearchData().sVisitDateFrom) : '',
        sVisitDateTo: this.$refs.searchForm.getSearchData().sVisitDateTo
          ? this.filteringTime(this.$refs.searchForm.getSearchData().sVisitDateTo) : '',
        sCreateTimeFrom: this.$refs.searchForm.getSearchData().sCreateTimeFrom
          ? this.filteringTime(this.$refs.searchForm.getSearchData().sCreateTimeFrom) : '',
        sCreateTimeTo: this.$refs.searchForm.getSearchData().sCreateTimeTo
          ? this.filteringTime(this.$refs.searchForm.getSearchData().sCreateTimeTo) : ''
      }
      return new Promise((resolve, reject) => {
        this.rowData = []
        logList(formData, {
          ...pagination
        })
          .then((res) => {
            this.rowData = res.data.content.map((item) => {
              item._selected = false
              item._selectedKeys = []
              return item
            })
            resolve(res.data.totalElements)
          })
          .catch(() => {
            reject(0)
          })
      })
    }
  }
}
</script>
