<template>
  <!-- 拜访计划详情-->
  <div class="detail">
    <cnd-dialog :visible="true" @close="onClose">
      <template slot="content">
        <el-form
          ref="formvalue"
          class="el-form-w100"
          label-width="100px"
          :inline="true"
          :rules="formDataRules"
          :model="formData"
          size="small"
        >
          <cnd-form-card-list
            ref="cndFormCardList"
            :active-panel="activeCollapseName"
            :error-position="true"
          >
            <cnd-form-card :title="$t('grid.tabs.basicInformation')" name="1">
              <el-row>
                <cnd-form-item label="计划编码" prop="sVisitPlanCode">
                  <el-input v-model="formData.sVisitPlanCode" clearable :disabled="true" />
                </cnd-form-item>

                <cnd-form-item label="客商名称" prop="sCustomerArchivesName">
                  <el-input v-model="formData.sCustomerArchivesName" clearable :disabled="true" />
                </cnd-form-item>

                <cnd-form-item label="统一社会信用代码" prop="sCreditNo">
                  <el-input v-model="formData.sCreditNo" clearable :disabled="true" />
                </cnd-form-item>

                <cnd-form-item label="拜访日期" prop="sVisitDate">
                  <el-date-picker
                    v-model="formData.sVisitDate"
                    format="yyyy-MM-dd HH:mm:ss"
                    type="date"
                    :disabled="true"
                  />
                </cnd-form-item>

                <cnd-form-item label="人员" prop="vStaffName">
                  <el-input v-model="formData.vStaffName" clearable :disabled="true" />
                </cnd-form-item>

                <cnd-form-item label="来源类型" prop="sUpType">
                  <el-select v-model="formData.sUpType" disabled>
                    <el-option
                      v-for="item in selectOps['msb.merchants.planSourceType']"
                      :key="item.sCodeValue"
                      :label="item.sCodeName"
                      :value="item.sCodeValue"
                    />
                  </el-select>
                </cnd-form-item>

                <cnd-form-item label="来源" prop="sUpId">
                  <el-input v-model="formData.sUpId" clearable :disabled="true" />
                </cnd-form-item>

                <cnd-form-item label="部门" prop="vDepartmentName">
                  <el-input v-model="formData.vDepartmentName" clearable :disabled="true" />
                </cnd-form-item>

                <cnd-form-item label="经营单位" prop="vManagementName">
                  <el-input v-model="formData.vManagementName" clearable :disabled="true" />
                </cnd-form-item>

                <cnd-form-item label="拜访事项" prop="sVisitMatter" :custom-width="24">
                  <el-input
                    v-model="formData.sVisitMatter"
                    clearable
                    :disabled="true"
                    type="textarea"
                    :autosize="{ minRows: 8, maxRows: 5 }"
                  />
                </cnd-form-item>
              </el-row>
            </cnd-form-card>
          </cnd-form-card-list>

          <el-row type="flex" class="row-bg" justify="space-between">
            <el-col span="12">
              <div class="name-text">同行人</div>
              <div class="ht-35">
                <div class="layout-content auto-page-title flexV">
                  <steelTradeAggrid
                    ref="aggrid"
                    :column-defs="columnDefs"
                    :row-data="rowData1"
                    :load-data="loadDataStaff"
                    :auto-load-data="true"
                    table-selection
                    row-key="sId"
                    @selectedChange="handleFooterCount"
                  />
                </div>
              </div>
            </el-col>

            <el-col span="12">
              <div class="ml-6">
                <div class="name-text">邀请信息</div>
                <div class="ht-35">
                  <div class="layout-content auto-page-title flexV">
                    <steelTradeAggrid
                      ref="aggrid1"
                      :column-defs="columnDefs1"
                      :row-data="rowData"
                      :load-data="loadData"
                      :auto-load-data="true"
                      table-selection
                      row-key="sId"
                      @selectedChange="handleFooterCount"
                    />
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>

          <cnd-form-card-list
            ref="cndFormCardList"
            :active-panel="activeCollapseName"
            :error-position="true"
          >
            <cnd-form-card title="系统信息" name="1">
              <el-row>
                <cnd-form-item label="创建人" prop="sCreatorName">
                  <el-input v-model="formData.sCreatorName" clearable :disabled="true" />
                </cnd-form-item>

                <cnd-form-item label="创建时间" prop="sCreateTime">
                  <el-date-picker
                    v-model="formData.sCreateTime"
                    format="yyyy-MM-dd HH:mm:ss"
                    type="date"
                    :disabled="true"
                  />
                </cnd-form-item>

                <cnd-form-item label="修改人" prop="sModifierName">
                  <el-input v-model="formData.sModifierName" clearable :disabled="true" />
                </cnd-form-item>

                <cnd-form-item label="修改时间" prop="sModifyTime">
                  <el-date-picker
                    v-model="formData.sModifyTime"
                    format="yyyy-MM-dd HH:mm:ss"
                    type="date"
                    :disabled="true"
                  />
                </cnd-form-item>
              </el-row>
            </cnd-form-card>
          </cnd-form-card-list>
        </el-form>
      </template>
    </cnd-dialog>
  </div>
</template>

<script>
import { Moment } from 'cnd-utils'
import { planDetail, planStaff, planStaffLog } from '@/api/sysConfig/travellingMerchant'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import { getDictet } from '@/api/logistics/saleDelivery/saleorder'
import { getCnDitc } from '@/utils/common'

export default {
  name: 'RequirementViewing',
  components: { steelTradeAggrid },
  props: {
    width: {
      type: String,
      default: '700px'
    }
  },
  data() {
    return {
      selectOps: {
        'msb.merchants.planSourceType': []
      },
      columnDefs: [
        {
          field: 'vStaffName',
          headerName: '人员名称'
        },
        {
          field: 'vStaffCode',
          headerName: '人员编码',
          width: '160px'
        },
        {
          field: 'sCreateTime',
          headerName: '创建时间',
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
          },
          width: '160px'
        }
      ],
      columnDefs1: [
        {
          field: 'sSource',
          headerName: '操作方向',
          valueGetter: (params) => {
            return getCnDitc(params, this.sourceList, 'sSource')
          }
        },
        {
          field: 'vStaffName',
          headerName: '人员名称',
          width: '160px'
        },
        {
          field: 'vStaffCode',
          headerName: '人员编码',
          width: '160px'
        },
        {
          field: 'sCreateTime',
          headerName: '创建时间',
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
          },
          width: '160px'
        }
      ],
      rowData: [],
      rowData1: [],
      selectId: this.$route.query.id,
      activeCollapseName: ['1', '2'],
      formData: {
        sVisitPlanCode: '',
        sCustomerArchivesName: '',
        sCreditNo: '',
        sVisitDate: '',
        vStaffName: '',
        sUpType: '',
        sUpId: '',
        vDepartmentName: '',
        vManagementName: '',
        sVisitMatter: '',
        sCreatorName: '',
        sCreateTime: '',
        sModifierName: '',
        sModifyTime: ''
      },
      abnormalState: [],
      sourceList: [
        {
          sCodeValue: '10',
          sCodeName: '接受于',
          sSort: 911,
          sFilter: null,
          sIsEnabled: '11',
          sLanguage: 'zh_CN'
        },
        {
          sCodeValue: '20',
          sCodeName: '推送给',
          sSort: 912,
          sFilter: null,
          sIsEnabled: '11',
          sLanguage: 'zh_CN'
        }
      ],
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      }
    }
  },
  created() {
    if (this.selectId) {
      this.getDetail()
    }
  },
  beforeMount() {
    // 获取字典状态
    getDictet(['msb.merchants.planSourceType'])
      .then((result) => {
        this.selectOps['msb.merchants.planSourceType'] = result.data[0].dicts
      })
      .catch(() => {})
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    handleFooterCount() {
      // this.$refs.aggrid.getSelectedData(res => {
      // })
    },
    onSearch(load = true) {
      this.$refs.aggrid.loadTableData()
    },
    // 同行人
    loadDataStaff(pagination) {
      const sTopicId = {
        sVisitPlanId: this.selectId,
        sSort: 'DESC'
      }
      console.log(pagination, 'pagination')
      return new Promise((resolve, reject) => {
        planStaff(sTopicId, {
          ...pagination
        })
          .then((res) => {
            console.log(res, '/////')
            this.rowData1 = res.data.content.map((item) => {
              item._selected = false
              item._selectedKeys = []
              return item
            })
            resolve(res.data.totalElements)
            this.rowClicked({ data: res.data.content[0] })
          })
          .catch(() => {
            reject(0)
          })
      })
    },
    // 邀请信息
    loadData(pagination) {
      const sTopicId = {
        sVisitPlanId: this.selectId,
        sSort: 'DESC'
      }
      console.log(pagination, 'pagination')
      return new Promise((resolve, reject) => {
        planStaffLog(sTopicId, {
          ...pagination
        })
          .then((res) => {
            console.log(res, '/////')
            this.rowData = res.data.content.map((item) => {
              item._selected = false
              item._selectedKeys = []
              return item
            })
            resolve(res.data.totalElements)
            this.rowClicked({ data: res.data.content[0] })
          })
          .catch(() => {
            reject(0)
          })
      })
    },
    getDetail() {
      //  获取详情
      planDetail(this.selectId).then((res) => {
        this.formData = res.data || this.form
      })
    }
  }
}
</script>
<style scoped>
@import url('~@/styles/basisSelfCollectionSheet/detail.scss');
</style>
<style lang="scss" scoped>
.ht-35 {
  height: 30vh !important;
}
.auto-page-title {
  height: calc(100%) !important;
}
.detail {
  ::v-deep .dialog-body-position {
    top: 0 !important;
  }
}
.name-text {
  margin-top: 16px;
  height: 56px;
  line-height: 56px;
  background: #fff;
  padding-left: 10px;
}
.row-bg {
  margin-bottom: 20px;
}
.ml-6 {
  margin-left: 12px;
}
</style>
