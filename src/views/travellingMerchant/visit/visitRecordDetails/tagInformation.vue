<template>
  <div class="page-container tab">
    <div class="layout-content auto-page-title flexV">
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        table-selection
        @rowDoubleClicked="onRowDoubleClicked"
      />
    </div>
  </div>
</template>

<script>
import { Moment } from 'cnd-utils'
import businessMixin from '@/utils/businessMixin'
import mixins from '../../mixins'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import { logManList } from '@/api/sysConfig/travellingMerchant'
export default {
  name: 'VisitRecord',
  components: { steelTradeAggrid },
  mixins: [businessMixin, mixins],
  data() {
    return {
      selectId: this.$route.query.id,
      options: {
        'invoice.subtype': []
      },
      columnDefs: [
        {
          field: 'sSource',
          headerName: '操作方向',
          width: '120px',
          valueFormatter(params) {
            if (params.data.sSource === '10') {
              return '接受于'
            }
            if (params.data.sSource === '20') {
              return '推送给'
            }
            return ''
          }
        },
        {
          field: 'vStaffName',
          headerName: '人员名称',
          width: '140px'
        },
        {
          field: 'vStaffCode',
          headerName: '人员编码',
          width: '140px'
        },
        {
          field: 'sCreateTime',
          headerName: '推送时间',
          width: '140px',
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
          }
        }
      ],

      visible: false,
      rowData: [],
      rowDataChild: [],
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      deletingSid: [],
      vCurAmtArr: [],
      vCurAmtArrs: [],
      putShow: false,
      title: '新增',
      sId: '',
      vCurAmtArrEdit: []
    }
  },
  watch: {},
  created() {},
  mounted() {
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      this.$refs.aggrid.loadTableData()
    },
    // 主表
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        logManList(
          { sVisitRecordId: this.selectId },
          {
            ...pagination
          }
        )
          .then((res) => {
            this.rowData = res.data.content.map((item) => {
              item._selected = false
              item._selectedKeys = []
              return item
            })
            resolve(res.data.totalElements)
          })
          .catch(() => {
            reject(0)
          })
      })
    },
    // 点击 表格
    onRowDoubleClicked(params) {
      this.vCurAmtArrs = []
      this.vCurAmtArrs = [params.data]
    }
  }
}
</script>
<style lang="scss" scoped>
.form-box {
  padding: 10px;
}
.btn-box {
  padding: 12px 0;
  padding-left: 10px;
}
</style>
<style>
@import url('~@/styles/basisSelfCollectionSheet/detail.scss');
</style>
