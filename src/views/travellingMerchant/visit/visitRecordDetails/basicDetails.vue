<template>
  <!-- 基础信息 -->
  <div class="page-container detail">
    <el-form
      ref="form"
      class="el-form-w100"
      label-width="100px"
      :inline="true"
      :model="form"
      size="small"
    >
      <cnd-form-card-list :active-panel="activeCollapseName">
        <cnd-form-card :title="$t('grid.tabs.basicInformation')" name="1">
          <el-row>
            <cnd-form-item label="记录编码" prop="sRecordCode">
              <el-input v-model="form.sRecordCode" disabled />
            </cnd-form-item>
            <cnd-form-item label="客商名称" prop="sCustomerArchivesName">
              <el-input v-model="form.sCustomerArchivesName" disabled />
            </cnd-form-item>
            <cnd-form-item label="统一社会信用代码" prop="sCreditNo">
              <el-input v-model="form.sCreditNo" disabled />
            </cnd-form-item>
            <cnd-form-item label="拜访日期" prop="sVisitDate">
              <el-input v-model="form.sVisitDate" disabled />
            </cnd-form-item>

            <cnd-form-item label="市场情绪" prop="sMarketSentiment">
              <el-select v-model="form.sMarketSentiment" disabled>
                <el-option
                  v-for="item in selectOps['msb.merchants.emotion']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>

            <cnd-form-item label="来源类型" prop="sUpType">
              <el-select v-model="form.sUpType" disabled>
                <el-option
                  v-for="item in selectOps['msb.merchants.logSourceType']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item label="来源" prop="sourceCode">
              <el-input v-model="form.sourceCode" disabled />
            </cnd-form-item>

            <cnd-form-item label="人员" prop="vStaffName">
              <el-input v-model="form.vStaffName" clearable :disabled="true" />
            </cnd-form-item>

            <cnd-form-item label="部门" prop="vDepartmentName">
              <el-input v-model="form.vDepartmentName" disabled />
            </cnd-form-item>

            <cnd-form-item label="经营单位" prop="vManagementName">
              <el-input v-model="form.vManagementName" disabled />
            </cnd-form-item>
          </el-row>
        </cnd-form-card>
        <!-- 拜访内容列表 -->
        <cnd-form-card title="拜访内容列表" name="2">
          <div class="ht-35">
            <div class="layout-content auto-page-title flexV">
              <steelTradeAggrid
                ref="aggrid"
                :column-defs="columnDefs"
                :row-data="rowData"
                :load-data="loadData"
                :is-size-columns-to-fit="true"
                :auto-load-data="true"
                table-selection
                row-key="sId"
              />
            </div>
          </div>
        </cnd-form-card>

        <!-- 系统信息 -->
        <cnd-form-card :title="$t('grid.tabs.systemInformation')" name="3">
          <el-row>
            <cnd-form-item label="创建人" prop="sCreatorName">
              <el-input v-model="form.sCreatorName" disabled />
            </cnd-form-item>
            <cnd-form-item label="创建时间" prop="sCreateTime">
              <el-date-picker
                v-model="form.sCreateTime"
                format="yyyy-MM-dd HH:mm:ss"
                type="date"
                :disabled="true"
              />
            </cnd-form-item>
            <cnd-form-item label="修改人" prop="sModifierName">
              <el-input v-model="form.sModifierName" disabled />
            </cnd-form-item>
            <cnd-form-item label="修改时间" prop="sModifyTime">
              <el-date-picker
                v-model="form.sModifyTime"
                format="yyyy-MM-dd HH:mm:ss"
                type="date"
                :disabled="true"
              />
            </cnd-form-item>
          </el-row>
        </cnd-form-card>
      </cnd-form-card-list>
    </el-form>
    <formDialog
      :visible="visible"
      :form-items="rowDataChild"
      width="500px"
      height="300px"
      title="拜访内容"
      @close="visible = false"
    />
  </div>
</template>

<script>
import businessMixin from '@/utils/businessMixin'
import mixins from '../../mixins'
import { logDetail, logContentList } from '@/api/sysConfig/travellingMerchant'
import { getCnDitc } from '@/utils/common'
import { getDictet } from '@/api/logistics/saleDelivery/saleorder'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import formDialog from '@/components/formDialog'
export default {
  name: 'VisitRecord',
  components: { steelTradeAggrid, formDialog },
  mixins: [businessMixin, mixins],
  data() {
    return {
      selectId: this.$route.query.id,
      dialogVisible: {
        annex: false, // 附件管理
        processQuery: false, // 进程查询
        approval: false, // 审批情况
        approvalN8: false
      },
      selectOps: {
        'msb.merchants.logSourceType': [],
        'msb.merchants.emotion': [],
        'msb.merchants.personalTopic': []
      },
      form: {
        sRecordCode: '',
        sCustomerArchivesName: '',
        sCreditNo: '',
        sVisitDate: '',
        sMarketSentiment: '',
        sUpType: '',
        sUpId: '',
        vStaffName: '',
        vDepartmentName: '',
        vManagementName: '',
        sCreatorName: '',
        sCreateTime: '',
        sModifierName: '',
        sModifyTime: '',
        msbCustomerVisitRecordContentVoList: []
      },
      columnDefs: [
        {
          field: 'sTopicTypeName',
          headerName: '类型',
          width: '120px'
        },
        {
          field: 'sTopicContent',
          headerName: '内容',
          width: '500px',
          valueGetter: (params) => {
            const list = JSON.parse(params.data.sTopicContent)

            if (Array.isArray(list)) {
              return '详情内容：' + list[0] + '；' + list[1] + '；其他信息：' + list[2]
            } else {
              return list
            }
          }
        },
        {
          field: '',
          headerName: '操作',
          width: '140px',
          cellStyle: { textAlign: 'center' },
          onCellClicked: (params) => {
            this.openDetail(params)
          },
          cellRenderer: (params) => {
            const sHtml = '<a style="color: #409EFF">查看</a>'
            return sHtml
          }
          // cellRendererFramework: 'totalValueRenderer'
        }
      ],
      activeCollapseName: ['1', '2', '3', '4', '5'],
      visible: false,
      rowData: [],
      rowDataChild: [],
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      }
    }
  },
  watch: {},
  mounted() {
    this.onSearch(false)
  },
  beforeMount() {
    // 获取字典状态
    getDictet(['msb.merchants.logSourceType', 'msb.merchants.emotion', 'msb.merchants.personalTopic', 'msb.merchants.publicTopics'])
      .then((result) => {
        this.selectOps['msb.merchants.logSourceType'] = result.data[0].dicts
        this.selectOps['msb.merchants.emotion'] = result.data[1].dicts
        this.selectOps['msb.merchants.personalTopic'] = [{ sCodeName: '待办', sCodeValue: '10' }].concat(
          result.data[2].dicts
        ).concat(
          result.data[3].dicts
        )
      })
      .catch(() => {})
  },
  created() {
    if (this.selectId) {
      this.getDetail()
    }
  },
  methods: {
    onSearch(load = true) {
      this.$refs.aggrid.loadTableData()
    },
    // 主表
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        logContentList(
          { sVisitRecordId: this.selectId },
          {
            ...pagination
          }
        )
          .then((res) => {
            this.rowData = res.data.content.map((item) => {
              item._selected = false
              item._selectedKeys = []
              return item
            })
            resolve(res.data.totalElements)
          })
          .catch(() => {
            reject(0)
          })
      })
    },
    getDetail() {
      //  获取详情
      logDetail(this.selectId).then((res) => {
        this.form = res.data
      })
    },
    // 审批撤回
    openDetail(params) {
      console.log('打开详情', params)
      params.data.sTopicTypeName = getCnDitc(params, this.selectOps['msb.merchants.personalTopic'], 'sTopicType')
      const list = JSON.parse(params.data.sTopicContent)
      let content = ''
      if (Array.isArray(list)) {
        content = '详情内容：' + list[0] + '\n' + list[1] + '\n其他信息：' + list[2]
      } else {
        content = list
      }
      this.rowDataChild = [
        {
          label: '类型',
          value: 'sTopicTypeName',
          type: 'elInput',
          disabled: true,
          default: params.data.sTopicTypeName,
          customWidth: 22
        },
        {
          label: '内容',
          value: 'sTopicContent',
          type: 'elInputTextArea',
          disabled: true,
          default: content,
          rows: 5,
          customWidth: 22
        }
      ]
      this.visible = true
    }
  }
}
</script>
<style lang="scss" scoped>
.ht-35 {
  height: 42vh !important;
}
.auto-page-title {
  height: calc(100%) !important;
}
.row-bg {
  margin-bottom: 20px;
}
.dialog-img {
  display: flex;
  flex-wrap: wrap;
}
</style>
