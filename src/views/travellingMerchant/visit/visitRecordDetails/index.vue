<template>
  <!-- 拜访记录详情-->
  <div class="page-container detail">
    <cnd-dialog :visible="true" @close="onClose">
      <template slot="leftBtn">
        <el-button type="primary" size="mini" @click="dialogVisible.approval = true">附件管理</el-button>
      </template>
      <template slot="content">
        <el-tabs v-model="activeName">
          <el-tab-pane label="基础信息" name="shipmentCommodity">
            <basicDetails />
          </el-tab-pane>
          <el-tab-pane label="推送消息" name="tagInformation">
            <tagInformation v-if="activeName == 'tagInformation'" />
          </el-tab-pane>
        </el-tabs>
      </template>
    </cnd-dialog>
    <steel-annex-dialog
      :visible="dialogVisible.approval"
      append-to-body
      :biz-id="selectId"
      :disabled-btn="{
        scan: true,
        del: true
      }"
      @onSelect="dialogVisible.approval = false"
    />
  </div>
</template>

<script>
import businessMixin from '@/utils/businessMixin'
import mixins from '../../mixins'
import { mscsteelmillsresourceGet } from '@/api/steelInfoManage/steelPlantResources.js'
import { getDictet } from '@/api/logistics/saleDelivery/saleorder'

import basicDetails from './basicDetails'
import tagInformation from './tagInformation'
export default {
  name: 'VisitRecord',
  components: { basicDetails, tagInformation },
  mixins: [businessMixin, mixins],
  data() {
    return {
      selectId: this.$route.query.id,

      dialogVisible: {
        annex: false, // 附件管理
        processQuery: false, // 进程查询
        approval: false, // 审批情况
        approvalN8: false
      },
      selectOps: {
        'msc.steelmill.status': []
      },
      activeName: 'shipmentCommodity',
      form: {},
      activeCollapseName: ['1', '2'],
      visible: false,
      disRemove: true,
      srcList: []
    }
  },
  watch: {},
  beforeMount() {
    // 获取字典状态
    getDictet(['msc.steelmill.status'])
      .then((result) => {
        this.selectOps['msc.steelmill.status'] = result.data[0].dicts
      })
      .catch(() => {})
  },
  created() {
    if (this.selectId) {
      this.getDetail()
    }
  },
  methods: {
    getDetail() {
      //  获取详情
      mscsteelmillsresourceGet(this.selectId).then((res) => {
        this.form = res.data || this.form

        this.form.tImages.forEach((sImg) => {
          if (sImg.url) {
            this.srcList.push(sImg.url)
          }
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-img {
  display: flex;
  flex-wrap: wrap;
}
::v-deep .flexCB {
  //   z-index: 999 !important;
}
::v-deep .is-fullscreen .form-card-list-container .el-collapse-item {
  margin-right: 0 !important;
  margin-left: 0 !important;
}
</style>
