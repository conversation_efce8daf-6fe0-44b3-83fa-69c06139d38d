<template>
  <div class="page-container">
    <cnd-dialog :title="$t('grid.tabs.basicInformation')" :visible="true" @close="onClose">
      <template slot="leftBtn">
        <span>
          <el-button type="primary" size="mini" @click="save">保存</el-button>
          <el-button type="danger" size="mini" :disabled="!id" @click="remove">删除</el-button>
        </span>
      </template>
      <template slot="content">
        <el-form
          ref="form"
          class="el-form-w100"
          label-width="90px"
          inline
          :model="form"
          :rules="rules"
          size="small"
        >
          <cnd-form-card-list :active-panel="activeCollapseName">
            <cnd-form-card class="mb-10" title="基础信息" name="1">
              <el-row>
                <cnd-form-item
                  label="周报填写人"
                  prop="sWriterStaffName"
                  :error-msg="rules.sWriterStaffName[0].message"
                >
                  <horizon-search-select-item
                    v-model="form.sWriterStaffName"
                    default-url="/staff/info/page-tree-orgid"
                    search-key="sCnName"
                    @change="changeSelect2"
                  />
                </cnd-form-item>
                <cnd-form-item label="填写人编码" prop="sWriterStaffCode">
                  <el-input v-model="form.sWriterStaffCode" :disabled="true" />
                </cnd-form-item>
                <cnd-form-item
                  label="是否提醒"
                  prop="sIsReminded"
                  :error-msg="rules.sIsReminded[0].message"
                >
                  <el-select v-model="form.sIsReminded">
                    <el-option
                      v-for="item in isRemindDictList"
                      :key="item.sCodeValue"
                      :label="item.sCodeName"
                      :value="item.sCodeValue"
                    />
                  </el-select>
                </cnd-form-item>
              </el-row>

              <div class="btn-group mt-20">
                <div class="text">列表</div>
                <div>
                  <el-button type="primary" size="mini" @click="getAdd">新增</el-button>
                  <el-button
                    :disabled="!selectedRowData.length"
                    type="danger"
                    size="mini"
                    @click="delListUser"
                  >删除</el-button>
                </div>
              </div>
              <div class="layoutContent">
                <steelTradeAggrid
                  ref="aggrid"
                  class="layoutContent"
                  :column-defs="columnDefs"
                  :row-data="rowData"
                  :load-data="loadData"
                  :auto-load-data="false"
                  table-selection="multiple"
                  :paginationinif="false"
                  @rowSelected="onDefSelectfun"
                  @headerSelected="onHeaderDefSelect"
                />
              </div>
            </cnd-form-card>

            <cnd-form-card class="mb-10" :title="$t('grid.tabs.systemInformation')" name="3">
              <el-row>
                <cnd-form-item :label="$t('grid.title.createdBy')" prop="sCreatorName">
                  <el-input v-model="form.sCreatorName" disabled clearable />
                </cnd-form-item>
                <cnd-form-item :label="$t('grid.title.createdAt')" prop="sCreateTime">
                  <el-date-picker
                    v-model="form.sCreateTime"
                    format="yyyy-MM-dd HH:mm:ss"
                    type="date"
                    disabled
                    :placeholder="$t('grid.title.createdAt')"
                  />
                </cnd-form-item>
                <cnd-form-item :label="$t('grid.title.modifiedBy')" prop="sModifierName">
                  <el-input v-model="form.sModifierName" disabled clearable />
                </cnd-form-item>
                <cnd-form-item :label="$t('grid.title.modifiedAt')" prop="sModifyTime">
                  <el-date-picker
                    v-model="form.sModifyTime"
                    format="yyyy-MM-dd HH:mm:ss"
                    type="date"
                    disabled
                    :placeholder="$t('grid.title.modifiedAt')"
                  />
                </cnd-form-item>
              </el-row>
            </cnd-form-card>
          </cnd-form-card-list>
        </el-form>
      </template>
    </cnd-dialog>
    <horizon-staff-dialog
      :append-to-body="true"
      org-type="100"
      :visible="depart"
      @onSelect="onStaffSelect"
    />
  </div>
</template>

<script>
import weeklyReportApi from '@/api/sysConfig/weeklyReportConfiguration'
import { salesAreaPage } from '@/api/steelInfoManage/steelMillMessageMaintain.js'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import { DictUtil, MessageUtil } from 'cnd-horizon-utils'

export default {
  name: 'CustomerRecipientDetail',
  components: { steelTradeAggrid },
  data() {
    return {
      params: {},
      id: '',
      formDisabled: true,
      activeCollapseName: ['1', '2', '3'],
      form: {
        sWriterStaffName: '',
        sWriterStaffId: '',
        sWriterStaffCode: '',
        sIsReminded: ''
      },
      rules: {
        sWriterStaffName: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        sIsReminded: [{ required: true, message: '必填', trigger: 'change' }]
      },
      isRemindDictList: [],
      statusList: [],
      dialogVisible: {
        approval: false,
        annex: false
      },
      selectedRowData: [],
      columnDefs: [
        {
          field: 'sViewedStaffName',
          headerName: '周报查阅人',
          width: '120px'
        }
        // {
        //   field: 'sViewedStaffCode',
        //   headerName: '周报查阅人编码',
        //   width: '140px'
        // }
      ],
      rowData: [],
      rowDataChild: [],
      sWriterStaffId: '',
      depart: false,
      reqConfig: {
        // url: '/org/busi/tree',
        // method: 'get',
        url: '/staff/info/tree-orgid/0/20',
        method: 'post',
        treeProps: {
          label: 'name'
          // disabled: (data, node) => {
          //   return data.path && data.path.indexOf('129111111111194') === -1
          // }
        }
      }
    }
  },
  computed: {
    // statusDisabled() {
    //   return this.form.sStatus === '1'
    // }
  },
  mounted() {},
  created() {
    this.param = this.$route.query
    this.id = this.param.id
    if (this.id) {
      this.getWeekReportConfigDetails()
    }
    this.loadDict()
  },
  methods: {
    loadDict() {
      DictUtil.getDict([
        'base.yes-no'
      ], res => {
        this.isRemindDictList = res[0].dicts
      })
    },
    onHeaderDefSelect(value, e) {
      this.onDefSelectfun()
    },
    onDefSelectfun() {
      this.$refs.aggrid.getSelectedData(selected => {
        this.selectedRowData = selected
      })
    },
    // 获取周报配置详情
    getWeekReportConfigDetails() {
      weeklyReportApi.getWeekReportConfigDetails(this.id).then(res => {
        this.form = res.data
        this.rowData = res.data.staffVos || []
      })
    },
    getAdd() {
      this.depart = true
    },
    delListUser() {
      console.log(this.rowData, this.selectedRowData)
      this.rowData = this.rowData.filter(item => {
        return !this.selectedRowData.find(i => i.sViewedStaffId === item.sViewedStaffId)
      })
    },
    handleChangeSelect(val, FieldName) {
      console.log(val)
      // this.form[FieldName] = val ? val.name : undefined
    },
    // 查阅人选择
    changeSelect2(e) {
      console.log('🚀 ~ changeSelect2 ~ e:', e)
      this.form.sWriterStaffCode = e.sCode
      this.form.sWriterStaffId = e.sId
      this.form.sWriterStaffName = e.sCnName
    },
    // 主表
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        salesAreaPage(
          { sWriterStaffId: this.sWriterStaffId },
          {
            ...pagination
          }
        )
          .then((res) => {
            this.rowData = res.data.content.map((item) => {
              item._selected = false
              item._selectedKeys = []
              return item
            })
            resolve(res.data.totalElements)
          })
          .catch(() => {
            reject(0)
          })
      })
    },
    save() {
      this.$refs.form.validate(valid => {
        if (valid) {
          console.log('`````````this.rowData`````````', this.rowData)
          this.form.staffVos = this.rowData
          weeklyReportApi.addWeekReportConfig(this.form).then(res => {
            this.$message.success('保存成功')
            this.$tabDelete(
              `/egl/weeklyReportConfiguration`
            )
            this.loadDetail()
          })
        }
      })
    },
    remove() {
      weeklyReportApi.removesWeeklyList(
        [this.id]
      ).then(res => {
        this.$message.success('删除成功')
        this.$tabDelete(
          `/egl/AddWeekly/${res.data.sId}?id=${res.data.sId}&status=${res.data.sStatus}&type=edit&name=周报配置详情【${res.data.sId}】&activeId=${localStorage.getItem('menuId')}&random=${Math.random()}`
        )
      })
    },
    // 人员选择
    onStaffSelect(select) {
      if (!select || select.type === 'click') {
        // 兼容：关闭会触发此回调，且入参是Event
        this.depart = false
        return
      }
      console.log('select', select)
      const currentUserId = select[0]?.children[0].id
      if (this.rowData.find(item => item.sViewedStaffId === currentUserId)) {
        MessageUtil.warning('周报查阅人不能重复添加')
        return
      }

      if (select && select.length && select[0]?.children.length) {
        this.rowData.push({
          sViewedStaffId: currentUserId, // 周报查阅人
          sViewedStaffName: select[0]?.children[0].name,
          sViewedStaffCode: '',
          sCompanyId: '',
          sDepartmentId: select[0].id, // 部门
          sManagementId: select[0].parentId, // 经营单位
          sCheckGroupId: '',
          _selected: false,
          _selectedKeys: []
        })
      }
      this.depart = false
    }
  }
}
</script>
<style>
.layoutContent {
  height: 480px;
}
@import url('~@/styles/basisSelfCollectionSheet/detail.scss');
</style>
