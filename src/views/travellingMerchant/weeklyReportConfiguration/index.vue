<template>
  <!-- 页面第一层，必配page-container类 -->
  <div class="page-container staffing-list">
    <!-- 页面标题层（page-title） -->
    <p class="page-title">周报配置</p>
    <!-- 页面内容层（auto-page-title） -->
    <div class="auto-page-title layout-content flexV">
      <cnd-search-form ref="searchForm" :form-items="searchItems" @search="onSearch" />

      <div class="btn-group mt-10">
        <div class="text">列表</div>
        <div>
          <el-button type="primary" size="mini" @click="showAdd('周报未提交提醒设置', null)">提醒时间配置</el-button>

          <el-button type="primary" size="mini" @click="isImport = true">导入</el-button>

          <el-button type="primary" size="mini" @click="getAdd">新增</el-button>

          <el-button type="primary" size="mini" @click="getMaintenance('view')">编辑</el-button>

          <el-button type="danger" size="mini" :disabled="delDisable" @click="getDel">删除</el-button>
        </div>
      </div>

      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        row-key="sId"
        table-selection="multiple"
        @selectedChange="handleSelectedChange"
        @rowDoubleClicked="onRowDoubleClicked"
        @rowSelected="onDefSelectfun"
      />
    </div>

    <!-- 弹框 -->
    <cnd-dialog
      :visible="isPopup"
      class="addPopup"
      :fullscreen="false"
      :title="$t(popupTitle)"
      width="450px"
      height="fit-content"
      @close="closePopup"
    >
      <template slot="content">
        <div class="item">
          <el-form ref="ruleForm" :model="formInfo" :rules="nameRules" label-width="100px">
            <cnd-form-item label="提醒设置" prop="week" :custom-width="100">
              <el-select v-model="formInfo.week" filterable size="mini">
                <el-option
                  v-for="option in selectOps['msb.merchants.weeks']"
                  :key="option.sCodeValue"
                  :label="option.sCodeName"
                  :value="option.sCodeValue"
                />
              </el-select>
            </cnd-form-item>

            <cnd-form-item label="周报提醒时间" prop="time" :custom-width="100">
              <el-time-select
                v-model="formInfo.time"
                size="mini"
                format="HH:mm"
                :picker-options="{
                  start: '00:00',
                  step: '00:10',
                  end:'23:59'
                }"
                placeholder="请选择时间"
                style="width: 100%"
              />
            </cnd-form-item>
          </el-form>
        </div>
      </template>
      <template slot="footer">
        <el-button size="mini" @click="closePopup">取消</el-button>
        <el-button type="primary" size="mini" @click="submit('ruleForm')">保存</el-button>
      </template>
    </cnd-dialog>

    <!-- 导入 -->
    <cnd-dialog
      :visible="isImport"
      class="importPopup"
      :fullscreen="false"
      title="导入"
      width="380px"
      height="fit-content"
      @close="isImport = false"
    >
      <template slot="content">
        <el-upload
          class="upload-demo"
          drag
          action="/api/weeklyreport/msbcustomerweeklyreportsettings/import"
          :show-file-list="false"
          :headers="importHeaders"
          :on-error="uploadError"
          :on-progress="uploadProgress"
          :on-success="uploadSuccess"
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">请选择.xlsx或.xls文件，每次只能导入一个文件</div>
        </el-upload>
      </template>
      <template slot="footer">
        <el-button size="mini" @click="getExcelTemplate">下载模板</el-button>
        <el-button size="mini" @click="isImport = false">取消</el-button>
      </template>
    </cnd-dialog>
  </div>
</template>

<script>
import { GridPageMixin } from '@/utils/page-mixins'
import { request } from 'cnd-horizon-utils'
import weeklyReportApi from '@/api/sysConfig/weeklyReportConfiguration'
import exportBtn from '@/components/exportBtnV2'
import { getToken } from 'cnd-horizon-utils/src/utils/auth'
import axios from 'axios'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import { Loading } from 'element-ui'
import {
  getCnDitc,
  getDictet
} from '@/api/logistics/saleDelivery/saleorder'

export default {
  // name: 'WeeklyReportConfiguration',
  components: {
    exportBtn,
    steelTradeAggrid
  },
  mixins: [GridPageMixin],
  data() {
    return {
      value2: new Date(2016, 9, 10, 18, 40),
      searchItems: [{
        label: '周报填写人',
        value: 'sWriterStaffId',
        type: 'cndInputDialogItem',
        defaultUrl: '/msc/foundation/staff/page/admin',
        option: {
          valueKey: 'sPath'
        },
        dialogType: 'staff'
      }, {
        label: '周报查阅人',
        value: 'sViewedStaffId',
        type: 'cndInputDialogItem',
        defaultUrl: '/msc/foundation/staff/page/admin',
        option: {
          valueKey: 'sPath'
        },
        dialogType: 'staff'
      }, {
        label: '是否提醒',
        type: 'elSelect',
        value: 'sIsReminded',
        dict: []
      }],
      searchInfo: {},
      formCard: '1',
      modifyPasdiaVisible: false,
      sizeToFit: 'nofit',
      activeTabName: 'tabRole',
      openText: this.$t('btns.enable'),
      userParam: {
        info: '',
        enable: '',
        accountLocked: ''
      },
      curUserId: '',
      // 分页配置
      pagination: {
        pageNo: 0,
        pageSize: 20,
        total: 0
      },
      nameRules: {
        week: [
          {
            required: true,
            message: '提醒设置不能为空',
            trigger: ['blur', 'change']
          }
        ],
        time: [
          {
            required: true,
            message: '周报提醒时间不能为空',
            trigger: ['blur', 'change']
          }
        ]
      },
      formInfo: {
        week: '',
        time: ''
      },
      isPopup: false,
      popupTitle: '周报未提交提醒设置',
      depList: null,
      depart: false,

      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      gridApi: null,
      columnApi: null,
      // 导入
      isImport: false,
      excelTemplate: '',
      importHeaders: {
        Authorization: getToken()
      },
      rowData: [],
      selectOps: {
        'base.yes-no': [],
        'msb.merchants.weeks': []
      },
      columnDefs: [
        {
          field: 'sWriterStaffName',
          headerName: '周报填写人',
          minWidth: 150
        },
        {
          field: 'viewedStaffNames',
          headerName: '周报查阅人',
          minWidth: 150
        },
        {
          field: 'sIsReminded',
          headerName: '是否提醒',
          minWidth: 100,
          valueGetter: params => {
            return getCnDitc(params, this.selectOps['base.yes-no'], 'sIsReminded')
          }
        },

        {
          field: 'sCreator',
          headerName: '创建人',
          minWidth: 100
        },
        {
          field: 'sCreateTime',
          headerName: '创建时间',
          minWidth: 160,
          valueFormatter: (params) => {
            return params.value.replace(/T/g, ' ')
          }
        },
        {
          field: 'sModifier',
          headerName: '修改人',
          minWidth: 100
        },
        {
          field: 'sModifyTime',
          headerName: '修改时间',
          minWidth: 160,
          valueFormatter: (params) => {
            return params.value.replace(/T/g, ' ')
          }
        }
      ],
      delDisable: true,
      detailsId: ''
    }
  },
  watch: {},
  created() {
    getDictet([
      'base.yes-no',
      'msb.merchants.weeks'
    ]).then(result => {
      this.selectOps['base.yes-no'] = result.data[0].dicts
      this.selectOps['msb.merchants.weeks'] = result.data[1].dicts

      const target = this.searchItems.find(i => i.value === 'sIsReminded')
      if (target) {
        target.dict = this.selectOps['base.yes-no']
      }
    }).catch(() => {
    })
  },
  mounted() {
  },
  methods: {
    onGridReady(params) {
      console.log('1params', params)
      this.gridApi = params.api
      this.columnApi = params.columnApi
      this.gridApi.sizeColumnsToFit()
    },
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    handleSelectedChange(rowData) {
      this.$refs.aggrid.getSelectedData(res => {
        if (res.length > 0) {
          this.delDisable = false
        } else {
          this.delDisable = true
        }
      })
    },
    // 查询
    loadData(pagination = { page: 0, limit: 30 }) {
      return new Promise((resolve, reject) => {
        weeklyReportApi.getWeekReportConfigList(
          this.searchInfo,
          pagination
        ).then(res => {
          this.rowData = res.data.content.map((item, index) => {
            item.index = index + 1
            item._selected = false
            item._selectedKeys = []
            return item
          })
          resolve(res.data)
        })
      })
    },
    showAdd(title) {
      // this.formItems[1].disabled = false
      this.isPopup = true
      this.popupTitle = title
      weeklyReportApi.getWeekReportReminder().then(res => {
        this.formInfo.week = res.data.find(item => item.code === 'WEEK_SET').value
        this.formInfo.time = res.data.find(item => item.code === 'TIME_SET').value
      })
    },

    deleteUser() {
      this.$refs.aggrid.getSelectedData(res => {
        const list = res.map(item => item.sId)
        this.$confirm(this.$t('tips.isItOkToDelete'), this.$t('grid.others.prompt'), {
          confirmButtonText: this.$t('btns.confirm'),
          cancelButtonText: this.$t('btns.cancel'),
          type: 'info'
        })
          .then(() => {
            request({
              url: '/msb/userinfo/msbuserinfo/removes',
              method: 'delete', // 请求方法
              data: list
            }).then(res => {
              this.$message({
                message: this.$t('tips.deletedSuccessfully'),
                type: 'success'
              })
              console.log('!!!!', res)
              this.$refs.aggrid.reloadTableData()
            })
          })
      })
    },
    closePopup() {
      this.isPopup = false
      this.formInfo = {
        week: '',
        time: ''
      }
      this.$refs['ruleForm'].resetFields()
    },

    submit(formName) {
      this.$refs[formName].validate((valid) => {
        if (!valid) {
          return
        }
        if (this.popupTitle === '周报未提交提醒设置') {
          weeklyReportApi.addWeekReportReminder(this.formInfo).then(res => {
            this.closePopup()
            this.$refs.aggrid.loadTableData()
          })
          return
        }
      })
    },

    // 获取excel模板
    getExcelTemplate() {
      const loadingInstance = Loading.service({
        fullscreen: true,
        text: '下载中，请稍后...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.8)'
      })
      axios.get(
        process.env.VUE_APP_BASE_API + '/weeklyreport/msbcustomerweeklyreportsettings/template',
        {
          responseType: 'arraybuffer',
          headers: {
            Authorization: getToken()
          }
        }
      ).then((res) => {
        const blob = new Blob([res.data])
        const fileName = '周报配置模板.xls'
        if ('download' in document.createElement('a')) {
          // 非IE下载
          const elink = document.createElement('a')
          elink.download = fileName
          elink.style.display = 'none'
          elink.href = URL.createObjectURL(blob)
          document.body.appendChild(elink)
          elink.click()
          URL.revokeObjectURL(elink.href) // 释放URL 对象
          document.body.removeChild(elink)
        } else {
          // IE10+下载
          navigator.msSaveBlob(blob, fileName)
        }
      }).catch(err => {
        this.$message({
          message: err,
          type: 'error'
        })
      }).finally(() => {
        loadingInstance.close()
      })
    },
    uploadError(err) {
      this.$message({
        message: err,
        type: 'error'
      })
    },
    uploadProgress() {
      this.loadingInstance = Loading.service({
        fullscreen: true,
        text: this.$t('grid.others.dataImportingPleaseWait'),
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.8)'
      })
    },
    uploadSuccess(res) {
      console.log('!!!', res)
      if (res.code === '0000') {
        if (res.data && res.data.errorMesage && res.data.errorMesage.length) {
          let err = ''
          res.data?.errorMesage.forEach((element, index) => {
            err += element + (index < res.data.errorMesage.length - 1 ? '；' : '')
          })

          this.$message({
            message: err,
            type: 'error'
          })
        } else {
          this.isImport = false
          this.loadData()
        }
      } else {
        this.$message({
          message: res.message,
          type: 'error'
        })
      }
      this.loadingInstance.close()
    },
    // 点击 表格
    onRowDoubleClicked(params) {
      // this.detailsId = params.data.sId
      this.getMaintenance(params.data.sId)
      console.log(params.data, '双击')
    },
    onDefSelectfun(r) {
      console.log(r.data, '单选中')
      if (this.detailsId === r.data.sId) {
        this.detailsId = ''
      } else {
        this.unitary = r.data
        this.detailsId = r.data.sId
      }
    },
    getAdd() {
      this.$router.push({
        path: `/AddWeekly/add`,
        query: {
          type: 'add',
          name: '周报配置详情',
          status: 70
        }
      })
    },
    getMaintenance(detailsId) {
      var detailsIdShow
      if (detailsId === 'view') {
        detailsIdShow = this.detailsId
        sessionStorage.setItem('BASICS', JSON.stringify(this.unitary))
      } else {
        detailsIdShow = detailsId
      }

      // var detailsIdShow = this.detailsId || detailsId

      if (detailsId === 'view') {
        console.log(this.detailsId, 'this.detailsId')
        if (!this.detailsId) {
          this.$message.error('请勾选列表需要编辑的项')
          return
        }
      } else {
        if (!detailsIdShow) {
          this.$message.error('请勾选列表需要编辑的项')
          return
        }
      }
      this.$router.push({
        path: `/AddWeekly/${detailsIdShow}`,
        query: {
          id: detailsIdShow,
          type: 'edit',
          name: '周报配置详情',
          status: 70,
          activeId: localStorage.getItem('menuId')
        }
      })
    },
    getDel() {
      this.$refs.aggrid.getSelectedData(res => {
        const list = res.map(item => item.sId)
        weeklyReportApi.removesWeeklyList(
          list
        ).then(res => {
          this.$message.success('删除成功')
          this.loadData()
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped></style>
