<template>
  <!-- 准入申请 -->
  <div class="page-container">
    <p class="page-title">准入申请</p>
    <div class="layout-content auto-page-title flexV">
      <cnd-search-form
        ref="searchForm"
        :form-items="formItems"
        @search="onSearch"
        @searchValue="onSearchValue"
      />
      <div class="btn-group mt-10">
        <div class="text">列表</div>
        <el-button size="mini" type="primary" @click="getMaintenance('view')">查看</el-button>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        table-selection="single"
        @rowClicked="rowClicked"
        @selectedChange="handleFooterCount"
        @rowDoubleClicked="onRowDoubleClicked"
        @rowSelected="onDefSelectfun"
      />
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import { Moment } from 'cnd-utils'
import { getCnDitc } from '@/utils/common'
import { getDictet } from '@/api/logistics/saleDelivery/saleorder'
import { accessList } from '@/api/sysConfig/travellingMerchant'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import exportBtn from '@/components/exportBtnV2'
import mixins from '../mixins'
export default {
  name: 'BusinessPartnerProfile',
  components: { steelTradeAggrid, exportBtn },
  mixins: [mixins],
  data() {
    const startDate = moment()
      .subtract(90, 'day')
      .startOf('day')
      .format('YYYY-MM-DDTHH:mm:ss')
    const endDate = moment().endOf('day').format('YYYY-MM-DDTHH:mm:ss')
    return {
      options: {
        'msb.merchants.TypeAccess': [],
        'msb.merchants.sheet.status': [],
        'base.yes-no': [],
        'customer.nature': []
      },
      searchInfo: null,
      formItems: [
        {
          label: '申请单号',
          value: 'sApplyNo',
          type: 'elInput'
        },
        {
          label: '客商名称',
          // value: 'sSupplierId',
          // type: 'cndInputDialogItem',
          // dialogType: 'customer',
          // defaultUrl: '/esc/customer/page',
          // option: { valueKey: 'sPath' },
          // customerType: '40'
          // value: 'sCustomerName',
          value: 'sCustomerId',
          type: 'cndInputDialogItem',
          dialogType: 'customer',
          defaultUrl: '/esc/customer/page',
          option: { valueKey: 'sPath' },
          customerType: null
        },
        {
          label: '状态',
          value: 'sSheetStatus',
          type: 'elSelect',
          dict: 'msb.merchants.sheet.status'
        },
        {
          label: '准入类型',
          value: 'sType',
          type: 'elSelect',
          dict: 'msb.merchants.TypeAccess'
        },
        {
          // 人员
          label: this.$t('grid.title.personnel'),
          value: 'sStaffId',
          type: 'cndInputDialogItem',
          option: { valueKey: 'sPath' },
          dialogType: 'staff',
          // 请选择人员
          placeholder: this.$t('grid.others.pleaseSelectPersonnel')
        },
        {
          label: '统一社会信用代码',
          value: 'sCreditNo',
          type: 'elInput',
          width: '180px'
        },
        {
          label: '申请日期',
          value: ['sApplyStartTime', 'sApplyEndTime'],
          placeholder: [
            this.$t('grid.others.pleaseSelectTheDate'),
            this.$t('grid.others.pleaseSelectTheDate')
          ],
          default: ['', ''],
          unlinkPanels: true,
          type: 'elDatePicker',
          itemType: 'occultation'
        },
        {
          label: '创建人',
          value: 'sCreator',
          type: 'cndInputDialog',
          itemType: 'occultation',
          placeholder: this.$t('grid.others.pleaseSelectTheCreator'),
          dialogType: 'applicant'
        },
        {
          label: '创建时间',
          value: ['sCreateStartTime', 'sCreateEndTime'],
          placeholder: [
            this.$t('grid.others.pleaseSelectTheDate'),
            this.$t('grid.others.pleaseSelectTheDate')
          ],
          default: [startDate, endDate],
          unlinkPanels: true,
          type: 'elDatePicker',
          itemType: 'occultation'
        }
      ],
      columnDefs: [
        {
          field: 'sApplyNo',
          headerName: '申请单号'
        },

        {
          field: 'sCustomerCode',
          headerName: '客商编码'
        },
        {
          field: 'sCustomerName',

          headerName: '客商名称'
        },
        {
          field: 'sCreditNo',
          headerName: '统一社会信用代码'
        },

        {
          headerName: '客商性质',
          field: 'sCustomerNature',
          width: '100px',
          valueGetter: (params) => {
            return getCnDitc(params, this.options['customer.nature'], 'sCustomerNature')
          }
        },
        {
          headerName: '准入类型',
          field: 'sType',
          width: '100px',
          valueGetter: (params) => {
            return getCnDitc(params, this.options['msb.merchants.TypeAccess'], 'sType')
          }
        },

        // {
        //   headerName: '是否申请先提货后结算（仅限终端）',
        //   field: 'sIsSettlementAfterDelivery',
        //   width: '240px',
        //   valueGetter: (params) => {
        //     return getCnDitc(params, this.options['base.yes-no'], 'sIsSettlementAfterDelivery')
        //   }
        // },
        // {
        //   headerName: '收费标准（吨）',
        //   field: 'sFeeStandards',
        //   width: '140px'
        // },

        {
          headerName: '状态',
          field: 'sSheetStatus',
          width: '120px',
          valueGetter: (params) => {
            return getCnDitc(params, this.options['msb.merchants.sheet.status'], 'sSheetStatus')
          }
        },
        {
          headerName: '申请日期',
          field: 'sApplyTime',
          cellStyle: { textAlign: 'right' },
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sApplyTime)
          },
          width: '160px'
        },
        {
          headerName: '人员',
          field: 'sStaffName'
        },
        {
          headerName: '部门',
          field: 'sDepartmentName'
        },
        {
          headerName: '核算组',
          field: 'sCheckGroupName'
        },
        {
          headerName: '经营单位',
          field: 'sManagementName'
        },

        {
          headerName: '创建人',
          field: 'sCreatorName'
        },
        {
          headerName: '创建时间',
          field: 'sCreateTime',
          cellStyle: { textAlign: 'right' },
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
          },
          width: '160px'
        },
        {
          headerName: '修改人',
          field: 'sModifierName'
        },
        {
          headerName: '修改时间',
          field: 'sModifyTime',
          cellStyle: { textAlign: 'right' },
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sModifyTime)
          },
          width: '160px'
        }
      ],
      rowData: [],
      selectId: '',
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      detailsId: null
    }
  },
  computed: {
  },
  beforeCreate() {
    getDictet(['msb.merchants.TypeAccess', 'msb.merchants.sheet.status', 'base.yes-no', 'customer.nature'])
      .then((result) => {
        this.options['msb.merchants.TypeAccess'] = result.data[0].dicts.map((item) => {
          if (item.sCodeValue === '70') {
            item.sCodeName = '执行'
          }
          return item
        })
        this.options['msb.merchants.sheet.status'] = result.data[1].dicts
        this.options['base.yes-no'] = result.data[2].dicts
        this.options['customer.nature'] = result.data[3].dicts
      })
      .catch(() => {})
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    getMaintenance(detailsId) {
      var detailsIdShow = detailsId === 'view' ? this.selectId : detailsId
      if (!detailsIdShow) {
        this.$message.error('请勾选列表需要查看的项')
        return
      }
      this.$router.push({
        path: `/CustomerDetailsAccess/${detailsIdShow}`,
        query: {
          id: detailsIdShow,
          type: 'edit',
          name: '准入申请详情',
          status: 70,
          activeId: localStorage.getItem('menuId')
        }
      })
    },
    onSearch(load = true) {
      this.$refs.aggrid.loadTableData()
      this.detailsId = null
    },
    onDefSelectfun(r) {
      console.log(r.data, '单选中')
      if (this.selectId === r.data.sId) {
        this.selectId = ''
      } else {
        this.selectId = r.data.sId
      }
    },
    // 点击 表格
    onRowDoubleClicked(params) {
      this.getMaintenance(params.data.sId)
    },
    handleFooterCount() {
      // this.$refs.aggrid.getSelectedData(res => {
      // })
    },
    filteringTime(time) {
      return time.replace('T', ' ')
    },
    // 主表
    loadData(pagination) {
      const formData = {
        ...this.$refs.searchForm.getSearchData(),
        sApplyStartTime: this.$refs.searchForm.getSearchData().sApplyStartTime
          ? this.filteringTime(this.$refs.searchForm.getSearchData().sApplyStartTime) : '',
        sApplyEndTime: this.$refs.searchForm.getSearchData().sApplyEndTime
          ? this.filteringTime(this.$refs.searchForm.getSearchData().sApplyEndTime) : '',
        sCreateStartTime: this.$refs.searchForm.getSearchData().sCreateStartTime
          ? this.filteringTime(this.$refs.searchForm.getSearchData().sCreateStartTime) : '',
        sCreateEndTime: this.$refs.searchForm.getSearchData().sCreateEndTime
          ? this.filteringTime(this.$refs.searchForm.getSearchData().sCreateEndTime) : ''
      }
      return new Promise((resolve, reject) => {
        accessList(formData, {
          ...pagination
        })
          .then((res) => {
            this.rowData = res.data.content.map((item) => {
              item._selected = false
              item._selectedKeys = []
              return item
            })
            resolve(res.data.totalElements)
          })
          .catch(() => {
            reject(0)
          })
      })
    }
  }
}
</script>
