<!--
 * @Description: 编辑页
-->
<!--  -->
<template>
  <div>
    <cnd-dialog :title="$t('grid.tabs.basicInformation')" :visible="true" @close="onClose">
      <template slot="leftBtn">
        <el-button
          type="primary"
          size="mini"
          @click="dialogVisible.approval = true"
        >{{ $t('grid.others.approvalStatus') }}</el-button>
      </template>
      <template slot="content">
        <el-form
          ref="form"
          class="el-form-w100"
          label-width="140px"
          :inline="true"
          :model="form"
          size="small"
        >
          <cnd-form-card-list :active-panel="activeCollapseName">
            <cnd-form-card :title="$t('grid.tabs.basicInformation')" name="1">
              <el-row>
                <!-- 基础信息 -->
                <cnd-form-item label="申请单号" prop="sApplyNo">
                  <el-input v-model="form.sApplyNo" disabled />
                </cnd-form-item>

                <cnd-form-item label="申请日期" prop="sApplyTime">
                  <el-date-picker
                    v-model="form.sApplyTime"
                    format="yyyy-MM-dd HH:mm:ss"
                    type="date"
                    :disabled="true"
                  />
                </cnd-form-item>
                <cnd-form-item label="客户名称" prop="sCustomerName">
                  <el-input v-model="form.sCustomerName" disabled />
                </cnd-form-item>
                <cnd-form-item label="终端/贸易商" prop="sCustomerSecType">
                  <el-input v-model="form.sCustomerSecType" disabled />
                </cnd-form-item>

                <cnd-form-item label="与我司合作起始时间" prop="sCooperateStartTime">
                  <el-input v-model="form.sCooperateStartTime" disabled />
                </cnd-form-item>

                <cnd-form-item label="近1年累计成交量(吨)" prop="formattedTotal">
                  <el-input v-model="form.formattedTotal" disabled />
                </cnd-form-item>
                <cnd-form-item label="是否出现逾期" prop="overdueAmountString">
                  <el-input v-model="form.overdueAmountString" disabled />
                </cnd-form-item>
                <cnd-form-item label="准入类型" prop="sTypeName">
                  <el-input v-model="form.sTypeName" disabled />
                </cnd-form-item>
                <!-- 准入信息 -->

                <template v-if="form.sType === '10'">
                  <!-- 预售 -->
                  <!-- <cnd-form-item
                    label="是否与我司合作过预售业务"
                    prop="sIsPresale"
                  >
                    <el-select
                      v-model="form.sIsPresale"
                      placeholder=""
                      disabled
                    >
                      <el-option
                        v-for="item in selectOps['base.yes-no']"
                        :key="item.sCodeValue"
                        :label="item.sCodeName"
                        :value="item.sCodeValue"
                      />
                    </el-select>
                  </cnd-form-item>
                  <cnd-form-item
                    v-if="form.sIsPresale === '1'"
                    label="前期预售业务成交量"
                    prop="sPresaleTurnover"
                  >
                    <el-input v-model="form.sPresaleTurnover" disabled />
                  </cnd-form-item>-->
                </template>
                <template v-if="form.sType === '20'">
                  <!-- 含权 -->
                  <cnd-form-item label="是否申请先提货后结算(仅限终端)" prop="sIsSettlementAfterDelivery">
                    <el-select v-model="form.sIsSettlementAfterDelivery" placeholder disabled>
                      <el-option
                        v-for="item in selectOps['base.yes-no']"
                        :key="item.sCodeValue"
                        :label="item.sCodeName"
                        :value="item.sCodeValue"
                      />
                    </el-select>
                  </cnd-form-item>
                  <!-- <cnd-form-item label="是否与我司合作过含权业务" prop="sIsCumRights">
                    <el-select v-model="form.sIsCumRights" placeholder disabled>
                      <el-option
                        v-for="item in selectOps['base.yes-no']"
                        :key="item.sCodeValue"
                        :label="item.sCodeName"
                        :value="item.sCodeValue"
                      />
                    </el-select>
                  </cnd-form-item>-->
                  <template v-if="form.sIsCumRights === '1'">
                    <!-- <cnd-form-item label="近半年与我司合作含权业务量" prop="sHalfYearBusinessVolume">
                      <el-input v-model="form.sHalfYearBusinessVolume" disabled />
                    </cnd-form-item>-->
                    <cnd-form-item
                      v-if="form.sIsSettlementAfterDelivery === '1'"
                      label="申请先提货后结算申请理由"
                      prop="sSettlementApplicationReason"
                    >
                      <el-input v-model="form.sSettlementApplicationReason" disabled />
                    </cnd-form-item>
                  </template>
                </template>
                <template v-if="form.sType === '30'">
                  <!-- 点价 -->
                  <cnd-form-item label="是否申请先提货后点价(仅限终端)" prop="sIsPricingBusiness">
                    <el-select v-model="form.sIsPricingBusiness" placeholder disabled>
                      <el-option
                        v-for="item in selectOps['base.yes-no']"
                        :key="item.sCodeValue"
                        :label="item.sCodeName"
                        :value="item.sCodeValue"
                      />
                    </el-select>
                  </cnd-form-item>
                  <template v-if="form.sIsPricingBusiness === '1'">
                    <cnd-form-item label="申请先提货后点价申请理由" prop="sPricingBusinessReason">
                      <el-input v-model="form.sPricingBusinessReason" disabled />
                    </cnd-form-item>
                  </template>
                  <cnd-form-item label="收费标准" prop="sFeeStandards">
                    <el-input v-model="form.sFeeStandards" disabled />
                  </cnd-form-item>
                </template>

                <cnd-form-item label="人员" prop="sStaffName">
                  <el-input v-model="form.sStaffName" clearable :disabled="true" />
                </cnd-form-item>

                <cnd-form-item label="部门" prop="sDepartmentName">
                  <el-input v-model="form.sDepartmentName" disabled />
                </cnd-form-item>
                <cnd-form-item label="经营单位" prop="sManagementName">
                  <el-input v-model="form.sManagementName" disabled />
                </cnd-form-item>
                <cnd-form-item label="统一社会信用代码" prop="sCreditNo">
                  <el-input v-model="form.sCreditNo" disabled />
                </cnd-form-item>

                <cnd-form-item label="客户相关情况说明" prop="sCustomerRelatedInformation">
                  <el-input v-model="form.sCustomerRelatedInformation" disabled />
                </cnd-form-item>
              </el-row>
            </cnd-form-card>

            <!-- 系统信息 -->
            <cnd-form-card :title="$t('grid.tabs.systemInformation')" name="5">
              <el-row>
                <cnd-form-item label="创建人" prop="sCreatorName">
                  <el-input v-model="form.sCreatorName" disabled />
                </cnd-form-item>
                <cnd-form-item label="创建时间" prop="sCreateTime">
                  <el-date-picker
                    v-model="form.sCreateTime"
                    format="yyyy-MM-dd HH:mm:ss"
                    type="date"
                    :disabled="true"
                  />
                </cnd-form-item>
                <cnd-form-item label="修改人" prop="sModifierName">
                  <el-input v-model="form.sModifierName" disabled />
                </cnd-form-item>
                <cnd-form-item label="修改时间" prop="sModifyTime">
                  <el-date-picker
                    v-model="form.sModifyTime"
                    format="yyyy-MM-dd HH:mm:ss"
                    type="date"
                    :disabled="true"
                  />
                </cnd-form-item>

                <cnd-form-item label="状态" prop="sSheetStatus">
                  <el-select v-model="form.sSheetStatus" placeholder disabled>
                    <el-option
                      v-for="item in selectOps['msb.merchants.sheet.status']"
                      :key="item.sCodeValue"
                      :label="item.sCodeName"
                      :value="item.sCodeValue"
                    />
                  </el-select>
                </cnd-form-item>

                <cnd-form-item label="版本号" prop="sSheetVersion">
                  <el-input v-model="form.sSheetVersion" disabled />
                </cnd-form-item>

                <cnd-form-item label="本版本生效日期" prop="sModifyTime">
                  <el-date-picker
                    v-model="form.sModifyTime"
                    format="yyyy-MM-dd HH:mm:ss"
                    type="date"
                    :disabled="true"
                  />
                </cnd-form-item>
              </el-row>
            </cnd-form-card>
          </cnd-form-card-list>
        </el-form>
      </template>
    </cnd-dialog>
    <!-- 弹出层 -->
    <horizon-approval-dialog
      :id="selectId"
      :solt-btn="false"
      :visible.sync="dialogVisible.approval"
      @handleClose="dialogVisible.approval = false"
    />
  </div>
</template>

<script>
import {
  accessDetail,
  customerGet,
  getData
} from '@/api/sysConfig/travellingMerchant'
import mixins from '../../mixins'
import businessMixin from '@/utils/businessMixin'
import { getCnDitc } from '@/utils/common'
import { getDictet } from '@/api/logistics/saleDelivery/saleorder'
export default {
  name: 'DomesticProcurementEdit',
  components: {},
  mixins: [businessMixin, mixins],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    },
    sCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogVisible: {
        annex: false, // 附件管理
        processQuery: false, // 进程查询
        approval: false, // 审批情况
        approvalN8: false
      },
      selectId: this.$route.query.id,
      activeName: 'basic', // active tab
      selectOps: {
        'msb.merchants.admissionType': [],
        'base.yes-no': [],
        'msb.merchants.sheet.status': [],
        'customer.kind': []
      },
      form: {
        sApplyNo: '',
        sApplyTime: '',
        sCustomerName: '',
        sCooperateStartTime: '',
        formattedTotal: '', // 近 1 年累计成交量(吨)
        overdueAmount: '', // 是否出现逾期
        overdueAmountString: '', // 是否出现逾期
        sType: '',
        sTypeName: '',
        sIsPresale: '',
        sPresaleTurnover: '',
        sIsCumRights: '',
        sHalfYearBusinessVolume: '',
        sIsSettlementAfterDelivery: '',
        sSettlementApplicationReason: '',
        sIsPricingBusiness: '',
        sPricingBusinessReason: '',
        sFeeStandards: '',
        sCreditNo: '',
        sCustomerSecType: '', // 终端贸易商
        sCustomerRelatedInformation: '',
        sStaffName: '',
        sDepartmentName: '',
        sCompanyName: '',
        sCheckGroupName: '',
        sManagementName: '',
        sCreatorName: '',
        sCreateTime: '',
        sModifierName: '',
        sModifyTime: '',
        sSheetStatus: '',
        sSheetVersion: ''
      },
      activeCollapseName: ['1', '2', '3', '4', '5'],
      disRemove: true,
      srcList: []
    }
  },
  created() {
    if (this.selectId) {
      this.getDetail()
    }
  },
  beforeMount() {
    // 获取字典状态
    getDictet([
      'msb.merchants.admissionType',
      'base.yes-no',
      'msb.merchants.sheet.status',
      'customer.kind'
    ])
      .then((result) => {
        this.selectOps['msb.merchants.admissionType'] = result.data[0].dicts
        this.selectOps['base.yes-no'] = result.data[1].dicts
        this.selectOps['msb.merchants.sheet.status'] = result.data[2].dicts
        this.selectOps['customer.kind'] = result.data[3].dicts
      })
      .catch(() => {})
  },
  methods: {
    // 获取基本信息
    getDetail() {
      accessDetail(this.selectId).then((res) => {
        this.form = res.data || {}
        if (this.form.sType && this.form.sType != null) {
          const params = {
            data: { sType: this.form.sType }
          }
          this.form.sTypeName = getCnDitc(
            params,
            this.selectOps['msb.merchants.admissionType'],
            'sType'
          )
          this.form.overdueAmountString = res.data.overdueAmount ? '预期' : '无'
          this.getMoreInfo()
        }
      })
    },
    getMoreInfo() {
      // 获取终端/贸易商
      if (this.form.sCustomerId) {
        customerGet(this.form.sCustomerId).then((res) => {
          const arrKind = this.selectOps['customer.kind'].find(
            (item) => item.sCodeValue === res.data.sCustomerSecType
          )
          this.form.sCustomerSecType = arrKind.sCodeName
        })
      }

      // 近1年累计
      if (this.form.sCustomerCode) {
        getData({
          serviceCode: 'ebl.freeapi.2023080012',
          dataMap: {
            inFields: {
              // orgId: this.biObj.orgId,
              // orgType: this.biObj.userInfo.sRoleType,
              orgId: '00011061',
              orgType: '1',
              customerCode: this.form.sCustomerCode,
              startMonth: this.getMonthRange().startMonth,
              endMonth: this.getMonthRange().endMonth
            }
          }
        }).then((res) => {
          const total = res.data.data.reduce((sum, item) => sum + item.num, 0)
          this.form.formattedTotal = total.toLocaleString()
        })
      }
    },
    getMonthRange() {
      const currentDate = new Date() // 获取当前日期

      // 获取当前年份和月份
      const currentYear = currentDate.getFullYear()
      const currentMonth = currentDate.getMonth() + 1 // 当前月份，1 为1月，12 为12月

      // 计算近一年结束的年月（即当前年月）
      const endYear = currentYear
      const endMonth = currentMonth

      // 计算近一年开始的年月
      let startYear = currentYear
      let startMonth = currentMonth - 12

      // 处理跨年的情况
      if (startMonth <= 0) {
        startYear -= 1
        startMonth += 12
      }

      // 格式化为 "YYYY-MM" 格式
      const startMonthStr = `${startYear}-${
        startMonth < 10 ? '0' + startMonth : startMonth
      }`
      const endMonthStr = `${endYear}-${
        endMonth < 10 ? '0' + endMonth : endMonth
      }`

      return {
        startMonth: startMonthStr,
        endMonth: endMonthStr
      }
    },
    // 关闭弹窗
    onClose() {
      this.$emit('onClose')
    }
    // processQuery() {
    //   this.$router.push({
    //     path: '/purchaseQuerys',
    //     query: {
    //       sCode: this.basicData.sCode,
    //       name: this.$t('grid.others.procurementContractProcess'),
    //       activeId: localStorage.getItem('menuId')
    //     }
    //   })
    // }
  }
}
</script>
