<template>
  <!-- 基础信息 -->
  <div class="page-container detail">
    <cnd-dialog :visible="true" @close="onClose">
      <template slot="content">
        <el-form
          ref="form"
          class="el-form-w100"
          label-width="100px"
          :inline="true"
          :model="form"
          size="small"
        >
          <cnd-form-card-list
            ref="cndFormCardList"
            :error-position="true"
            :active-panel="activeCollapseName"
          >
            <cnd-form-card :title="$t('grid.tabs.basicInformation')" name="1">
              <el-row>
                <cnd-form-item label="填写人" prop="sStaffName">
                  <el-input v-model="form.sStaffName" disabled />
                </cnd-form-item>
                <cnd-form-item label="填写人编码" prop="sStaffCode">
                  <el-input v-model="form.sStaffCode" disabled />
                </cnd-form-item>
                <cnd-form-item label="周报日期" prop="sWeeklyReportStartTime">
                  <div
                    class="input-style"
                  >{{ form.sWeeklyReportStartTime.slice(0, 10)+'至'+form.sWeeklyReportEndTime.slice(0, 10) }}</div>
                </cnd-form-item>
                <cnd-form-item label="状态" prop="sWeeklyReportStatus">
                  <el-select
                    v-model="form.sWeeklyReportStatus"
                    :placeholder="$t('components.pleaseSelect')"
                    clearable
                    size="mini"
                    disabled
                  >
                    <el-option
                      v-for="item in selectOps['msb.merchants.submit.status']"
                      :key="item.sCodeValue"
                      :label="item.sCodeName"
                      :value="item.sCodeValue"
                    />
                  </el-select>
                </cnd-form-item>

                <cnd-form-item label="本周基础事务" prop="sBasicThing" :custom-width="24">
                  <el-input
                    v-model="form.sBasicThing"
                    clearable
                    :disabled="true"
                    type="textarea"
                    :autosize="{ minRows: 7, maxRows: 5 }"
                  />
                </cnd-form-item>

                <cnd-form-item label="本周拜访情况" prop="sVisitingCondition" :custom-width="24">
                  <el-input
                    v-model="form.sVisitingCondition"
                    clearable
                    :disabled="true"
                    type="textarea"
                    :autosize="{ minRows: 7, maxRows: 5 }"
                  />
                </cnd-form-item>

                <cnd-form-item label="下周工作计划" prop="sNextWeekWorkPlan" :custom-width="24">
                  <el-input
                    v-model="form.sNextWeekWorkPlan"
                    clearable
                    :disabled="true"
                    type="textarea"
                    :autosize="{ minRows: 7, maxRows: 5 }"
                  />
                </cnd-form-item>

                <cnd-form-item label="需要协助事项" prop="sAssistanceNeededItems" :custom-width="24">
                  <el-input
                    v-model="form.sAssistanceNeededItems"
                    clearable
                    :disabled="true"
                    type="textarea"
                    :autosize="{ minRows: 7, maxRows: 5 }"
                  />
                </cnd-form-item>
                <cnd-form-item label="五年规划建议" prop="sFiveYearPlanSuggestions" :custom-width="24">
                  <el-input
                    v-model="form.sFiveYearPlanSuggestions"
                    clearable
                    :disabled="true"
                    type="textarea"
                    :autosize="{ minRows: 7, maxRows: 5 }"
                  />
                </cnd-form-item>
              </el-row>
            </cnd-form-card>

            <!-- 系统信息 -->
            <cnd-form-card :title="$t('grid.tabs.systemInformation')" name="5">
              <el-row>
                <cnd-form-item label="创建人" prop="sCreatorName">
                  <el-input v-model="form.sCreatorName" disabled />
                </cnd-form-item>
                <cnd-form-item label="创建时间" prop="sCreateTime">
                  <el-date-picker
                    v-model="form.sCreateTime"
                    format="yyyy-MM-dd HH:mm:ss"
                    type="date"
                    :disabled="true"
                  />
                </cnd-form-item>
                <cnd-form-item label="修改人" prop="sModifierName">
                  <el-input v-model="form.sModifierName" disabled />
                </cnd-form-item>
                <cnd-form-item label="修改时间" prop="sModifyTime">
                  <el-date-picker
                    v-model="form.sModifyTime"
                    format="yyyy-MM-dd HH:mm:ss"
                    type="date"
                    :disabled="true"
                  />
                </cnd-form-item>
              </el-row>
            </cnd-form-card>
          </cnd-form-card-list>
        </el-form>
      </template>
    </cnd-dialog>
  </div>
</template>

<script>
import businessMixin from '@/utils/businessMixin'
import mixins from '../../mixins'
import weeklyReportApi from '@/api/sysConfig/weeklyReportConfiguration'
import { getDictet } from '@/api/logistics/saleDelivery/saleorder'
import { Moment } from 'cnd-utils'

export default {
  name: 'ViewDetails',
  components: {},
  mixins: [businessMixin, mixins],
  data() {
    return {
      selectId: this.$route.query.id,

      dialogVisible: {
        annex: false, // 附件管理
        processQuery: false, // 进程查询
        approval: false, // 审批情况
        approvalN8: false
      },
      selectOps: {
        'msb.merchants.submit.status': []
      },
      form: {
        sCreator: '',
        sCreateTime: '',
        sModifier: '',
        sModifyTime: '',
        sStaffId: '',
        sDepartmentId: '',
        sPhone: '',
        sCompanyId: '',
        sCheckGroupId: '',
        sManagementId: '',
        sContent: '',
        sProductCategory: '',
        sCount: '',
        sSupplierName: '',
        sArea: '',
        sOrderStatus: '',
        sExpirationTime: '',
        tImages: []
      },
      activeCollapseName: ['1', '2', '3', '4', '5'],
      visible: false,
      disRemove: true,
      srcList: []
    }
  },
  watch: {},
  beforeMount() {
    // 获取字典状态
    getDictet(['msb.merchants.submit.status'])
      .then((result) => {
        this.selectOps['msb.merchants.submit.status'] = result.data[0].dicts
      })
      .catch(() => {})
  },
  created() {
    if (this.selectId) {
      this.getDetail()
    }
  },
  methods: {
    Moment,
    getDetail() {
      //  获取详情
      weeklyReportApi.getWeeklyDetails(this.selectId).then((res) => {
        this.form = res.data || this.form
        console.log(this.form, '1111111111111111111')
        // this.form.tImages.forEach((sImg) => {
        //   if (sImg.url) {
        //     this.srcList.push(sImg.url)
        //   }
        // })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-img {
  display: flex;
  flex-wrap: wrap;
}
// ::v-deep .flexCB {
//   z-index: 999 !important;
// }
::v-deep .is-fullscreen .form-card-list-container .el-collapse-item {
  margin-right: 0 !important;
  margin-left: 0 !important;
}
.detail {
  ::v-deep .dialog-body-position {
    top: 0 !important;
  }
}
.input-style {
  background-color: #f5f7fa;
  height: 26px;
  line-height: 26px;
  color: #262626;
  font-size: 12px;
  border-radius: 4px;
  padding: 0 15px;
  border: 1px solid #e4e7ed;
}
</style>
