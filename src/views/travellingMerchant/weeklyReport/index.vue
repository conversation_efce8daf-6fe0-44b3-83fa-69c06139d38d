<template>
  <!-- 周报 -->
  <div class="page-container">
    <p class="page-title">周报</p>
    <div class="layout-content auto-page-title flexV">
      <cnd-search-form
        ref="searchForm"
        :form-items="formItems"
        @search="onSearch"
        @searchValue="onSearchValue"
      />
      <div class="btn-group mt-10">
        <div class="text">列表</div>
        <el-button size="mini" type="primary" @click="getMaintenance('view')">查看</el-button>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        table-selection="single"
        @rowClicked="rowClicked"
        @selectedChange="handleFooterCount"
        @rowDoubleClicked="onRowDoubleClicked"
        @rowSelected="onDefSelectfun"
      />
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import { Moment } from 'cnd-utils'
import weeklyReportApi from '@/api/sysConfig/weeklyReportConfiguration'
import { getDictet } from '@/api/logistics/saleDelivery/saleorder'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import exportBtn from '@/components/exportBtnV2'
import mixins from '../mixins'
import { getCnDitc } from '@/utils/common'
import { getWeekNumber } from '@/utils/week'

export default {
  name: 'BusinessPartnerProfile',
  components: { steelTradeAggrid, exportBtn },
  mixins: [mixins],
  data() {
    const startDate = moment()
      .subtract(90, 'day')
      .startOf('day')
      .format('YYYY-MM-DDTHH:mm:ss')
    const endDate = moment().endOf('day').format('YYYY-MM-DDTHH:mm:ss')
    return {
      disRemove: true,
      options: {
        'msb.merchants.submit.status': []
      },
      headerCount: null,
      footerCount: null,
      searchInfo: null,
      formItems: [
        {
          label: '填写人',
          value: 'sStaffId',
          type: 'cndInputDialogItem',
          option: { valueKey: 'sPath' },
          dialogType: 'staff',
          // 请选择人员
          placeholder: this.$t('grid.others.pleaseSelectPersonnel')
        },
        {
          label: '状态',
          value: 'sWeeklyReportStatus',
          type: 'elSelect',
          dict: 'msb.merchants.submit.status'
        },
        {
          label: '年',
          value: 'year',
          type: 'elInput'
        },
        {
          label: '周',
          value: 'week',
          type: 'elInput'
        },
        {
          label: '周报日期',
          value: ['sCreateTime', 'vCreateTimeTo'],
          placeholder: [
            this.$t('grid.others.pleaseSelectTheDate'),
            this.$t('grid.others.pleaseSelectTheDate')
          ],
          default: [startDate, endDate],
          unlinkPanels: true,
          type: 'elDatePicker',
          itemType: 'occultation'
        },

        {
          label: '创建人',
          value: 'sCreator',
          type: 'cndInputDialog',
          itemType: 'occultation',
          placeholder: this.$t('grid.others.pleaseSelectTheCreator'),
          dialogType: 'applicant'
        },
        {
          label: '创建时间',
          value: ['sCreateTime', 'vCreateTimeTo'],
          placeholder: [
            this.$t('grid.others.pleaseSelectTheDate'),
            this.$t('grid.others.pleaseSelectTheDate')
          ],
          default: [startDate, endDate],
          unlinkPanels: true,
          type: 'elDatePicker',
          itemType: 'occultation'
        }
      ],
      columnDefs: [
        {
          field: 'sStaffName',
          headerName: '填写人'
        },

        {
          field: 'sStaffCode',
          headerName: '填写人编码',
          width: '160px'
        },
        {
          field: 'sWeeklyReportStartTime',
          headerName: '年',
          valueFormatter(params) {
            return Moment.time('YYYY', params.data.sWeeklyReportStartTime)
          }
        },
        {
          field: 'sWeeklyReportStartTime',
          headerName: '周',
          valueFormatter(params) {
            return getWeekNumber(params.data.sWeeklyReportStartTime)
          }
        },

        {
          headerName: '周报日期',
          field: 'sWeeklyReportStartTime',
          width: '200px',
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD', params.data.sWeeklyReportStartTime) + '至' + Moment.time('YYYY-MM-DD', params.data.sWeeklyReportEndTime)
          }
        },
        {
          headerName: '状态',
          field: 'sWeeklyReportStatus',
          width: '160px',
          valueGetter: (params) => {
            return getCnDitc(params, this.options['msb.merchants.submit.status'], 'sWeeklyReportStatus')
          }
        },

        {
          headerName: '创建人',
          field: 'sCreatorName'
        },
        {
          headerName: '创建时间',
          field: 'sCreateTime',
          cellStyle: { textAlign: 'right' },
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
          },
          width: '160px'
        },
        {
          headerName: '修改人',
          field: 'sModifierName'
        },
        {
          headerName: '修改时间',
          field: 'sModifyTime',
          cellStyle: { textAlign: 'right' },
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sModifyTime)
          },
          width: '160px'
        }
      ],
      rowData: [],
      rowDataChild: [],
      selectId: '',
      dialogVisibleadd: false,
      selectedId: null,
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      detailsId: null,
      unitary: null
    }
  },
  computed: {
    detailParams() {
      const sIds = []
      const selectIds = []
      this.rowData.forEach((item) => {
        sIds.push(item.sId)
        if (item && item._selected) {
          selectIds.push(item.sId)
        }
      })
      return {
        sIds: sIds.toString(),
        selectIds: selectIds.toString()
      }
    }
  },
  beforeCreate() {
    getDictet(['msb.merchants.submit.status'])
      .then((result) => {
        this.options['msb.merchants.submit.status'] = result.data[0].dicts.map((item) => {
          if (item.sCodeValue === '70') {
            item.sCodeName = '执行'
          }
          return item
        })
      })
      .catch(() => {})
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    getMaintenance(detailsId) {
      var detailsIdShow = detailsId === 'view' ? this.selectId : detailsId
      if (!detailsIdShow) {
        this.$message.error('请勾选列表需要查看的项')
        return
      }
      this.$router.push({
        path: `/WeeklyReportDetails/${detailsIdShow}`,
        query: {
          id: detailsIdShow,
          type: 'edit',
          name: '周报详情',
          status: 70,
          activeId: localStorage.getItem('menuId')
        }
      })
    },
    onSearch(load = true) {
      this.$refs.aggrid.loadTableData()
      this.detailsId = null
    },
    onDefSelectfun(r) {
      console.log(r.data, '单选中')
      if (this.selectId === r.data.sId) {
        this.selectId = ''
      } else {
        this.selectId = r.data.sId
      }
    },
    // 点击 表格
    onRowDoubleClicked(params) {
      this.getMaintenance(params.data.sId)
    },
    handleFooterCount() {
      // this.$refs.aggrid.getSelectedData(res => {
      // })
    },
    filteringTime(time) {
      return time.replace('T', ' ')
    },
    // 主表
    loadData(pagination) {
      // todo
      const formData = {
        ...this.$refs.searchForm.getSearchData(),
        sVisitDateFrom: this.$refs.searchForm.getSearchData().sVisitDateFrom
          ? this.filteringTime(this.$refs.searchForm.getSearchData().sVisitDateFrom) : '',
        sVisitDateTo: this.$refs.searchForm.getSearchData().sVisitDateTo
          ? this.filteringTime(this.$refs.searchForm.getSearchData().sVisitDateTo) : '',
        sCreateTimeFrom: this.$refs.searchForm.getSearchData().sCreateTimeFrom
          ? this.filteringTime(this.$refs.searchForm.getSearchData().sCreateTimeFrom) : '',
        sCreateTimeTo: this.$refs.searchForm.getSearchData().sCreateTimeTo
          ? this.filteringTime(this.$refs.searchForm.getSearchData().sCreateTimeTo) : ''
      }
      return new Promise((resolve, reject) => {
        weeklyReportApi.weeklyList(formData, {
          ...pagination
        })
          .then((res) => {
            this.rowData = res.data.content.map((item) => {
              item._selected = false
              item._selectedKeys = []
              return item
            })
            resolve(res.data.totalElements)
            this.rowClicked({ data: res.data.content[0] })
          })
          .catch(() => {
            reject(0)
          })
      })
    }
  }
}
</script>
