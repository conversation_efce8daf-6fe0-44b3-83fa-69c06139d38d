<template>
  <div class="page-container tab">
    <div class="layout-content auto-page-title flexV">
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        table-selection=""
        @rowClicked="rowClicked"
        @selectedChange="handleFooterCount"
        @rowDoubleClicked="onRowDoubleClicked"
      />
    </div>
  </div>
</template>

<script>
import businessMixin from '@/utils/businessMixin'
import mixins from '../../mixins'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import formDialog from '@/components/formDialog'
import { merchantTagList } from '@/api/sysConfig/travellingMerchant'
import { Moment } from 'cnd-utils'
import { getCnDitc } from '@/utils/common'

export default {
  name: 'Maintenance',
  components: { steelTradeAggrid, formDialog },
  mixins: [businessMixin, mixins],
  props: {
    selectOps: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    const _this = this
    return {
      columnDefs: [
        {
          field: 'sName',
          headerName: '标签名',
          width: '120px'
        },
        {
          field: 'sType',
          headerName: '标签类型',
          width: '140px',
          valueGetter(params) {
            return getCnDitc(params, _this.selectOps['msb.label.type'], 'sType')
          }
        },
        {
          field: 'sCreateTime',
          headerName: '添加时间',
          minWidth: 150,
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
          }
        }
      ],
      rowData: [],
      rowDataChild: [],
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      }
    }
  },
  watch: {},
  created() {
  },
  mounted() {
  },
  methods: {
    // 主表
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        merchantTagList(
          { sCustomerArchivesId: this.$route.query.id },
          {
            ...pagination
          }
        )
          .then((res) => {
            console.log(res, 'resresres')
            this.rowData = res.data.content.map((item) => {
              item._selected = false
              item._selectedKeys = []
              return item
            })
            resolve(res.data.totalElements)
            this.rowClicked({ data: res.data.content[0] })
          })
          .catch(() => {
            reject(0)
          })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.form-box {
  padding: 10px;
}
.btn-box {
  padding: 12px 0;
  padding-left: 10px;
}
</style>
<style>
@import url('~@/styles/basisSelfCollectionSheet/detail.scss');
</style>
