<template>
  <!-- 基础信息 -->
  <div class="page-container detail">
    <el-form
      ref="form"
      class="el-form-w100"
      label-width="100px"
      :inline="true"
      :model="form"
      size="small"
    >
      <cnd-form-card-list :active-panel="activeCollapseName">
        <cnd-form-card :title="$t('grid.tabs.basicInformation')" name="1">
          <el-row>
            <cnd-form-item label="档案编码" prop="sCustomerArchivesNo">
              <el-input v-model="form.sCustomerArchivesNo" disabled />
            </cnd-form-item>
            <cnd-form-item label="客商名称" prop="sCustomerName">
              <el-input v-model="form.sCustomerName" disabled />
            </cnd-form-item>
            <cnd-form-item label="统一社会信用代码" prop="sCreditNo">
              <el-input v-model="form.bussinessInformationVo.sCreditNo" disabled />
            </cnd-form-item>
            <cnd-form-item label="客商类型" prop="sCustomerType">
              <div
                class="input-style"
              >{{ getDictName(form.sCustomerType, 'msb.merchants.typeMerchant') }}</div>
            </cnd-form-item>

            <cnd-form-item label="人员" prop="sStaffName">
              <el-input v-model="form.sStaffName" clearable :disabled="true" />
            </cnd-form-item>

            <cnd-form-item label="部门" prop="sDepartmentName">
              <el-input v-model="form.sDepartmentName" disabled />
            </cnd-form-item>

            <cnd-form-item label="经营单位" prop="sManagementName">
              <el-input v-model="form.sManagementName" disabled />
            </cnd-form-item>

            <cnd-form-item label="准入情况" prop="access">
              <el-input v-model="form.accessInfo" disabled />
            </cnd-form-item>
          </el-row>
        </cnd-form-card>
        <!-- 客户印象 -->
        <cnd-form-card title="客户印象" name="2">
          <el-row>
            <cnd-form-item label="企业类型" prop="sCustomerImpressionVolume">
              <el-input v-model="form.sEnterpriseType" disabled />
            </cnd-form-item>
            <cnd-form-item label="主营产品" prop="sCustomerImpressionVolume">
              <el-input v-model="form.sMainProducts" disabled />
            </cnd-form-item>
            <cnd-form-item label="年采购量(万吨)" prop="sCustomerImpressionVolume">
              <el-input v-model="form.sAnnualProcurementVolume" disabled />
            </cnd-form-item>
            <cnd-form-item label="实控人" prop="sCustomerImpressionVolume">
              <el-input v-model="form.sActualController" disabled />
            </cnd-form-item>
            <cnd-form-item label="涉足非钢" prop="sCustomerImpressionVolume">
              <el-input v-model="form.sSetNonSteel" disabled />
            </cnd-form-item>
            <cnd-form-item label="从业时间(年)" prop="sCustomerImpressionVolume">
              <el-input v-model="form.sEmploymentYear" disabled />
            </cnd-form-item>
            <cnd-form-item label="行业地位" prop="sCustomerImpressionVolume">
              <el-input v-model="form.sIndustryStatus" disabled />
            </cnd-form-item>
            <cnd-form-item label="主要客户群体" prop="sCustomerImpressionVolume">
              <el-input v-model="form.sMainCustomerGroups" disabled />
            </cnd-form-item>
            <cnd-form-item label="其他信息" prop="sCustomerImpressionBase" :custom-width="24">
              <el-input
                v-model="form.sCustomerImpressionBase"
                clearable
                :disabled="true"
                type="textarea"
                :autosize="{ minRows: 8, maxRows: 5 }"
              />
            </cnd-form-item>
          </el-row>
        </cnd-form-card>

        <!-- 供应商印象 -->
        <cnd-form-card title="供应商印象" name="3">
          <el-row>
            <cnd-form-item label="基础信息" prop="sSupplierImpressionBase" :custom-width="12">
              <el-input
                v-model="form.sSupplierImpressionBase"
                clearable
                :disabled="true"
                type="textarea"
                :autosize="{ minRows: 8, maxRows: 5 }"
              />
            </cnd-form-item>

            <cnd-form-item label="政策" prop="sSupplierImpressionPolicy" :custom-width="12">
              <el-input
                v-model="form.sSupplierImpressionPolicy"
                clearable
                :disabled="true"
                type="textarea"
                :autosize="{ minRows: 8, maxRows: 5 }"
              />
            </cnd-form-item>
          </el-row>
        </cnd-form-card>

        <!-- 其他信息 -->
        <cnd-form-card title="其他信息" name="4">
          <el-row>
            <cnd-form-item label="客商性质" prop="sCustomerNature">
              <div class="input-style">{{ getDictName(form.sCustomerNature, 'customer.nature') }}</div>
            </cnd-form-item>
            <cnd-form-item label="客户性质" prop="sCustomerSecType">
              <div class="input-style">{{ getDictName(form.sCustomerSecType, 'customer.kind') }}</div>
            </cnd-form-item>

            <cnd-form-item v-if="form.isSign" label="E建签ID" prop="ejqContent">
              <el-input v-model="form.ejqContent" disabled />
            </cnd-form-item>
            <cnd-form-item label="注册资本" prop="sRegisteredCapital">
              <el-input v-model="form.bussinessInformationVo.sRegisteredCapital" disabled />
            </cnd-form-item>

            <cnd-form-item label="成立日期" prop="sEstablishmentDate">
              <el-date-picker
                v-model="form.bussinessInformationVo.sEstablishmentDate"
                format="yyyy-MM-dd HH:mm:ss"
                type="date"
                :disabled="true"
              />
            </cnd-form-item>
            <cnd-form-item label="注册地址" prop="sRegisteredAddress">
              <el-input v-model="form.bussinessInformationVo.sRegisteredAddress" disabled />
            </cnd-form-item>
            <cnd-form-item label="法人代表" prop="sLegalRepresentation">
              <el-input v-model="form.bussinessInformationVo.sLegalRepresentation" disabled />
            </cnd-form-item>
            <cnd-form-item label="股东背景" prop="sShareholderBackground">
              <el-input v-model="form.bussinessInformationVo.sShareholderBackground" disabled />
            </cnd-form-item>
          </el-row>
        </cnd-form-card>

        <!-- 系统信息 -->
        <cnd-form-card :title="$t('grid.tabs.systemInformation')" name="5">
          <el-row>
            <cnd-form-item label="创建人" prop="sCreatorName">
              <el-input v-model="form.sCreatorName" disabled />
            </cnd-form-item>
            <cnd-form-item label="创建时间" prop="sCreateTime">
              <el-date-picker
                v-model="form.sCreateTime"
                format="yyyy-MM-dd HH:mm:ss"
                type="date"
                :disabled="true"
              />
            </cnd-form-item>
            <cnd-form-item label="修改人" prop="sModifierName">
              <el-input v-model="form.sModifierName" disabled />
            </cnd-form-item>
            <cnd-form-item label="修改时间" prop="sModifyTime">
              <el-date-picker
                v-model="form.sModifyTime"
                format="yyyy-MM-dd HH:mm:ss"
                type="date"
                :disabled="true"
              />
            </cnd-form-item>
          </el-row>
        </cnd-form-card>
      </cnd-form-card-list>
    </el-form>
  </div>
</template>

<script>
import businessMixin from '@/utils/businessMixin'
import mixins from '../../mixins'
import { merchantDetail } from '@/api/sysConfig/travellingMerchant'
import { handleDict } from '@/utils/common'
export default {
  name: 'ViewDetails',
  components: {},
  mixins: [businessMixin, mixins],
  props: {
    selectOps: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      selectId: this.$route.query.id,
      form: {
        bussinessInformationVo: {
          sCreditNo: '',
          sMerchantNature: '',
          sRegisteredCapital: '',
          sEstablishmentDate: '',
          sRegisteredAddress: '',
          sLegalRepresentation: '',
          sShareholderBackground: ''
        },
        sCustomerArchivesNo: '',
        sCustomerName: '',
        sCustomerType: '',
        sStaffName: '',
        sDepartmentName: '',
        sManagementName: '',
        access: '',
        accessInfo: '', // 准入情况
        sCustomerImpressionBase: '',
        sCustomerImpressionVolume: '',
        sSupplierImpressionBase: '',
        sSupplierImpressionPolicy: '',
        sCustomerSecType: '',
        isSign: '',
        ejqContent: ''
      },
      activeCollapseName: ['1', '2', '3', '4', '5'],
      visible: false,
      disRemove: true,
      srcList: []
    }
  },
  watch: {},
  beforeMount() {
  },
  created() {
    if (this.selectId) {
      this.getDetail()
    }
  },
  methods: {
    getDetail() {
      //  获取详情
      merchantDetail(this.selectId).then((res) => {
        this.form = res.data || this.form
        this.form.accessInfo =
          res.data.access.map((v) => v.name).join('、') || ''
      })
    },
    getDictName(vals, dict) {
      if (vals) {
        const list = vals.split(',')
        let text = ''
        list.forEach((element, index) => {
          text += index < list.length - 1 ? handleDict(
            element,
            this.selectOps[dict]
          ) + '，' : handleDict(
            element,
            this.selectOps[dict]
          )
        })
        return text
      } else {
        return ''
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-img {
  display: flex;
  flex-wrap: wrap;
}
.input-style {
  background-color: #f5f7fa;
  height: 26px;
  line-height: 26px;
  color: #262626;
  font-size: 12px;
  border-radius: 4px;
  padding: 0 15px;
  border: 1px solid #e4e7ed;
}
</style>
