<template>
  <div class="page-container tab">
    <div class="layout-content auto-page-title flexV">
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        table-selection=""
        @rowClicked="rowClicked"
        @selectedChange="handleFooterCount"
        @rowDoubleClicked="onRowDoubleClicked"
      />
    </div>
  </div>
</template>

<script>
// import { getMorgUnit } from '@/api/money/cashFlow.js'
import businessMixin from '@/utils/businessMixin'
import mixins from '../../mixins'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import { merchantSeriesList } from '@/api/sysConfig/travellingMerchant'

import formDialog from '@/components/formDialog'
export default {
  name: 'Maintenance',
  components: { steelTradeAggrid, formDialog },
  mixins: [businessMixin, mixins],
  data() {
    return {
      options: {
        'invoice.subtype': []
      },
      dialogVisible: false,
      dialogVisible1: false,
      sCustomerId: this.$route.query.sCreditNo,
      formDialogItems1: [],
      columnDefs: [
        {
          field: 'sCode',
          headerName: '系列代码',
          width: '120px'
        },
        {
          field: 'sName',
          headerName: '系列名称',
          width: '140px'
        }
      ],

      visible: false,
      rowData: [],
      rowDataChild: [],
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      deletingSid: [],
      vCurAmtArr: [],
      vCurAmtArrs: [],
      putShow: false,
      title: '新增',
      sId: '',
      vCurAmtArrEdit: []
    }
  },
  watch: {},
  created() {},
  mounted() {
  },
  methods: {
    // 主表
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        merchantSeriesList(
          { sCreditNo: this.sCustomerId },
          {
            ...pagination
          }
        )
          .then((res) => {
            console.log(res, 'resresres')
            this.rowData = res.data.content.map((item) => {
              item._selected = false
              item._selectedKeys = []
              return item
            })
            resolve(res.data.totalElements)
            this.rowClicked({ data: res.data.content[0] })
          })
          .catch(() => {
            reject(0)
          })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.form-box {
  padding: 10px;
}
.btn-box {
  padding: 12px 0;
  padding-left: 10px;
}
</style>
<style>
@import url('~@/styles/basisSelfCollectionSheet/detail.scss');
</style>
