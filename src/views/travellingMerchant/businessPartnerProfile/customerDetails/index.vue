<template>
  <!-- 客商档案-->
  <div class="page-container detail">
    <cnd-dialog :visible="true" @close="onClose">
      <template slot="leftBtn">
        <el-button
          type="primary"
          size="mini"
          @click="dialogVisible.approval = true"
        >
          查看附件
        </el-button>
      </template>
      <template slot="content">
        <el-tabs v-model="activeName">
          <el-tab-pane label="基础信息" name="shipmentCommodity">
            <basicDetails :select-ops="selectOps" />
          </el-tab-pane>
          <el-tab-pane label="标签信息" name="tagInformation">
            <tagInformation v-if="activeName == 'tagInformation'" :select-ops="selectOps" />
          </el-tab-pane>

          <el-tab-pane label="所属系列" name="belongingSeries">
            <belongingSeries v-if="activeName == 'belongingSeries'" :select-ops="selectOps" />
          </el-tab-pane>
        </el-tabs>
      </template>
    </cnd-dialog>
    <steel-annex-dialog
      :visible="dialogVisible.approval"
      append-to-body
      :biz-id="selectId"
      :disabled-btn="{
        scan: true,
        del: true
      }"
      @onSelect="dialogVisible.approval = false"
    />
  </div>
</template>

<script>
import businessMixin from '@/utils/businessMixin'
import mixins from '../../mixins'
import { getDictet } from '@/api/logistics/saleDelivery/saleorder'

import basicDetails from './basicDetails'
import tagInformation from './tagInformation'
import belongingSeries from './belongingSeries'
export default {
  name: 'ViewDetails',
  components: { basicDetails, tagInformation, belongingSeries },
  mixins: [businessMixin, mixins],
  data() {
    return {
      selectId: this.$route.query.id,

      dialogVisible: {
        annex: false, // 附件管理
        processQuery: false, // 进程查询
        approval: false, // 审批情况
        approvalN8: false
      },
      selectOps: {
        'msb.merchants.typeMerchant': []
      },
      activeName: 'shipmentCommodity',
      form: {},
      activeCollapseName: ['1', '2'],
      visible: false,
      disRemove: true,
      srcList: []
    }
  },
  watch: {},
  beforeMount() {
    // 获取字典状态
    getDictet(['msb.merchants.typeMerchant', 'customer.nature', 'customer.kind', 'msb.label.type'])
      .then((result) => {
        this.selectOps['msb.merchants.typeMerchant'] = result.data[0].dicts
        this.selectOps['customer.nature'] = result.data[1].dicts
        this.selectOps['customer.kind'] = result.data[2].dicts
        this.selectOps['msb.label.type'] = result.data[3].dicts
      })
      .catch(() => {})
  },
  created() {
  },
  methods: {
  }
}
</script>
<style lang="scss" scoped>
.dialog-img {
  display: flex;
  flex-wrap: wrap;
}
::v-deep .flexCB {
  //   z-index: 999 !important;
}
::v-deep .is-fullscreen .form-card-list-container .el-collapse-item {
  margin-right: 0 !important;
  margin-left: 0 !important;
}
</style>
