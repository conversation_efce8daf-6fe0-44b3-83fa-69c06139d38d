<template>
  <div class="page-container">
    <p class="page-title">客商档案</p>
    <div class="layout-content auto-page-title flexV">
      <cnd-search-form
        ref="searchForm"
        :form-items="formItems"
        @search="onSearch"
        @searchValue="onSearchValue"
      />
      <div class="btn-group mt-10">
        <div class="text">列表</div>
        <el-button size="mini" type="primary" @click="getMaintenance('view')">查看</el-button>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        table-selection="single"
        @selectedChange="handleFooterCount"
        @rowDoubleClicked="onRowDoubleClicked"
        @rowSelected="onDefSelectfun"
      />
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import { Moment } from 'cnd-utils'
import { getDictet } from '@/api/logistics/saleDelivery/saleorder'
import { handleDict } from '@/utils/common'
import { merchantList } from '@/api/sysConfig/travellingMerchant'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import exportBtn from '@/components/exportBtnV2'
import mixins from '../mixins'
export default {
  name: 'BusinessPartnerProfile',
  components: { steelTradeAggrid, exportBtn },
  mixins: [mixins],
  data() {
    const startDate = moment()
      .subtract(90, 'day')
      .startOf('day')
      .format('YYYY-MM-DDTHH:mm:ss')
    const endDate = moment().endOf('day').format('YYYY-MM-DDTHH:mm:ss')
    return {
      options: {
        'msb.merchants.typeMerchant': []
      },
      searchInfo: null,
      formItems: [
        {
          label: '档案编码',
          value: 'sCustomerArchivesNo',
          type: 'elInput'
        },
        {
          // 人员
          label: this.$t('grid.title.personnel'),
          value: 'sStaffId',
          type: 'cndInputDialogItem',
          option: { valueKey: 'sPath' },
          dialogType: 'staff',
          // 请选择人员
          placeholder: this.$t('grid.others.pleaseSelectPersonnel')
        },
        {
          label: '客商名称',
          value: 'sCustomerName',
          type: 'elInput'
        },
        {
          label: '统一社会信用代码',
          value: 'sCreditNo',
          type: 'elInput',
          width: '180px'
        },
        {
          label: '创建人',
          value: 'sCreator',
          type: 'cndInputDialog',
          itemType: 'occultation',
          placeholder: this.$t('grid.others.pleaseSelectTheCreator'),
          dialogType: 'applicant'
        },
        {
          label: '创建时间',
          value: ['sCreateStartTime', 'sCreateEndTime'],
          placeholder: [
            this.$t('grid.others.pleaseSelectTheDate'),
            this.$t('grid.others.pleaseSelectTheDate')
          ],
          default: [startDate, endDate],
          unlinkPanels: true,
          type: 'elDatePicker',
          itemType: 'occultation'
        },
        {
          label: '修改人',
          value: 'sModifier',
          type: 'cndInputDialog',
          itemType: 'occultation',
          placeholder: this.$t('grid.others.pleaseSelectTheCreator'),
          dialogType: 'applicant'
        },
        {
          label: '修改时间',
          value: ['sModifyStartTime', 'sModifyEndTime'],
          placeholder: [
            this.$t('grid.others.pleaseSelectTheDate'),
            this.$t('grid.others.pleaseSelectTheDate')
          ],
          default: ['', ''],
          unlinkPanels: true,
          type: 'elDatePicker',
          itemType: 'occultation'
        }
      ],
      columnDefs: [
        {
          field: 'sCustomerArchivesNo',
          headerName: '档案编码'
        },

        {
          headerName: '客商名称',
          field: 'sCustomerName',
          width: '160px'
        },
        {
          headerName: '统一社会信用代码',
          field: 'sCreditNo',
          width: '160px'
        },
        {
          headerName: '客商类型',
          field: 'sCustomerType',
          width: '160px',
          valueGetter: (params) => {
            if (params.data.sCustomerType) {
              const list = params.data.sCustomerType.split(',')
              let text = ''
              list.forEach((element, index) => {
                text += index < list.length - 1 ? handleDict(
                  element,
                  this.options['msb.merchants.typeMerchant']
                ) + '，' : handleDict(
                  element,
                  this.options['msb.merchants.typeMerchant']
                )
              })
              return text
            } else {
              return ''
            }
          }
        },
        {
          headerName: '人员',
          field: 'sStaffName'
        },
        {
          headerName: '部门',
          field: 'sDepartmentName'
        },
        {
          headerName: '经营单位',
          field: 'sManagementName'
        },
        {
          headerName: '创建人',
          field: 'sCreatorName'
        },
        {
          headerName: '创建时间',
          field: 'sCreateTime',
          cellStyle: { textAlign: 'right' },
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
          },
          width: '160px'
        },
        {
          headerName: '修改人',
          field: 'sModifierName'
        },
        {
          headerName: '修改时间',
          field: 'sModifyTime',
          cellStyle: { textAlign: 'right' },
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sModifyTime)
          },
          width: '160px'
        }
      ],
      rowData: [],
      selectId: '',
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      detailsId: null,
      sCreditNo: ''
    }
  },
  computed: {
    detailParams() {
      const sIds = []
      const selectIds = []
      this.rowData.forEach((item) => {
        sIds.push(item.sId)
        if (item && item._selected) {
          selectIds.push(item.sId)
        }
      })
      return {
        sIds: sIds.toString(),
        selectIds: selectIds.toString()
      }
    }
  },
  beforeCreate() {
    getDictet(['msb.merchants.typeMerchant'])
      .then((result) => {
        this.options['msb.merchants.typeMerchant'] = result.data[0].dicts
      })
      .catch(() => {})
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    getMaintenance(detailsId) {
      var detailsIdShow = detailsId === 'view' ? this.selectId : detailsId
      if (!detailsIdShow) {
        this.$message.error('请勾选列表需要查看的项')
        return
      }

      console.log(this.sCreditNo, 'detailsIddetailsIddetailsId')

      this.$router.push({
        path: `/CustomerDetails/${detailsIdShow}`,
        query: {
          id: detailsIdShow,
          type: 'edit',
          name: '客商详情',
          status: 70,
          activeId: localStorage.getItem('menuId'),
          sCreditNo: this.sCreditNo
        }
      })
    },
    onSearch(load = true) {
      this.$refs.aggrid.loadTableData()
      this.detailsId = null
    },
    onDefSelectfun(r) {
      console.log(r.data, '单选中')
      if (this.selectId === r.data.sId) {
        this.selectId = ''
      } else {
        this.selectId = r.data.sId
      }
    },
    // 点击 表格
    onRowDoubleClicked(params) {
      this.sCreditNo = params.data.sCreditNo
      this.getMaintenance(params.data.sId)
    },

    handleFooterCount() {
      // this.$refs.aggrid.getSelectedData(res => {
      // })
    },
    filteringTime(time) {
      return time.replace('T', ' ')
    },
    // 主表
    loadData(pagination) {
      const formData = {
        ...this.$refs.searchForm.getSearchData(),
        sCreateStartTime: this.$refs.searchForm.getSearchData().sCreateStartTime
          ? this.filteringTime(this.$refs.searchForm.getSearchData().sCreateStartTime) : '',
        sCreateEndTime: this.$refs.searchForm.getSearchData().sCreateEndTime
          ? this.filteringTime(this.$refs.searchForm.getSearchData().sCreateEndTime) : '',
        sModifyStartTime: this.$refs.searchForm.getSearchData().sModifyStartTime
          ? this.filteringTime(this.$refs.searchForm.getSearchData().sModifyStartTime) : '',
        sModifyEndTime: this.$refs.searchForm.getSearchData().sModifyEndTime
          ? this.filteringTime(this.$refs.searchForm.getSearchData().sModifyEndTime) : ''
      }
      return new Promise((resolve, reject) => {
        merchantList(formData, {
          ...pagination
        })
          .then((res) => {
            this.rowData = res.data.content.map((item) => {
              item._selected = false
              item._selectedKeys = []
              return item
            })
            resolve(res.data.totalElements)
          })
          .catch(() => {
            reject(0)
          })
      })
    }
  }
}
</script>
