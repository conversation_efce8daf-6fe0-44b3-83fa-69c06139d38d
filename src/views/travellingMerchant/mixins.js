import { deepClone } from '@/utils/common'
import { factoryFieldConfigHeader } from '@/api/steelInfoManage/shippingInfo.js'
import { getDictList, getCnDitc } from '@/utils/dict'
import { Moment } from 'cnd-utils'
import { SteelFormat } from 'cnd-horizon-utils'
export default {
  data() {
    return {
      oldSearchValue: {}
    }
  },
  methods: {
    onSearchValue(v) {
      const { sUpSourceType } = this.oldSearchValue
      const index = this.formItems.findIndex(
        (item) => item.value === 'sSupplierId'
      )
      if (v.sUpSourceType) {
        if (sUpSourceType && v.sUpSourceType !== sUpSourceType) {
          v.sSupplierId = ''
          v._previewsSupplierId = ''
        }
        if (index !== -1) {
          this.formItems[index].otherOptions.sUpSourceType = v.sUpSourceType
        }
      }
      this.oldSearchValue = deepClone(v)
    },
    getHeader({ sUpSourceType, sMenuCode, aggridRefs, defaultColumns }) {
      factoryFieldConfigHeader({
        sUpSourceType,
        sMenuCode
      }).then((res) => {
        const { schema, sortColumns, dictList } = res.data
        let selectOps
        if (dictList.length > 0) {
          selectOps = dictList.reduce((acc, curr) => {
            acc[curr] = []
            return acc
          }, {})
          getDictList(selectOps)
        }
        if (sortColumns.length > 0) {
          const result = sortColumns.map((item) => {
            const { name: headerName, type, format, point, dict } = schema[item]
            const cellStyleAlign = type === 'number' ? 'right' : 'left'
            const isDateType =
              type === 'date' && (format === 'YYYY-MM-DD HH:mm:ss' || !format)
            return {
              headerName,
              field: item,
              cellStyle: () => ({
                textAlign: cellStyleAlign
              }),
              minWidth: isDateType ? 150 : '',
              valueGetter(params) {
                const value = params.data[item]
                switch (type) {
                  case 'date':
                    return Moment.time(format || 'YYYY-MM-DD HH:mm:ss', value)
                  case 'number':
                    return SteelFormat.formatThousandthSign(+value, point || 0)
                  case 'dict':
                    return getCnDitc(params, selectOps[dict], item)
                  case 'text':
                  case undefined:
                    return value
                  default:
                    return value
                }
              }
            }
          })
          aggridRefs.setColumnDefs(result)
        } else {
          aggridRefs.setColumnDefs(defaultColumns)
        }
      })
    }
  }
}
