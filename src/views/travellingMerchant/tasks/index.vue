<template>
  <div class="page-container">
    <p class="page-title">待办</p>
    <div class="layout-content auto-page-title flexV">
      <cnd-search-form
        ref="searchForm"
        :form-items="formItems"
        @search="onSearch"
        @searchValue="onSearchValue"
      />
      <div class="btn-group mt-10">
        <div class="text">列表</div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
      />
    </div>
  </div>
</template>

<script>
import { Moment } from 'cnd-utils'
import { getDictet } from '@/api/logistics/saleDelivery/saleorder'
import { getTaskList } from '@/api/sysConfig/travellingMerchant'
import { getCnDitc } from '@/utils/common'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import mixins from '../mixins'
import moment from 'moment'
export default {
  name: 'Tasks',
  components: { steelTradeAggrid },
  mixins: [mixins],
  data() {
    const startDate = moment()
      .subtract(90, 'day')
      .startOf('day')
      .format('YYYY-MM-DD HH:mm:ss')
    const endDate = moment().endOf('day').format('YYYY-MM-DD HH:mm:ss')
    return {
      disRemove: true,
      options: {
        'msb.merchants.disposeStatus': [],
        'msb.merchants.methodHandling': []
      },
      headerCount: null,
      footerCount: null,
      searchInfo: null,
      formItems: [
        {
          label: '待办编码',
          value: 'sPendingTaskCode',
          type: 'elInput'
        },
        {
          label: '待办内容',
          value: 'sPendingTaskContent',
          type: 'elInput'
        },
        {
          label: '处理结果',
          value: 'sProcessResult',
          type: 'elInput'
        },
        {
          label: '状态',
          value: 'sProcessStatus',
          type: 'elSelect',
          dict: 'msb.merchants.disposeStatus'
        },
        {
          label: '创建人',
          value: 'sCreator',
          type: 'cndInputDialog',
          placeholder: this.$t('grid.others.pleaseSelectTheCreator'),
          dialogType: 'applicant'
        },
        {
          label: '创建日期',
          value: ['sCreateTimeFrom', 'sCreateTimeTo'],
          placeholder: [
            this.$t('grid.others.pleaseSelectTheDate'),
            this.$t('grid.others.pleaseSelectTheDate')
          ],
          default: [startDate, endDate],
          unlinkPanels: true,
          type: 'elDatePicker'
        },
        {
          label: '修改人',
          value: 'sModifier',
          type: 'cndInputDialog',
          dialogType: 'applicant'
        },
        {
          label: '修改日期',
          value: ['sModifyTimeFrom', 'sModifyTimeTo'],
          placeholder: [
            this.$t('grid.others.pleaseSelectTheDate'),
            this.$t('grid.others.pleaseSelectTheDate')
          ],
          default: ['', ''],
          unlinkPanels: true,
          type: 'elDatePicker'
        }
      ],
      columnDefs: [
        {
          headerName: '待办编码',
          field: 'sPendingTaskCode',
          width: '250px'
        },
        {
          headerName: '内容事项',
          field: 'sPendingTaskContent',
          width: '250px'
        },
        {
          headerName: '处理结果',
          field: 'sProcessResult',
          width: '150px'
        },
        {
          headerName: '提醒时间',
          field: 'sReminderDate',
          width: '150px'
        },
        {
          headerName: '计划处理日期',
          field: 'sPlannedProcessTime',
          width: '150px'
        },
        {
          headerName: '实际处理日期',
          field: 'sActualProcessTime',
          width: '150px',
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
          }
        },
        {
          headerName: '状态',
          field: 'sProcessStatus',
          width: '100px',
          valueGetter: (params) => {
            return getCnDitc(params, this.options['msb.merchants.disposeStatus'], 'sProcessStatus')
          }
        },
        {
          headerName: '处理方式',
          field: 'sProcessMethod',
          width: '150px',
          valueGetter: (params) => {
            return getCnDitc(params, this.options['msb.merchants.methodHandling'], 'sProcessMethod')
          }
        },
        {
          headerName: '关联拜访计划编号',
          field: 'sourceCode',
          width: '150px'
        },
        {
          headerName: '创建人',
          field: 'sCreatorName'
        },
        {
          headerName: '创建时间',
          field: 'sCreateTime',
          cellStyle: { textAlign: 'right' },
          width: '180px',
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
          }
        },
        {
          headerName: '修改人',
          field: 'sModifierName'
        },
        {
          headerName: '修改时间',
          field: 'sModifyTime',
          cellStyle: { textAlign: 'right' },
          width: '140px',
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sModifyTime)
          }
        }
      ],
      rowData: [],
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      }
    }
  },
  computed: {},
  beforeCreate() {
    // 获取字典
    getDictet([
      'msb.merchants.disposeStatus',
      'msb.merchants.methodHandling'
    ])
      .then((result) => {
        this.options['msb.merchants.disposeStatus'] = result.data[0].dicts
        this.options['msb.merchants.methodHandling'] = result.data[1].dicts
      })
      .catch(() => {})
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },

    filteringTime(time) {
      return time.replace('T', ' ')
    },
    // 主表
    loadData(pagination) {
      const formData = {
        ...this.$refs.searchForm.getSearchData(),
        sCreateTimeFrom: this.$refs.searchForm.getSearchData().sCreateTimeFrom
          ? this.filteringTime(this.$refs.searchForm.getSearchData().sCreateTimeFrom) : '',
        sCreateTimeTo: this.$refs.searchForm.getSearchData().sCreateTimeTo
          ? this.filteringTime(this.$refs.searchForm.getSearchData().sCreateTimeTo) : '',
        sModifyTimeFrom: this.$refs.searchForm.getSearchData().sModifyTimeFrom
          ? this.filteringTime(this.$refs.searchForm.getSearchData().sModifyTimeFrom) : '',
        sModifyTimeTo: this.$refs.searchForm.getSearchData().sModifyTimeTo
          ? this.filteringTime(this.$refs.searchForm.getSearchData().sModifyTimeTo) : ''
      }
      return new Promise((resolve, reject) => {
        getTaskList(formData, {
          ...pagination
        })
          .then((res) => {
            this.rowData = res.data.content.map((item) => {
              item._selected = false
              item._selectedKeys = []
              return item
            })
            resolve(res.data.totalElements)
          })
          .catch(() => {
            reject(0)
          })
      })
    }
  }
}
</script>
