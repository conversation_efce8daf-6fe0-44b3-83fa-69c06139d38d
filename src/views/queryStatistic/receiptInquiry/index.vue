<template>
  <div class="page-container">
    <p class="page-title">收款往来查询</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form
        ref="searchForm"
        :form-items="formItems"
        @search="onSearch"
      />

      <div class="btn-group mt-10">
        <div class="text">
          收款往来查询列表
        </div>
      </div>
      <!-- 收款往来查询列表 -->
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        table-selection="multiple"
        :auto-load-data="false"
        row-key="sGroupKey"
        @pageChange="selectId = null"
        @rowClicked="rowClicked"
        @selectedChange="selectedChange"
      />

      <div class="btn-group mt-10">
        <div class="text">
          往来明细
        </div>
      </div>
      <steelTradeAggrid
        ref="detailAggrid"
        :column-defs="detailColumnDefs"
        :row-data="detailRowData"
        :load-data="loadDetailData"
        :auto-load-data="false"
        table-selection="multiple"
        :paginationinif="false"
        row-key="sId"
        @selectedChange="detailSelectedChange"
      />
    </div>

  </div>
</template>

<script>
// import Vue from 'vue'
var Decimal = window.Decimal
import steelTradeAggrid from '@/components/steelTradeAggrid'

import {
  receiptInquirtPage,
  receiptInquirtPageDetail
} from '@/api/queryStatistic/purchase'
import {
  getCnDitc,
  getDictet
} from '@/api/logistics/saleDelivery/saleorder'
import { SteelFormat } from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
// import { Middleware } from 'cndinfo-ui'
export default {
  name: 'ReceiptInquiry',
  components: { steelTradeAggrid },
  data() {
    return {
      searchInfo: null,
      selectOps: {
        'pay.subtype': [],
        'dev.common.sheet.status': []
      },
      formItems: [
        {
          label: this.$t('grid.title.salesContractNumber'),
          value: 'sSaleContractCode',
          type: 'elInput',
          placeholder: this.$t('grid.others.pleaseEnterTheSalesContractNumber')
        },
        {
          label: this.$t('grid.others.customer'),
          value: 'sCustomerId',
          type: 'cndInputDialogItem',
          dialogType: 'customer',
          defaultUrl: '/esc/customer/page',
          option: { valueKey: 'sPath' },
          placeholder: this.$t('grid.others.pleaseSelectTheCustomer')
        },
        {
          label: this.$t('grid.title.company'),
          value: 'sCompanyId',
          type: 'cndInputDialog',
          dialogType: 'company',
          placeholder: this.$t('grid.others.pleaseSelectCompany')
        },
        {
          label: '往来类型',
          value: 'sInteractionSubclass',
          type: 'elSelect',
          dict: [{
            sCodeName: '暂收款',
            sCodeValue: '190'
          }, {
            sCodeName: '预收货款',
            sCodeValue: '210'
          }, {
            sCodeName: '应收保证金',
            sCodeValue: '180'
          }, {
            sCodeName: '应收货款',
            sCodeValue: '110'
          }]
        },
        {
          label: '来源单据号',
          value: 'sUpSheetCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.title.accountingGroup'),
          value: 'sCheckGroupId',
          type: 'cndInputDialog',
          dialogType: 'cost',
          placeholder: this.$t('grid.others.pleaseSelectAccountingGroup')
        },
        {
          label: this.$t('grid.others.itemNumberTag'),
          value: 'sProjectCode',
          type: 'elInput',
          placeholder: this.$t('grid.others.pleaseEnterTheItemNumberTag')
        },
        {
          label: this.$t('grid.others.dateOfTransaction'),
          value: ['sPayDate', 'sPayDateTo'],
          itemType: 'occultation',
          unlinkPanels: true,
          type: 'elDatePicker'
        }
      ],
      columnDefs: [
        {
          headerName: '往来类型',
          field: 'sInteractionSubclass',
          valueGetter: params => {
            return getCnDitc(params, this.selectOps['pay.subtype'], 'sInteractionSubclass')
          }
        },
        //   {
        //   headerName: this.$t('grid.others.merchants'),
        //   field: 'vCustomerName'
        // },
        {
          headerName: this.$t('grid.others.customer'),
          field: 'vCustomerName'
        },
        {
          headerName: this.$t('grid.title.company'),
          field: 'vCompanyName'
        },
        {
          headerName: '来款金额',
          field: 'vTaxAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: '使用金额',
          field: 'vPayAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: '剩余金额',
          field: 'vLeftAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: '来源单据号',
          field: 'sUpSheetCode'
        },
        {
          headerName: this.$t('grid.others.itemNumberTag'),
          field: 'sProjectCode'
        },
        {
          headerName: this.$t('grid.title.salesContractNumber'),
          field: 'sSaleContractCode'
        },
        {
          headerName: '往来日期',
          field: 'sPayDate',
          valueGetter: params => Moment.time('YYYY-MM-DD', params.data.sPayDate),
          valueFormatter: params => {
            return Moment.time('YYYY-MM-DD', params.data.sPayDate)
          }
        }
        // {
        //   headerName: this.$t('grid.title.accountingGroup'),
        //   field: 'vCheckGroupName'
        // },
      ],
      detailColumnDefs: [
        {
          headerName: this.$t('grid.others.customer'),
          field: 'vCustomerName'
        },
        {
          headerName: this.$t('grid.title.accountingGroup'),
          field: 'vCheckGroupName'
        },
        {
          headerName: this.$t('grid.title.salesContractNumber'),
          field: 'sSaleContractCode'
        },
        {
          headerName: '金额',
          field: 'sTaxAmt',
          cellStyle: { textAlign: 'right', color: 'red' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: '来源单据号',
          field: 'sUpSheetCode'
        },
        {
          headerName: '往来类型',
          field: 'sInteractionSubclass',
          valueGetter: params => {
            return getCnDitc(params, this.selectOps['pay.subtype'], 'sInteractionSubclass')
          }
        },
        {
          headerName: this.$t('grid.title.status'),
          field: 'sSheetStatus',
          valueGetter: params => {
            return getCnDitc(params, this.selectOps['dev.common.sheet.status'], 'sSheetStatus')
          }
        },
        {
          headerName: this.$t('grid.title.createdAt'),
          minWidth: 150,
          field: 'sCreateTime',
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
          }
        }
      ],
      rowData: [],
      detailRowData: [],
      selectId: '',
      selectSaleContractId: '',
      currIndex: 0
    }
  },
  created() {
    getDictet([
      'pay.subtype',
      'dev.common.sheet.status'
    ]).then(result => {
      this.selectOps['pay.subtype'] = result.data[0].dicts
      this.selectOps['dev.common.sheet.status'] = result.data[1].dicts
    }).catch(() => {
    })
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      this.selectId = null
      const searchInfo = this.$refs.searchForm.getSearchData()
      searchInfo.vIsRebate = searchInfo.vIsRebate || '1'
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    computeTotal(type = 'all') {
      return Object.assign(this.rowData.reduce((prev, next) => {
        Object.keys(prev).forEach(key => {
          if (type === 'all') {
            prev[key] = new Decimal(+prev[key]).add(+next[key] || 0)
          } else {
            if (next._selected) {
              prev[key] = new Decimal(+prev[key]).add(+next[key] || 0)
            }
          }
        })
        return prev
      }, {
        vTaxAmt: 0,
        vPayAmt: 0,
        vLeftAmt: 0
      }), {
        sInteractionSubclass: type === 'all' ? this.$t('grid.others.amountTo') : `${this.$t('components.selected')}${this.rowData.filter(item => item._selected).length}${this.$t('pagination.items')}`,
        vCustomerName: null,
        vCompanyName: null,
        sUpSheetCode: null,
        sProjectCode: null,
        sSaleContractCode: null,
        sPayDate: null,
        _selected: false,
        _hiddenCheckbox: true
      })
    },
    computeDetailTotal(type = 'all') {
      return Object.assign(this.detailRowData.reduce((prev, next) => {
        Object.keys(prev).forEach(key => {
          if (type === 'all') {
            prev[key] = new Decimal(+prev[key]).add(+next[key] || 0)
          } else {
            if (next._selected) {
              prev[key] = new Decimal(+prev[key]).add(+next[key] || 0)
            }
          }
        })
        return prev
      }, {
        sTaxAmt: 0
      }), {
        vCustomerName: type === 'all' ? this.$t('grid.others.amountTo') : `${this.$t('components.selected')}${this.detailRowData.filter(item => item._selected).length}${this.$t('pagination.items')}`,
        vCheckGroupName: null,
        sSaleContractCode: null,
        sUpSheetCode: null,
        sInteractionSubclass: null,
        sCreateTime: null,
        _selected: false,
        _hiddenCheckbox: true
      })
    },
    selectedChange(rowData) {
      this.$refs.aggrid.getSelectedData(res => {
        setTimeout(() => {
          this.$refs.aggrid.gridApi.setPinnedBottomRowData([this.computeTotal(res.length ? 'selected' : 'all')])
        }, 0)
      })
    },
    detailSelectedChange() {
      this.$refs.detailAggrid.getSelectedData(res => {
        setTimeout(() => {
          this.$refs.detailAggrid.gridApi.setPinnedBottomRowData([this.computeDetailTotal(res.length ? 'selected' : 'all')])
        }, 0)
      })
    },
    rowClicked(params) {
      if (params?.data && !params.data._hiddenCheckbox && this.selectId !== params.data.sGroupKey) {
        this.selectId = params.data.sGroupKey
        this.$refs.detailAggrid.loadTableData()
      }
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        receiptInquirtPage(this.searchInfo, pagination).then(res => {
          this.rowData = res.data.content.map((item, index) => {
            if (this.selectId) {
              item._selected = this.selectId === item.sGroupKey
            } else {
              item._selected = index === 0
            }
            item._selectedKeys = []
            return item
          })
          resolve(res.data)
          this.rowClicked({ data: res.data.content[0] })
        }).catch(() => {
          reject([])
        })
      })
    },
    loadDetailData(pagination) {
      return new Promise((resolve, reject) => {
        receiptInquirtPageDetail(this.selectId, pagination).then(res => {
          this.detailRowData = res.data.map(item => {
            item._selected = false
            item._selectedKeys = []
            return item
          })
          resolve(res.data)
        }).catch(() => {
          reject([])
        })
      })
    },
    handleDetailCount(list) {

    }
  }
}
</script>
