<template>
  <div class="page-container">
    <p class="page-title">购销关系查询工作台</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form
        ref="searchForm"
        :form-items="formItems"
        @search="onSearch"
      />
      <div class="btn-group mt-10">
        <div class="text">
          购销关系查询列表
        </div>
        <div>
          <el-button
            type="primary"
            size="mini"
            @click="openModifyDialog('release')"
          >
            释放购销
          </el-button>
          <el-button
            type="primary"
            size="mini"
            @click="openModifyDialog('establish')"
          >
            建立购销
          </el-button>
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        table-selection="single"
        row-key="sId"
        @selectedChange="handleFooterCount"
      />
    </div>
    <cnd-dialog
      v-if="modifyDialog"
      :visible="modifyDialog"
      :fullscreen="false"
      append-to-body
      :title="modifyConfig[modifyDialogType].title"
      width="350px"
      :height="modifyConfig[modifyDialogType].height"
      @close="modifyDialog = false"
    >
      <template slot="content">
        <div>
          <el-form
            :model="modifyForm"
            label-width="60px"
            size="small"
            @submit.native.prevent
          >
            <template v-if="modifyDialogType === 'release'">
              <cnd-form-item
                label="释放数量"
                :custom-width="20"
              >
                <cnd-input-number
                  v-model="modifyForm.releaseData"
                  type="amount"
                  :decimal-digit="6"
                  clearable
                  :placeholder="$t('components.pleaseEnter')"
                  @keyup.enter.native="modifySubmit"
                />
              </cnd-form-item>
            </template>
            <template v-if="modifyDialogType === 'establish'">
              <cnd-form-item
                label="建立数量"
                :custom-width="20"
              >
                <cnd-input-number
                  v-model="modifyForm.establishData"
                  type="amount"
                  :decimal-digit="6"
                  clearable
                  :placeholder="$t('components.pleaseEnter')"
                  @keyup.enter.native="modifySubmit"
                />
              </cnd-form-item>
            </template>
          </el-form>
        </div>
      </template>
      <template slot="footer">
        <el-button
          size="mini"
          @click="modifyDialog = false"
        >{{ $t('btns.cancel') }}</el-button>
        <el-button
          type="primary"
          size="mini"
          @click="modifySubmit"
        >{{ $t('btns.confirm') }}</el-button>
      </template>
    </cnd-dialog>
  </div>
</template>
<script>
import { SteelFormat } from 'cnd-horizon-utils'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import { getDictList, getCnDitc } from '@/utils/dict'
import {
  contractExt,
  contractExtRel
} from '@/api/queryStatistic/common'
export default {
  name: 'PurchaseSalesRelation',
  components: { steelTradeAggrid },
  data() {
    return {
      formItems: [
        {
          label: this.$t('grid.title.purchaseContractNumber'),
          value: 'sPurCode',
          type: 'elInput',
          placeholder: this.$t('grid.others.enterPurchaseContractNumber')
        },
        {
          label: this.$t('grid.title.salesContractNumber'),
          value: 'sSaleCode',
          type: 'elInput',
          placeholder: this.$t('grid.others.pleaseEnterTheSalesContractNumber')
        },
        {
          label: this.$t('grid.others.department'),
          value: 'sDepartmentId',
          type: 'cndInputDialog',
          dialogType: 'depart',
          placeholder: this.$t('grid.others.pleaseSelectTheDepartment')
        },
        {
          label: this.$t('grid.title.accountingGroup'),
          value: 'sCheckGroupId',
          type: 'cndInputDialog',
          dialogType: 'cost',
          placeholder: this.$t('grid.others.pleaseSelectAccountingGroup')
        },
        {
          label: this.$t('grid.title.personnel'),
          value: 'sStaffId',
          type: 'cndInputDialogItem',
          option: { valueKey: 'sPath' },
          dialogType: 'staff',
          placeholder: this.$t('grid.others.pleaseSelectPersonnel')
        },
        {
          label: '采购公司',
          value: 'sCompanyId',
          type: 'cndInputDialog',
          dialogType: 'company',
          placeholder: this.$t('grid.others.pleaseSelectCompany')
        },
        {
          label: '经营单位',
          value: 'sManagementId',
          type: 'cndInputDialog',
          dialogType: 'escOrg',
          itemType: 'occultation'
        }
      ],
      searchInfo: null,
      statusList: [
        { 'sCodeValue': '10',
          'sCodeName': '准备',
          'sSort': 10,
          'sFilter': null,
          'sIsEnabled': '1',
          'sLanguage': 'zh_CN'
        },
        { 'sCodeValue': '11',
          'sCodeName': '修改',
          'sSort': 11,
          'sFilter': null,
          'sIsEnabled': '1',
          'sLanguage': 'zh_CN'
        },
        { 'sCodeValue': '15',
          'sCodeName': '驳回',
          'sSort': 15,
          'sFilter': null,
          'sIsEnabled': '1',
          'sLanguage': 'zh_CN'
        },
        {
          'sCodeValue': '30',
          'sCodeName': '待审',
          'sSort': 30,
          'sFilter': null,
          'sIsEnabled': '1',
          'sLanguage': 'zh_CN'
        },
        { 'sCodeValue': '70',
          'sCodeName': '执行',
          'sSort': 70,
          'sFilter': null,
          'sIsEnabled': '1',
          'sLanguage': 'zh_CN'
        },
        {
          'sCodeValue': '75',
          'sCodeName': this.$t('grid.others.completion'),
          'sSort': 75,
          'sFilter': null,
          'sIsEnabled': '1',
          'sLanguage': 'zh_CN'
        }
      ],
      columnDefs: [
        {
          headerName: '采购合同号',
          field: 'sPurContractCode'
        },
        {
          headerName: '采购合同状态',
          field: 'sPurSheetStatus',
          valueGetter: params => {
            return getCnDitc(params, this.statusList, 'sPurSheetStatus')
          }
        },
        {
          headerName: '采购可销售数量',
          field: 'sPurContractQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: '销售合同号',
          field: 'sSaleContractCode'
        },
        {
          headerName: '销售合同状态',
          field: 'sSaleSheetStatus',
          valueGetter: params => {
            return getCnDitc(params, this.statusList, 'sSaleSheetStatus')
          }
        },
        {
          headerName: '销售合同数量',
          field: 'sSaleContractQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: '建立关系数量',
          field: 'sRelContractQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: '已使用数量',
          field: 'sUseContractQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: '品名',
          field: 'vGoodsDetailName'
        },
        {
          headerName: '部门',
          field: 'vDepartmentName'
        },
        {
          headerName: '核算组',
          field: 'vCheckGroupName'
        },
        {
          headerName: '人员',
          field: 'vStaffName'
        },
        {
          headerName: '采购公司',
          field: 'vCompanyName'
        },
        {
          headerName: '经营单位',
          field: 'vManagementName'
        }
      ],
      rowData: [],
      options: {
        'dev.common.sheet.status': []
      },
      modifyForm: {
        establishData: null,
        releaseData: null
      },
      modifyData: null,
      modifyDialog: false,
      modifyDialogType: '',
      modifyConfig: {
        release: {
          title: '释放购销',
          height: '40px'
        },
        establish: {
          title: '建立购销',
          height: '40px'
        }
      }
    }
  },
  mounted() {
    getDictList(this.options)
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        contractExt(
          this.searchInfo,
          pagination
        ).then(res => {
          this.rowData = res.data.content.map(item => {
            item._selected = false
            return item
          })
          resolve(res.data)
        }).catch(() => {
          reject([])
        })
      })
    },
    handleFooterCount() {
    },
    openModifyDialog(type) {
      this.modifyDialogType = type
      const details = this.rowData.filter(item => item._selected)
      if (details.length > 0) {
        this.modifyForm.establishData = null
        this.modifyForm.releaseData = null
        this.modifyDialog = true
        this.modifyData = details[0]
      } else {
        this.$message.warning(this.$t('grid.others.pleaseCheckTheBox'))
      }
    },
    modifySubmit() {
      const { establishData, releaseData } = this.modifyForm
      const type = this.modifyDialogType === 'release' ? '0' : '1'
      const releaseQty = this.modifyDialogType === 'release' ? releaseData : establishData
      contractExtRel(type, {
        ...this.modifyData,
        releaseQty
      }).then((res) => {
        this.$message.success(this.$t('grid.others.modifySuccessfully'))
        this.modifyDialog = false
        this.$refs.aggrid.loadTableData()
      })
    }
  }
}
</script>

<style scoped>

</style>
