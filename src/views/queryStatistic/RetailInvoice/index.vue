<template>
  <div class="page-container">
    <p class="page-title">零售发货明细查询</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form
        ref="searchForm"
        :form-items="formItems"
        @search="onSearch"
      />
      <div class="btn-group mt-10">
        <div class="text">
          零售发货明细列表
        </div>
        <div>
          <export-btn
            class="ml-10"
            :file-name="'零售发货明细'"
            api-url="/esc/retail/mall/project/receive/export"
            :post-params="searchInfo"
            :export-config="exportConfig"
            :get-grid-api="getGridApi"
          />
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        table-selection="multiple"
        row-key="sId"
        :footer-total="footerTotal"
        @selectedChange="handleFooterCount"
      />
    </div>
  </div>
</template>
<script>
import { SteelFormat } from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import exportBtn from '@/components/exportBtnV2'
import {
  retailMallStats
} from '@/api/queryStatistic/common'
export default {
  name: 'RetailInvoice',
  components: { steelTradeAggrid, exportBtn },
  data() {
    return {
      formItems: [
        {
          label: this.$t('grid.others.shippingOrderNumber'),
          value: 'sCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.others.customer'),
          value: 'sCustomerId',
          type: 'cndInputDialogItem',
          dialogType: 'customer',
          defaultUrl: '/esc/customer/page',
          option: { valueKey: 'sPath' }
        },
        {
          label: this.$t('grid.title.company'),
          value: 'sCompanyId',
          type: 'cndInputDialog',
          dialogType: 'company',
          placeholder: this.$t('grid.others.pleaseSelectCompany')
        },
        {
          label: this.$t('grid.others.warehouse'),
          value: 'sWarehouseId',
          type: 'cndInputDialogItem',
          defaultUrl: '/esc/warehouse/info/dialog/v2/page-dialog',
          option: { valueKey: 'sPath' },
          placeholder: this.$t('grid.others.pleaseSelectAWarehouse')
        },
        {
          label: this.$t('grid.others.shippingDate'),
          value: ['sDeliveryNotifyDate', 'sDeliveryNotifyDateTo'],
          placeholder: [this.$t('grid.others.pleaseSelectTheDate'), this.$t('grid.others.pleaseSelectTheDate')],
          unlinkPanels: true,
          type: 'elDatePicker'
        },
        {
          label: this.$t('grid.others.itemNumberTag'),
          value: 'sProjectCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.others.supplier'),
          value: 'sSupplierId',
          type: 'cndInputDialogItem',
          dialogType: 'customer',
          defaultUrl: '/esc/customer/page',
          option: { valueKey: 'sPath' },
          placeholder: this.$t('grid.others.pleaseSelectSupplier')
        },
        // 自营类型
        {
          label: '自营类型',
          value: 'sSelfEmployedType',
          type: 'elSelect',
          dict: 'esc.self.employed.type',
          placeholder: '请输入自营类型'
        },
        {
          label: this.$t('grid.title.accountingGroup'),
          value: 'sCheckGroupId',
          type: 'cndInputDialog',
          dialogType: 'cost',
          placeholder: this.$t('grid.others.pleaseSelectAccountingGroup')
        },
        {
          label: this.$t('grid.others.operatingUnit'),
          value: 'sManagementId',
          type: 'cndInputDialog',
          dialogType: 'escOrg',
          itemType: 'occultation'
        }
      ],
      searchInfo: null,
      columnDefs: [
        {
          headerName: this.$t('grid.others.shippingOrderNumber'),
          field: 'sCode'
        },
        {
          headerName: this.$t('grid.others.steelCoilNumber'),
          field: 'sExtend4'
        },
        {
          headerName: this.$t('grid.others.item'),
          field: 'goodsName'
        },
        {
          headerName: this.$t('grid.others.material'),
          field: 'mateName'
        },
        {
          headerName: this.$t('grid.others.specification'),
          field: 'specName'
        },
        {
          headerName: this.$t('grid.title.quantity'),
          field: 'sContractQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: this.$t('grid.others.numberOfPiecesTag'),
          field: 'sQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: this.$t('grid.others.salesUnitPrice'),
          field: 'sTaxPrice',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: this.$t('grid.title.amount'),
          field: 'sTaxAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: this.$t('grid.others.customer'),
          field: 'vCustomerName'
        },
        {
          headerName: this.$t('grid.title.company'),
          field: 'vCompanyName'
        },
        {
          headerName: this.$t('grid.others.warehouse'),
          field: 'vWarehouseName'
        },
        {
          headerName: this.$t('grid.others.shippingDate'),
          field: 'sDeliveryNotifyDate',
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD', params.data.sDeliveryNotifyDate)
          }
        },
        {
          headerName: this.$t('grid.others.originalDocumentNumber'),
          field: 'sUpCode'
        },
        {
          headerName: this.$t('grid.others.itemNumberTag'),
          field: 'sProjectCode'
        },
        {
          headerName: this.$t('grid.others.procurementContractNumber'),
          field: 'sPurContractCode'
        },
        {
          headerName: this.$t('grid.others.supplier'),
          field: 'vSupplierName'
        },
        {
          headerName: '自营类型',
          field: 'sSelfEmployedTypeName'
        },
        {
          headerName: this.$t('grid.title.accountingGroup'),
          field: 'vCheckGroupName'
        },
        {
          headerName: this.$t('grid.title.personnel'),
          field: 'vStaffName'
        },
        {
          headerName: this.$t('grid.title.remarks'),
          field: 'sRemark'
        },
        {
          headerName: '是否定向',
          field: 'direction'
        },
        {
          headerName: '经营单位',
          field: 'vManagementName'
        }
      ],
      exportConfig: [
        {
          label: this.$t('grid.others.shippingOrderNumber'),
          value: 'sCode'
        },
        {
          label: this.$t('grid.others.steelCoilNumber'),
          value: 'sExtend4'
        },
        {
          label: this.$t('grid.others.item'),
          value: 'goodsName'
        },
        {
          label: this.$t('grid.others.material'),
          value: 'mateName'
        },
        {
          label: this.$t('grid.others.specification'),
          value: 'specName'
        },
        {
          label: this.$t('grid.title.quantity'),
          value: 'sContractQty',
          setValue: value => { return Number((+value).toFixed(4)) }
        },
        {
          label: this.$t('grid.others.numberOfPiecesTag'),
          value: 'sQty'
        },
        {
          label: this.$t('grid.others.salesUnitPrice'),
          value: 'sTaxPrice',
          setValue: value => { return Number((+value).toFixed(2)) }
        },
        {
          label: this.$t('grid.title.amount'),
          value: 'sTaxAmt',
          setValue: value => { return Number((+value).toFixed(2)) }
        },
        {
          label: this.$t('grid.others.customer'),
          value: 'vCustomerName'
        },
        {
          label: this.$t('grid.title.company'),
          value: 'vCompanyName'
        },
        {
          label: this.$t('grid.others.warehouse'),
          value: 'vWarehouseName'
        },
        {
          label: this.$t('grid.others.shippingDate'),
          value: 'sDeliveryNotifyDate',
          setValue: value => { return Moment.time('YYYY-MM-DD', value) }
        },
        {
          label: this.$t('grid.others.originalDocumentNumber'),
          value: 'sUpCode'
        },
        {
          label: this.$t('grid.others.itemNumberTag'),
          value: 'sProjectCode'
        },
        {
          label: this.$t('grid.others.procurementContractNumber'),
          value: 'sPurContractCode'
        },
        {
          label: this.$t('grid.others.supplier'),
          value: 'vSupplierName'
        },
        {
          label: '自营类型',
          value: 'sSelfEmployedTypeName'
        },
        {
          label: this.$t('grid.title.accountingGroup'),
          value: 'vCheckGroupName'
        },
        {
          label: this.$t('grid.title.personnel'),
          value: 'vStaffName'
        },
        {
          label: this.$t('grid.title.remarks'),
          value: 'sRemark'
        },
        {
          label: '是否定向',
          value: 'direction'
        },
        {
          label: '经营单位',
          value: 'vManagementName'
        }
      ],
      rowData: [],
      footerTotal: null,
      footerTotalAmt: null,
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      }
    }
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    setTotal(vCount = 0, sContractQtyAmt = 0, sQtyAmt = 0, sTaxAmtAll = 0, flag, title) {
      this[flag] = [
        { title: title, count: vCount, key: 'count' },
        { title: '数量', count: SteelFormat.formatThousandthSign(sContractQtyAmt, 4), unit: this.$t('grid.others.ton') },
        { title: '件数', count: SteelFormat.formatThousandthSign(sQtyAmt), unit: this.$t('grid.others.pieces') },
        { title: '金额', count: SteelFormat.formatPrice(sTaxAmtAll), unit: this.$t('grid.others.yuan') }
      ]
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        retailMallStats(
          this.searchInfo,
          pagination
        ).then(res => {
          this.rowData = res.data.deliveryPage.content.map(item => {
            item._selected = false
            return item
          })
          this.footerTotalAmt = res.data
          resolve(res.data.deliveryPage)
        }).catch(() => {
          reject([])
        })
      })
    },
    handleFooterCount(list) {
      console.log(list)
      const details = list.filter(item => item._selected)
      const vCount = details.length
      if (vCount > 0) {
        let sContractQtyAmt = 0
        let sQtyAmt = 0
        let sTaxAmtAll = 0
        details.forEach(el => {
          sContractQtyAmt += (Number(el.sContractQty) || 0)
          sQtyAmt += (Number(el.sQty) || 0)
          sTaxAmtAll += (Number(el.sTaxAmt) || 0)
        })
        this.setTotal(vCount, sContractQtyAmt, sQtyAmt, sTaxAmtAll, 'footerTotal', this.$t('components.selected'))
      } else {
        const { vCount, vSumContractQty, vSumQty, vSumAmt } = this.footerTotalAmt
        this.setTotal(vCount, vSumContractQty, vSumQty, vSumAmt, 'footerTotal', this.$t('pagination.total'))
      }
    }
  }
}
</script>

<style scoped>

</style>
