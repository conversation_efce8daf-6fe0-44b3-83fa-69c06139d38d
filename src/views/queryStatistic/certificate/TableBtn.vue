<template>
  <div class="flexC h100">
    <!-- 证据截图 -->
    <template v-if="params.colDef && params.colDef.field==='sFileId'">
      <a :href="params.data.sFilePath" target="_blank" class="flexC">
        <cnd-icon class="eyes-icon" name="cnd-eyes" width="1.2rem" height="1.2rem" />
      </a>
      <cnd-icon name="cnd-download" class="ml-10 download-icon pointer" width="1.2rem" height="1.2rem" @click="download('sFileId')" />
    </template>
    <!-- 证据证明 -->
    <template v-if="params.data.sIsUpload === '1' && params.colDef.field==='sCertFileId'">
      <a :href="params.data.sCertPath" target="_blank" class="flexC">
        <cnd-icon class="eyes-icon" name="cnd-eyes" width="1.2rem" height="1.2rem" />
      </a>
      <cnd-icon name="cnd-download" class="ml-10 download-icon pointer" width="1.2rem" height="1.2rem" @click="download('sCertFileId')" />
    </template>
  </div>
</template>

<script>
import Vue from 'vue'
import axios from 'axios'
import { MessageUtil } from 'cnd-horizon-utils'
import { getToken } from 'cnd-horizon-utils/src/utils/auth'
import {
  getEviId
} from '@/api/queryStatistic/purchase'
export default Vue.extend({
  data() {
    return {}
  },
  mounted() {
    console.log('this.params', this.params)
  },
  methods: {
    downloadFile() {
      window.open(this.params.data.path)
    },
    ossDownload(id, fileName) {
      axios.post(
        process.env.VUE_APP_BASE_API + '/annex/oss/download/' + id,
        {},
        {
          responseType: 'blob',
          headers: {
            Authorization: getToken()
          }
        }
      ).then((res) => {
        const blob = new Blob([res.data]) // 处理文档流
        const elink = document.createElement('a')
        elink.download = fileName
        elink.style.display = 'none'
        elink.href = URL.createObjectURL(blob)
        document.body.appendChild(elink)
        elink.click()
        URL.revokeObjectURL(elink.href) // 释放URL 对象
        document.body.removeChild(elink)
        this.$nextTick(() => {
          MessageUtil.success(this.$t('tips.downloadSuccessful'))
        })
      })
    },
    download(type) {
      if (type === 'sFileId') {
        this.ossDownload(this.params.data.sFileId, this.params.data.sFileName)
      } else {
        if (this.params.data.sCertId && this.params.data.sCertPath) {
          this.ossDownload(this.params.data.sCertFileId, this.params.data.sCertFileName)
        } else {
          getEviId(this.params.data.sId).then(res => {
            this.ossDownload(res.data.sCertFileId, res.data.sCertFileName)
          })
        }
      }
    },
    // 格式化获取url
    formatUrl(params = {}, url) {
      let urlStr = ''
      if (url.indexOf('{') > -1) {
        const urlArr = url.split('/')
        // console.log('urlArr前--', urlArr)
        urlArr.forEach((item, index) => {
          if (index !== 0) {
            let temp
            if (item.indexOf('{') > -1) {
              // console.log(item.substring(1, item.length - 1))
              temp = `/${params[item.substring(1, item.length - 1)]}`
            } else {
              temp = `/${item}`
            }
            urlStr += temp
          }
        })
        // console.log('urlStr--', urlStr)
      } else {
        urlStr = url
      }
      return urlStr
    }
  }
})
</script>

<style scoped lang="scss">
.download-icon:hover, .eyes-icon:hover {
  color: #66B1FF;
}
.download{
  color: #66B1FF;
  margin-left: 35px;
}
</style>
