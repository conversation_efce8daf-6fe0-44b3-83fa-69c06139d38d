<template>
  <div class="page-container">
    <p class="page-title">锐眼存证查询</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form ref="searchForm" :form-items="formItems" @search="onSearch" />
      <div class="btn-group mt-10" style="display: flex">
        <div class="text" style="flex:1">
          锐眼存证查询列表
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :header-total="headerCount"
        :footer-total="footerCount"
        :auto-load-data="false"
        table-selection="multiple"
        @selectedChange="handleFooterCount"
        @cellValueChanged="cellValueChanged"
      />
    </div>
  </div>
</template>

<script>
// import { Moment } from 'cnd-utils'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import exportBtn from '@/components/exportBtnV2'
import { DictUtil } from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
import {
  getEviPage
} from '@/api/queryStatistic/purchase'
import TableBtn from './TableBtn'

export default {
  name: 'Certificate',
  components: { steelTradeAggrid, exportBtn },
  data() {
    return {
      searchInfo: null,
      formItems: [
        {
          label: '单据号',
          value: 'sCode',
          default: '',
          type: 'elInput'
        },
        {
          label: '证据编号',
          value: 'sCertId',
          default: '',
          type: 'elInput'
        },
        {
          label: '类型',
          value: 'sType',
          type: 'elSelect',
          dict: 'evidence.type'
        },
        {
          label: '是否上传',
          value: 'sIsUpload',
          type: 'elSelect',
          dict: 'base.yes-no'
        },
        {
          label: '更新时间',
          value: ['sStartTime', 'sEndTime'],
          placeholder: [this.$t('grid.others.pleaseSelectTheDate'), this.$t('grid.others.pleaseSelectTheDate')],
          unlinkPanels: true,
          type: 'elDatePicker'
        }
      ],
      columnDefs: [

      ],
      exportConfig: [

      ],
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      headerCount: null,
      footerCount: null,
      rowData: [],
      typeList: [],
      yesorno: []
    }
  },
  mounted() {
    this.loadDict()
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    loadDict() {
      DictUtil.getDict([
        'evidence.type',
        'base.yes-no'
      ], res => {
        this.typeList = res[0].dicts
        this.yesorno = res[1].dicts
      })
    },
    loadData(pagination) {
      this.rowData = []
      return new Promise((resolve, reject) => {
        getEviPage(this.searchInfo, pagination).then(res => {
          this.rowData = res.data.content.map(item => {
            item._selected = false
            item._selectedKeys = []
            return item
          })
          this.$refs.aggrid.gridApi.setColumnDefs([{
            headerName: '单据号',
            field: 'sCode'
          },
          {
            headerName: '类型',
            field: 'sType',
            valueGetter: (params) => {
              const type = this.typeList.filter(item => item.sCodeValue === params.data.sType)
              return type.length ? type[0].sCodeName : params.data.sType
            }
          },
          {
            headerName: '元数据',
            field: 'sJsonData'
          },
          {
            headerName: '文件名称',
            field: 'sFileName'
          },
          {
            headerName: '证据编号',
            field: 'sCertId'
          },
          {
            headerName: '是否上传锐眼',
            field: 'sIsUpload',
            valueGetter: (params) => {
              const type = this.yesorno.filter(item => item.sCodeValue === params.data.sIsUpload)
              return type.length ? type[0].sCodeName : params.data.sIsUpload
            }
          },
          {
            headerName: '更新时间',
            field: 'sModifyTime',
            cellStyle: { textAlign: 'right' },
            minWidth: 150,
            valueFormatter(params) {
              return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sModifyTime)
            }
          },
          {
            headerName: '证据截图',
            field: 'sFileId',
            width: 100,
            cellRendererFramework: TableBtn
          }, {
            headerName: '证据证明',
            field: 'sCertFileId',
            width: 100,
            cellRendererFramework: TableBtn
          }])
          resolve(res.data)
        }).catch(() => {
          reject([])
        })
      })
    },
    handleFooterCount(rowData) {
      console.log(rowData)
    },
    cellValueChanged({ data }) {

    }
  }
}
</script>

<style scoped>

</style>
