<template>
  <div class="page-container">
    <p class="page-title">赊销明细查询</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form
        ref="searchForm"
        :form-items="formItems"
        @search="onSearch"
      />
      <div class="btn-group mt-10">
        <div class="text">
          赊销列表
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        table-selection="multiple"
        row-key="sId"
        :header-total="headerTotal"
        :footer-total="footerTotal"
        @selectedChange="handleFooterCount"
      />
    </div>
  </div>
</template>

<script>
import { SteelFormat } from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
import {
  creditDetailPage
} from '@/api/money/creditSales.js'
import steelTradeAggrid from '@/components/steelTradeAggrid'
export default {
  name: 'CreditDetailQuery',
  components: { steelTradeAggrid },
  data() {
    return {
      formItems: [
        {
          label: this.$t('grid.others.customer'),
          value: 'sCustomerId',
          type: 'cndInputDialogItem',
          dialogType: 'customer',
          defaultUrl: '/esc/customer/page',
          option: { valueKey: 'sPath' }
        },
        {
          label: this.$t('grid.title.company'),
          value: 'sCompanyId',
          type: 'cndInputDialog',
          dialogType: 'company',
          placeholder: this.$t('grid.others.pleaseSelectCompany')
        },
        {
          label: this.$t('grid.others.shippingOrderNumber'),
          value: 'sNoticeGoodsCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.title.salesContractNumber'),
          value: 'sSaleContractCode',
          type: 'elInput',
          placeholder: this.$t('grid.others.pleaseEnterTheSalesContractNumber')
        },
        {
          label: '预计收款日期',
          value: ['sExpectReceiptDateBegin', 'sExpectReceiptDateOver'],
          placeholder: [this.$t('grid.others.pleaseSelectTheDate'), this.$t('grid.others.pleaseSelectTheDate')],
          unlinkPanels: true,
          type: 'elDatePicker'
        },
        {
          label: this.$t('grid.others.department'),
          value: 'sDepartmentId',
          type: 'cndInputDialog',
          dialogType: 'depart',
          placeholder: this.$t('grid.others.pleaseSelectTheDepartment')
        },
        {
          label: this.$t('grid.title.accountingGroup'),
          value: 'sCheckGroupId',
          type: 'cndInputDialog',
          dialogType: 'cost',
          placeholder: this.$t('grid.others.pleaseSelectAccountingGroup')
        },
        {
          label: '经营单位',
          value: 'sManagementId',
          type: 'cndInputDialog',
          dialogType: 'escOrg',
          itemType: 'occultation'
        },
        {
          label: '未收金额',
          value: 'sUnReceiAmtCond',
          allHide: true,
          type: 'elSelect',
          default: '0',
          dict: [{
            sCodeValue: '2',
            sCodeName: '全部'
          }, {
            sCodeValue: '0',
            sCodeName: '大于0'
          }, {
            sCodeValue: '1',
            sCodeName: '等于0'
          }],
          itemType: 'occultation',
          placeholder: this.$t('grid.others.pleaseSelectStatus')
        }
      ],
      searchInfo: null,
      columnDefs: [
        {
          headerName: this.$t('grid.others.customer'),
          field: 'vCustomerName'
        },
        {
          headerName: '应收金额',
          field: 'sReceiAmount',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: '已收金额',
          field: 'sReceiedAmount',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: '未收金额',
          field: 'sUnReceiAmount',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: '预计收款日期',
          field: 'sExpectReceiptDate',
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD', params.data.sExpectReceiptDate)
          }
        },
        {
          headerName: '销售发货单号',
          field: 'sNoticeGoodsCode'
        },
        {
          headerName: this.$t('grid.title.salesContractNumber'),
          field: 'sSaleContractCode'
        },
        {
          headerName: this.$t('grid.others.itemNumberTag'),
          field: 'sProjectCode'
        },

        {
          headerName: this.$t('grid.title.company'),
          field: 'vCompanyName'
        },
        {
          headerName: this.$t('grid.others.department'),
          field: 'vDepartmentName'
        },
        {
          headerName: this.$t('grid.title.accountingGroup'),
          field: 'vCheckGroupName'
        },
        {
          headerName: this.$t('grid.title.personnel'),
          field: 'vStaffName'
        },
        {
          headerName: '经营单位',
          field: 'vManagementName'
        }
      ],
      rowData: [],
      headerTotal: null,
      footerTotal: null,
      delDisable: true
    }
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      if (!searchInfo.sUnReceiAmtCond) {
        searchInfo.sUnReceiAmtCond = '2'
      }
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    setTotal(vCount = 0, vReceiAmt = 0, vReceiedAmt = 0, vSumAmt = 0, flag) {
      this[flag] = [
        { count: vCount, key: 'count' },
        { title: '应收金额', count: SteelFormat.formatPrice(vReceiAmt), unit: this.$t('grid.others.yuan') },
        { title: '已收金额', count: SteelFormat.formatPrice(vReceiedAmt), unit: this.$t('grid.others.yuan') },
        { title: '未收金额', count: SteelFormat.formatPrice(vSumAmt), unit: this.$t('grid.others.yuan') }
      ]
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        creditDetailPage(
          this.searchInfo,
          pagination
        ).then(res => {
          this.rowData = res.data.pages.content.map(item => {
            item._selected = false
            return item
          })
          // const { vCount, vSumAmt } = res.data
          // this.setTotal(vCount, vSumAmt, 'headerTotal')
          resolve(res.data.pages)
        }).catch(() => {
          reject([])
        })
      })
    },
    handleFooterCount(list) {
      const details = list.filter(item => item._selected)
      this.delDisable = true
      if (details.length) {
        this.delDisable = !details.every(val => val.sSheetStatus === '10')
      }
      const vCount = details.length
      let vSumAmt = 0
      let vReceiAmt = 0
      let vReceiedAmt = 0
      details.forEach(el => {
        vSumAmt += (Number(el.sUnReceiAmount) || 0)
        vReceiAmt += (Number(el.sReceiAmount) || 0)
        vReceiedAmt += (Number(el.sReceiedAmount) || 0)
      })
      this.setTotal(vCount, vReceiAmt, vReceiedAmt, vSumAmt, 'footerTotal')
    }
  }
}
</script>

<style scoped>

</style>
