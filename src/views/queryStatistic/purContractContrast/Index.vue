<template>
  <div class="page-container">
    <p class="page-title">采购合同台账对比</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form ref="searchForm" :form-items="formItems" @search="onSearch" />
      <div class="btn-group mt-10" style="display: flex">
        <div class="text" style="flex:1">
          采购合同台账对比列表
        </div>
        <div>
          <export-btn
            class="ml-10"
            :file-name="'采购合同台账对比'"
            api-url="/esc/ledger/pur/contract/export"
            :post-params="searchInfo"
            :export-config="exportConfig"
            :get-grid-api="getGridApi"
          />
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :header-total="headerCount"
        :footer-total="footerCount"
        :auto-load-data="false"
        table-selection="multiple"
        @selectedChange="handleFooterCount"
        @cellValueChanged="cellValueChanged"
      />
    </div>
  </div>
</template>

<script>
import { Moment } from 'cnd-utils'
import moment from 'moment'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import exportBtn from '@/components/exportBtnV2'
import { SteelFormat } from 'cnd-horizon-utils'
import {
  getLedgerPurContractPage,
  getLedgerPurContractRemark
} from '@/api/queryStatistic/purchase'
export default {
  name: 'PurContractContrast',
  components: { steelTradeAggrid, exportBtn },
  data() {
    return {
      statusList: [
        { 'sCodeValue': '70',
          'sCodeName': '执行',
          'sSort': 70,
          'sFilter': null,
          'sIsEnabled': '1',
          'sLanguage': 'zh_CN'
        },
        {
          'sCodeValue': '75',
          'sCodeName': this.$t('grid.others.completion'),
          'sSort': 75,
          'sFilter': null,
          'sIsEnabled': '1',
          'sLanguage': 'zh_CN'
        },
        {
          'sCodeValue': '90',
          'sCodeName': this.$t('grid.others.invalidate'),
          'sSort': 90,
          'sFilter': null,
          'sIsEnabled': '1',
          'sLanguage': 'zh_CN'
        }
      ],
      searchInfo: null,
      formItems: [{
        label: this.$t('grid.others.contractNumber'),
        value: 'sPurContractCode',
        type: 'elInput'
      },
      // {
      //   label: this.$t('grid.others.customer'),
      //   value: 'sCustomerId',
      //   type: 'cndInputDialogItem',
      //   dialogType: 'customer',
      //   placeholder: this.$t('grid.others.pleaseSelectTheCustomer')
      // }, {
      //   label: this.$t('grid.others.itemNumberTag'),
      //   value: 'sProjectCode',
      //   type: 'elInput',
      //   placeholder: this.$t('grid.others.pleaseEnterTheItemNumberTag')
      // }, {
      //   label: this.$t('grid.title.company'),
      //   value: 'sCompanyId',
      //   type: 'cndInputDialog',
      //   dialogType: 'company',
      //   placeholder: this.$t('grid.others.pleaseSelectCompany')
      // }, {
      //   label: this.$t('grid.title.accountingGroup'),
      //   value: 'sCheckGroupId',
      //   type: 'cndInputDialog',
      //   dialogType: 'cost',
      //   placeholder: this.$t('grid.others.pleaseSelectAccountingGroup')
      // }, {
      //   label: this.$t('grid.others.department'),
      //   value: 'sDepartmentId',
      //   type: 'cndInputDialog',
      //   dialogType: 'depart',
      //   placeholder: this.$t('grid.others.pleaseSelectTheDepartment')
      // }, {
      //   label: this.$t('grid.title.personnel'),
      //   value: 'sStaffId',
      //   type: 'cndInputDialog',
      //   dialogType: 'staff',
      //   placeholder: this.$t('grid.others.pleaseSelectPersonnel')
      // }, {
      //   label: this.$t('grid.title.createdBy'),
      //   value: 'sCreator',
      //   type: 'cndInputDialog',
      //   dialogType: 'creater',
      //   placeholder: this.$t('grid.others.pleaseSelectTheCreator')
      // }, {
      //   label: this.$t('grid.title.status'),
      //   value: 'sSheetStatus',
      //   type: 'elSelect',
      //   itemType: 'occultation',
      //   default: '70',
      //   dict: [
      //     { 'sCodeValue': '70',
      //       'sCodeName': '执行',
      //       'sSort': 70,
      //       'sFilter': null,
      //       'sIsEnabled': '1',
      //       'sLanguage': 'zh_CN'
      //     },
      //     {
      //       'sCodeValue': '75',
      //       'sCodeName': this.$t('grid.others.completion'),
      //       'sSort': 75,
      //       'sFilter': null,
      //       'sIsEnabled': '1',
      //       'sLanguage': 'zh_CN'
      //     }
      //     // {
      //     //   'sCodeValue': '90',
      //     //   'sCodeName': this.$t('grid.others.invalidate'),
      //     //   'sSort': 90,
      //     //   'sFilter': null,
      //     //   'sIsEnabled': '1',
      //     //   'sLanguage': 'zh_CN'
      //     // }
      //   ] }, {
      //   label: this.$t('grid.title.createdAt'),
      //   value: ['sCreateTime', 'vCreateTimeTo'],
      //   default: ['', ''],
      //   itemType: 'occultation',
      //   unlinkPanels: true,
      //   type: 'elDatePicker'
      // },
      {
        label: '日期',
        value: ['startDateTime', 'endDateTime'],
        default: [
          moment().subtract(1, 'day').startOf('day').format('YYYY-MM-DDTHH:mm:ss'),
          moment().subtract(1, 'day').endOf('day').format('YYYY-MM-DDTHH:mm:ss')
        ],
        itemType: 'occultation',
        unlinkPanels: true,
        type: 'elDatePicker'
      },
      {
        label: '经营单位',
        value: 'sManagementId',
        type: 'cndInputDialog',
        dialogType: 'escOrg',
        itemType: 'occultation'
      },
      {
        label: '当日新增问题',
        value: 'sIsEmptyRemark',
        type: 'elCheckbox'
      }
      ],
      columnDefs: [
        {
          headerName: '合同号',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sPurContractCode',
              headerClass: 'c-header_child',
              pinned: 'left',
              cellStyle: {
                color: '#409EFF',
                textDecoration: 'underline',
                cursor: 'pointer'
              },
              onCellClicked: e => {
                this.$router.push({
                  path: '/purchaseQuerys',
                  query: {
                    sCode: e.value,
                    name: this.$t('grid.others.procurementContractProcess'),
                    activeId: localStorage.getItem('menuId')
                  }
                })
              }
            }
          ]
        },
        {
          headerName: '云钢付款金额',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sTaxAmt',
              headerClass: 'c-header_child',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.value)
              }
            }
          ]
        },
        {
          headerName: 'ERP付款金额',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sErpTaxAmt',
              headerClass: 'c-header_child',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.value)
              }
            }
          ]
        },
        {
          headerName: '付款金额差异',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sTaxAmtDiff',
              headerClass: 'c-header_child',
              cellStyle: { textAlign: 'right' },
              cellRenderer: params => {
                const text = SteelFormat.formatPrice(params.value)
                const sHtml = '<span  style=color:red>' + text + '</span>'
                return params.value !== 0 ? sHtml : text
              }
            }
          ]
        },
        {
          headerName: '云钢采购发票',
          children: [
            {
              field: 'sInvQtx',
              headerName: '数量',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatThousandthSign(params.value, 4)
              }
            },
            {
              field: 'sInvAmt',
              headerName: '金额',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.value)
              }
            }
          ]
        },
        {
          headerName: 'ERP采购发票',
          children: [
            {
              field: 'sErpInvQtx',
              headerName: '数量',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatThousandthSign(params.value, 4)
              }
            },
            {
              field: 'sErpInvAmt',
              headerName: '金额',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.value)
              }
            }
          ]
        },
        {
          headerName: '发票金额差异',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sInvDiff',
              headerClass: 'c-header_child',
              cellStyle: { textAlign: 'right' },
              cellRenderer: params => {
                const text = SteelFormat.formatPrice(params.value)
                const sHtml = '<span  style=color:red>' + text + '</span>'
                return params.value !== 0 ? sHtml : text
              }
            }
          ]
        },

        {
          headerName: '云钢库存',
          children: [
            {
              field: 'sPurQtx',
              headerName: '数量',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatThousandthSign(params.value, 4)
              }
            },
            {
              field: 'sPurQty',
              headerName: '件数',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatThousandthSign(params.value)
              }
            }
          ]
        },

        {
          headerName: 'ERP库存',
          children: [
            {
              field: 'sErpPurQtx',
              headerName: '数量',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatThousandthSign(params.value, 4)
              }
            },
            {
              field: 'sErpPurQty',
              headerName: '件数',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatThousandthSign(params.value)
              }
            }
          ]
        },

        {
          headerName: '库存数量差异',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sPurDiff',
              headerClass: 'c-header_child',
              cellStyle: { textAlign: 'right' },
              cellRenderer: params => {
                const text = SteelFormat.formatThousandthSign(params.value, 4)
                const sHtml = '<span  style=color:red>' + text + '</span>'
                return params.value !== 0 ? sHtml : text
              }
            }
          ]
        },

        {
          headerName: '备注',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sRemark',
              headerClass: 'c-header_child',
              width: 400,
              editable: true // 可编辑
            }
          ]
        },
        {
          headerName: '处理人',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sHandledBy',
              headerClass: 'c-header_child',
              width: 400,
              editable: true // 可编辑
            }
          ]
        },
        {
          headerName: '日期',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sRdate',
              headerClass: 'c-header_child',
              valueFormatter(params) {
                return Moment.time('YYYY-MM-DD', params.data.sRdate)
              }
            }
          ]
        },
        {
          headerName: '经营单位',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'vManagementName',
              headerClass: 'c-header_child'
            }
          ]
        }
      ],
      exportConfig: [
        { label: '合同号', value: 'sPurContractCode' },
        { label: '云钢库存数量', value: 'sPurQtx' },
        { label: '云钢库存件数', value: 'sPurQty' },
        { label: '云钢采购发票数量', value: 'sInvQtx' },
        { label: '云钢采购发票金额', value: 'sInvAmt' },
        { label: 'ERP库存数量', value: 'sErpPurQtx' },
        { label: 'ERP库存件数', value: 'sErpPurQty' },
        { label: 'ERP采购发票数量', value: 'sErpInvQtx' },
        { label: 'ERP采购发票金额', value: 'sErpInvAmt' },
        { label: '库存数量差异', value: 'sPurDiff' },
        { label: '发票金额差异', value: 'sInvDiff' },
        { label: '备注', value: 'sRemark' },
        { label: '处理人', value: 'sHandledBy' },
        { label: '日期', value: 'sRdate',
          setValue(value) {
            return Moment.time('YYYY-MM-DD', value)
          }
        },
        { label: '经营单位', value: 'vManagementName' }
      ],
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      headerCount: null,
      footerCount: null,
      rowData: []
    }
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        getLedgerPurContractPage(this.searchInfo, pagination).then(res => {
          this.rowData = res.data.content.map(item => {
            item._selected = false
            item._selectedKeys = []
            return item
          })
          resolve(res.data)
        }).catch(() => {
          reject([])
        })
      })
    },
    handleFooterCount(rowData) {
      console.log(rowData)
    },
    cellValueChanged({ data }) {
      getLedgerPurContractRemark(data).then(() => {
        this.$message.success(this.$t('grid.others.modifySuccessfully'))
        this.$refs.aggrid.reloadTableData()
      })
    }
  }
}
</script>

<style scoped>

</style>
