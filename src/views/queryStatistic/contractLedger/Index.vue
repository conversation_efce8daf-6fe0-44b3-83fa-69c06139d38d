<template>
  <div class="page-container">
    <p class="page-title">合同台账</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form ref="searchForm" :form-items="formItems" @search="onSearch" />
      <div class="btn-group mt-10" style="display: flex">
        <div class="text" style="flex:1">
          合同台账列表
        </div>
        <div>
          <export-btn
            class="ml-10"
            :file-name="'合同台账'"
            api-url="/esc/contract/sale/account/excel/export"
            :post-params="searchInfo"
            :export-config="exportConfig"
            :get-grid-api="getGridApi"
          />
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :header-total="headerCount"
        :footer-total="footerCount"
        :auto-load-data="false"
        table-selection="multiple"
        @selectedChange="handleFooterCount"
      />
    </div>
  </div>
</template>

<script>
import steelTradeAggrid from '@/components/steelTradeAggrid'
import exportBtn from '@/components/exportBtnV2'
import { SteelFormat } from 'cnd-horizon-utils'
import {
  getContractSaleAccountPage
} from '@/api/queryStatistic/purchase'
export default {
  name: 'ContractLedger',
  components: { steelTradeAggrid, exportBtn },
  data() {
    return {
      statusList: [
        { 'sCodeValue': '70',
          'sCodeName': '执行',
          'sSort': 70,
          'sFilter': null,
          'sIsEnabled': '1',
          'sLanguage': 'zh_CN'
        },
        {
          'sCodeValue': '75',
          'sCodeName': this.$t('grid.others.completion'),
          'sSort': 75,
          'sFilter': null,
          'sIsEnabled': '1',
          'sLanguage': 'zh_CN'
        },
        {
          'sCodeValue': '90',
          'sCodeName': this.$t('grid.others.invalidate'),
          'sSort': 90,
          'sFilter': null,
          'sIsEnabled': '1',
          'sLanguage': 'zh_CN'
        }
      ],
      searchInfo: null,
      formItems: [{
        label: this.$t('grid.others.contractNumber'),
        value: 'sCode',
        type: 'elInput',
        placeholder: this.$t('grid.others.pleaseEnterTheContractNumber')
      },
      {
        label: this.$t('grid.others.customer'),
        value: 'sCustomerId',
        type: 'cndInputDialogItem',
        dialogType: 'customer',
        defaultUrl: '/esc/customer/page',
        option: { valueKey: 'sPath' },
        placeholder: this.$t('grid.others.pleaseSelectTheCustomer')
      }, {
        label: this.$t('grid.others.itemNumberTag'),
        value: 'sProjectCode',
        type: 'elInput',
        placeholder: this.$t('grid.others.pleaseEnterTheItemNumberTag')
      }, {
        label: this.$t('grid.title.company'),
        value: 'sCompanyId',
        type: 'cndInputDialog',
        dialogType: 'company',
        placeholder: this.$t('grid.others.pleaseSelectCompany')
      }, {
        label: this.$t('grid.title.accountingGroup'),
        value: 'sCheckGroupId',
        type: 'cndInputDialog',
        dialogType: 'cost',
        placeholder: this.$t('grid.others.pleaseSelectAccountingGroup')
      }, {
        label: this.$t('grid.others.department'),
        value: 'sDepartmentId',
        type: 'cndInputDialog',
        dialogType: 'depart',
        placeholder: this.$t('grid.others.pleaseSelectTheDepartment')
      }, {
        label: this.$t('grid.title.personnel'),
        value: 'sStaffId',
        type: 'cndInputDialogItem',
        option: { valueKey: 'sPath' },
        dialogType: 'staff',
        placeholder: this.$t('grid.others.pleaseSelectPersonnel')
      }, {
        label: this.$t('grid.title.createdBy'),
        value: 'sCreator',
        type: 'cndInputDialog',
        dialogType: 'creater',
        placeholder: this.$t('grid.others.pleaseSelectTheCreator')
      }, {
        label: this.$t('grid.title.status'),
        value: 'sSheetStatus',
        type: 'elSelect',
        itemType: 'occultation',
        default: '70',
        dict: [
          { 'sCodeValue': '70',
            'sCodeName': '执行',
            'sSort': 70,
            'sFilter': null,
            'sIsEnabled': '1',
            'sLanguage': 'zh_CN'
          },
          {
            'sCodeValue': '75',
            'sCodeName': this.$t('grid.others.completion'),
            'sSort': 75,
            'sFilter': null,
            'sIsEnabled': '1',
            'sLanguage': 'zh_CN'
          }
          // {
          //   'sCodeValue': '90',
          //   'sCodeName': this.$t('grid.others.invalidate'),
          //   'sSort': 90,
          //   'sFilter': null,
          //   'sIsEnabled': '1',
          //   'sLanguage': 'zh_CN'
          // }
        ] }, {
        label: this.$t('grid.title.createdAt'),
        value: ['sCreateTime', 'vCreateTimeTo'],
        default: ['', ''],
        itemType: 'occultation',
        unlinkPanels: true,
        type: 'elDatePicker'
      },
      {
        label: this.$t('grid.others.executionTime'),
        value: ['sRatifyDate', 'vRatifyDateTo'],
        itemType: 'occultation',
        unlinkPanels: true,
        type: 'elDatePicker'
      }],
      columnDefs: [
        {
          headerName: '合同号',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sCode',
              headerClass: 'c-header_child'
              // cellStyle: {
              //   color: '#409EFF',
              //   textDecoration: 'underline',
              //   cursor: 'pointer'
              // },
              // onCellClicked: e => {
              //   this.$router.push({
              //     path: '/SaleQuery',
              //     query: {
              //       sCode: e.value,
              //       name: this.$t('grid.others.salesContractProcess'),
              //       activeId: localStorage.getItem('menuId')
              //     }
              //   })
              // }
            }
          ]
        },
        {
          headerName: '物流',
          children: [
            {
              field: 'vLeftContractQty',
              headerName: '库存数量',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatThousandthSign(params.value, 4)
              }
            },
            {
              field: 'vLeftQty',
              headerName: '库存件数',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatThousandthSign(params.value)
              }
            },
            {
              field: 'vStockQtx',
              headerName: '出仓数量',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatThousandthSign(params.value, 4)
              }
            },
            {
              field: 'vStockQty',
              headerName: '出仓件数',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatThousandthSign(params.value)
              }
            }
          ]
        },
        {
          headerName: '收款',
          children: [
            {
              field: 'vPaymentAmt',
              headerName: '金额',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.value)
              }
            }
          ]
        },
        {
          headerName: '销售发票',
          children: [
            {
              field: 'sInvNum',
              headerName: '张数',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatThousandthSign(params.value)
              }
            },
            {
              field: 'vInvoiceQty',
              headerName: '数量',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatThousandthSign(params.value, 4)
              }
            },
            {
              field: 'vInvoiceAmt',
              headerName: '发票金额',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.value)
              }
            }
          ]
        }
      ],
      exportConfig: [
        { label: '合同号', value: 'sCode' },
        { label: '物流库存数量', value: 'vLeftContractQty' },
        { label: '物流库存件数', value: 'vLeftQty' },
        { label: '物流出仓数量', value: 'vStockQtx' },
        { label: '物流出仓件数', value: 'vStockQty' },
        { label: '收款金额', value: 'vPaymentAmt' },
        { label: '销售发票张数', value: 'sInvNum' },
        { label: '销售发票数量', value: 'vInvoiceQty' },
        { label: '销售发票发票金额', value: 'vInvoiceAmt' }
      ],
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      headerCount: null,
      footerCount: null,
      rowData: []
    }
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        getContractSaleAccountPage(this.searchInfo, pagination).then(res => {
          this.rowData = res.data.content.map(item => {
            item._selected = false
            item._selectedKeys = []
            return item
          })
          resolve(res.data)
        }).catch(() => {
          reject([])
        })
      })
    },
    handleFooterCount(rowData) {
      console.log(rowData)
    }
  }
}
</script>

<style scoped>

</style>
