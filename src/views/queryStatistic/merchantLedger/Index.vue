<template>
  <div class="page-container">
    <p class="page-title">客商余额台账</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form ref="searchForm" :form-items="formItems" @search="onSearch" />
      <div class="btn-group mt-10" style="display: flex">
        <div class="text" style="flex:1">
          客商余额台账列表
        </div>
        <div>
          <export-btn
            class="ml-10"
            :file-name="'客商余额台账'"
            api-url="/esc/ledger/customer/export"
            :post-params="searchInfo"
            :export-config="exportConfig"
            :get-grid-api="getGridApi"
          />
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :header-total="headerCount"
        :footer-total="footerCount"
        :auto-load-data="false"
        table-selection="multiple"
        @selectedChange="handleFooterCount"
        @cellValueChanged="cellValueChanged"
      />
    </div>
  </div>
</template>

<script>
import { Moment } from 'cnd-utils'
import moment from 'moment'

import steelTradeAggrid from '@/components/steelTradeAggrid'
import exportBtn from '@/components/exportBtnV2'
import { SteelFormat } from 'cnd-horizon-utils'
import {
  getLedgerCustomerPage,
  updateLedgerCustomerRemark
} from '@/api/queryStatistic/purchase'
export default {
  name: 'MerchantLedger',
  components: { steelTradeAggrid, exportBtn },
  data() {
    return {
      searchInfo: null,
      formItems: [
        {
          label: this.$t('grid.others.customer'),
          value: 'sCustomerIds',
          type: 'cndInputDialogItem',
          dialogType: 'customer',
          defaultUrl: '/esc/customer/page',
          option: { valueKey: 'sPath' },
          multiple: true,
          reserveKeyword: false,
          placeholder: this.$t('grid.others.pleaseSelectTheCustomer')
        },
        {
          label: this.$t('grid.title.company'),
          value: 'sCompanyIds',
          type: 'cndInputDialogItem',
          dialogType: 'company',
          multiple: true,
          reserveKeyword: false,
          placeholder: this.$t('grid.others.pleaseSelectCompany')
        },
        {
          label: '处理人',
          value: 'sHandleBy',
          type: 'elInput'
        },
        {
          label: '备注',
          value: 'sRemark',
          type: 'elInput'
        },
        {
          label: '当日新增问题',
          value: 'sIsEmptyRemark',
          type: 'elCheckbox'
        },
        {
          label: '日期',
          value: ['startDateTime', 'endDateTime'],
          default: [
            moment().subtract(1, 'day').startOf('day').format('YYYY-MM-DDTHH:mm:ss'),
            moment().subtract(1, 'day').endOf('day').format('YYYY-MM-DDTHH:mm:ss')
          ],
          unlinkPanels: true,
          type: 'elDatePicker'
        }
      ],
      columnDefs: [
        {
          headerName: '客户',
          field: 'sCustomerName',
          pinned: 'left'
        },
        {
          headerName: '公司',
          field: 'sCompanyName'
        },
        {
          headerName: '现款+银承',
          field: 'sCashAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: 'ERP暂收款（云钢）',
          field: 'sErpCashAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: '现款银承差异',
          field: 'sCashDiff',
          cellStyle: { textAlign: 'right' },
          cellRenderer: params => {
            const text = SteelFormat.formatPrice(params.value)
            const sHtml = '<span  style=color:red>' + text + '</span>'
            return params.value !== null && params.value !== 0 ? sHtml : text
          }
        },

        {
          headerName: '赊销金额',
          field: 'sCreditAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: 'ERP赊销金额',
          field: 'sErpCreditAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: '赊销金额差异',
          field: 'sCreditDiff',
          cellStyle: { textAlign: 'right' },
          cellRenderer: params => {
            const text = SteelFormat.formatPrice(params.value)
            const sHtml = '<span  style=color:red>' + text + '</span>'
            return params.value !== null && params.value !== 0 ? sHtml : text
          }
        },

        {
          headerName: '赊销还款金额',
          field: 'sRefundAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: 'ERP赊销还款金额',
          field: 'sErpRefundAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: '赊销还款金额差异',
          field: 'sRefundDiff',
          cellStyle: { textAlign: 'right' },
          cellRenderer: params => {
            const text = SteelFormat.formatPrice(params.value)
            const sHtml = '<span  style=color:red>' + text + '</span>'
            return params.value !== null && params.value !== 0 ? sHtml : text
          }
        },

        {
          headerName: '返利',
          field: 'sRebateAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: 'ERP返利',
          field: 'sErpRebateAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: '返利差异',
          field: 'sRebateDiff',
          cellStyle: { textAlign: 'right' },
          cellRenderer: params => {
            const text = SteelFormat.formatPrice(params.value)
            const sHtml = '<span  style=color:red>' + text + '</span>'
            return params.value !== 0 ? sHtml : text
          }
        },
        {
          headerName: '余额合计',
          field: 'sAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: 'ERP余额合计',
          field: 'sErpAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        }, {
          headerName: '余额差异',
          field: 'sAmtDiff',
          cellStyle: { textAlign: 'right' },
          cellRenderer: params => {
            const text = SteelFormat.formatPrice(params.value)
            const sHtml = '<span  style=color:red>' + text + '</span>'
            return params.value !== 0 ? sHtml : text
          }
        }, {
          headerName: '备注',
          field: 'sRemark',
          width: 300,
          editable: true // 可编辑
        }, {
          headerName: '处理人',
          field: 'sHandledBy',
          width: 300,
          editable: true
        }, {
          headerName: '日期',
          field: 'sRdate',
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD', params.data.sRdate)
          }
        }
      ],
      exportConfig: [
        { label: '客户', value: 'sCustomerName' },
        { label: '公司', value: 'sCompanyName' },
        { label: '现款+银承', value: 'sCashAmt' },
        { label: 'ERP暂收款（云钢）', value: 'sErpCashAmt' },
        { label: '现款银承差异', value: 'sCashDiff' },
        { label: '赊销金额', value: 'sCreditAmt' },
        { label: 'ERP赊销金额', value: 'sErpCreditAmt' },
        { label: '赊销金额差异', value: 'sCreditDiff' },
        { label: '赊销还款金额', value: 'sRefundAmt' },
        { label: 'ERP赊销还款金额', value: 'sErpRefundAmt' },
        { label: '赊销还款金额差异', value: 'sRefundDiff' },
        { label: '返利', value: 'sRebateAmt' },
        { label: 'ERP返利', value: 'sErpRebateAmt' },
        { label: '返利差异', value: 'sRebateDiff' },
        { label: '余额合计', value: 'sAmt' },
        { label: 'ERP余额合计', value: 'sErpAmt' },
        { label: '余额差异', value: 'sAmtDiff' },
        { label: '备注', value: 'sRemark' },
        { label: '处理人', value: 'sHandledBy' },
        { label: '日期', value: 'sRdate',
          setValue(value) {
            return Moment.time('YYYY-MM-DD', value)
          }
        }
      ],
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      headerCount: null,
      footerCount: null,
      rowData: []
    }
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      searchInfo.sCustomerIds = searchInfo.sCustomerIds?.toString()
      searchInfo.sCompanyIds = searchInfo.sCompanyIds?.toString()
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        getLedgerCustomerPage(this.searchInfo, pagination).then(res => {
          this.rowData = res.data.content.map(item => {
            item._selected = false
            item._selectedKeys = []
            return item
          })
          resolve(res.data)
        }).catch(() => {
          reject([])
        })
      })
    },
    handleFooterCount(rowData) {
      console.log(rowData)
    },
    cellValueChanged({ data }) {
      updateLedgerCustomerRemark(data).then(() => {
        this.$message.success(this.$t('grid.others.modifySuccessfully'))
        this.$refs.aggrid.reloadTableData()
      })
    }
  }
}
</script>

<style scoped>

</style>
