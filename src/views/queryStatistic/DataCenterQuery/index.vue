<template>
  <div class="page-container">
    <p class="page-title">数据中台查询</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form
        ref="searchForm"
        :form-items="formItems"
        @search="onSearch"
      />
      <div class="btn-group mt-10">
        <div class="text">
          数据中台查询
        </div>
        <div>
          <el-button v-has:esc_ext_balance_pending_entry_export_template type="primary" size="mini" @click="downloadTemplate">导入模板下载</el-button>
          <importBtn
            v-has:esc_ext_balance_pending_entry_import
            class="mr-10 ml-10"
            btn-text="导入数据"
            :action="`/api/esc/ext/balance/pending/entry/import`"
            :action-success-url="`/esc/ext/balance/pending/entry/importSuccessData`"
            success-mark="escExtbalancePendingEntryIds"
            @success="successImport"
          />
          <el-button
            v-has:esc_ext_balance_pending_entry_removes
            size="mini"
            type="danger"
            :disabled="!showDelete"
            @click="deleteCenterData"
          >
            删除
          </el-button>
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        table-selection="multiple"
        row-key="sId"
        :footer-total="footerTotal"
        @selectedChange="handleFooterCount"
      />
    </div>
  </div>
</template>
<script>
import { SteelFormat } from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import { getDictList, getCnDitc } from '@/utils/dict'
import importBtn from '@/components/importBtn'
import {
  extBalanceEntry,
  extBalanceRemoves,
  extBalanceTemplate
} from '@/api/queryStatistic/common'
import { convertRes2Blob } from '@/utils/common'
export default {
  name: 'DataCenterQuery',
  components: { steelTradeAggrid, importBtn },
  data() {
    return {
      formItems: [
        {
          label: this.$t('grid.title.purchaseContractNumber'),
          value: 'sPurContractCode',
          type: 'elInput',
          placeholder: this.$t('grid.others.enterPurchaseContractNumber')
        },
        {
          label: this.$t('grid.others.steelCoilNumber'),
          value: 'sSteelNo',
          type: 'elInput',
          placeholder: '请输入钢卷号'
        },
        {
          label: this.$t('grid.others.warehouseReturnNumberTag'),
          value: 'sExteEntryOrderNo',
          type: 'elInput',
          placeholder: '请输入仓库回单号'
        },
        {
          label: '货主',
          value: 'sCustomerId',
          type: 'cndInputDialogItem',
          defaultUrl: '/esc/customer/page',
          option: { valueKey: 'sPath' },
          placeholder: '请选择货主'
        },
        {
          label: this.$t('grid.others.warehouse'),
          value: 'sWarehouseCode',
          type: 'cndInputDialogItem',
          defaultUrl: '/esc/warehouse/info/dialog/v2/page-dialog',
          option: { valueKey: 'sPath', value: 'sCode' },
          placeholder: this.$t('grid.others.pleaseSelectAWarehouse')
        },
        {
          label: this.$t('grid.others.accountingFilter'),
          value: 'vIsCor',
          type: 'elSelect',
          dict: 'dev.common.verify.finish.type',
          placeholder: this.$t('grid.others.pleaseSelectTheUnderwritingFilter')
        },
        {
          label: '入库时间',
          value: ['sShipTime', 'vShipTimeTo'],
          placeholder: [this.$t('grid.others.pleaseSelectTheDate'), this.$t('grid.others.pleaseSelectTheDate')],
          unlinkPanels: true,
          type: 'elDatePicker'
        },
        {
          label: this.$t('grid.others.creationDate'),
          value: ['sCreateTime', 'vCreateTimeTo'],
          placeholder: [this.$t('grid.others.pleaseSelectTheDate'), this.$t('grid.others.pleaseSelectTheDate')],
          unlinkPanels: true,
          type: 'elDatePicker'
        }
      ],
      searchInfo: null,
      columnDefs: [
        {
          headerName: '仓库回单号',
          field: 'sExteEntryOrderNo'
        },
        {
          headerName: '采购合同号',
          field: 'sPurContractCode'
        },
        {
          headerName: '项目号',
          field: 'sProjectCode',
          width: 150
        },
        {
          headerName: '钢卷号',
          field: 'sSteelNo',
          width: 150
        },
        {
          headerName: '原始数量',
          field: 'sSrcQtx',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: '原始件数',
          field: 'sSrcQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value, 0)
          }
        },
        {
          headerName: '剩余数量',
          field: 'sLeftQtx',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: '剩余件数',
          field: 'sLeftQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value, 0)
          }
        },
        {
          headerName: '品名',
          field: 'sItemName'
        },
        {
          headerName: '材质',
          field: 'sMtrl'
        },
        {
          headerName: '规格',
          field: 'sSpec'
        },
        {
          headerName: '仓库名称',
          field: 'sWarehouseName',
          width: 150
        },
        {
          headerName: '货主',
          field: 'vCustomerName',
          width: 150
        },
        {
          headerName: '入库时间',
          field: 'sShipTime',
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD', params.data.sDeliveryNotifyDate)
          }
        },
        {
          headerName: '明细备注',
          field: 'sDetailRemark'
        },
        {
          headerName: '拣配类型',
          field: 'sPickType',
          valueGetter: (params) => {
            return getCnDitc(params, this.options['extbalance.arrival.type'], 'sPickType')
          }
        },
        {
          headerName: '数据类型',
          field: 'sEntryType',
          valueGetter: (params) => {
            return getCnDitc(params, this.options['pending.entry.type'], 'sEntryType')
          }
        },
        {
          headerName: '到货单号',
          field: 'sStockReceiptCode'
        },
        {
          headerName: '创建时间',
          field: 'sCreateTime',
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD', params.data.sDeliveryNotifyDate)
          }
        },
        {
          headerName: '创建人',
          field: 'vCreatorName'
        },
        {
          headerName: '修改时间',
          field: 'sShipTime',
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD', params.data.sDeliveryNotifyDate)
          }
        },
        {
          headerName: '修改人',
          field: 'vModifierName'
        }
      ],
      rowData: [],
      options: {
        'extbalance.arrival.type': [],
        'pending.entry.type': []
      },
      showDelete: false
    }
  },
  mounted() {
    getDictList(this.options)
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        extBalanceEntry(
          this.searchInfo,
          pagination
        ).then(res => {
          this.rowData = res.data.content.map(item => {
            item._selected = false
            return item
          })
          resolve(res.data)
        }).catch(() => {
          reject([])
        })
      })
    },
    handleFooterCount() {
      this.$refs.aggrid.getSelectedData(res => {
        this.showDelete = res.every(item => item.sEntryType === 'IMPORT')
      })
    },
    deleteCenterData() {
      this.$refs.aggrid.getSelectedData(res => {
        const list = res.map(item => item.sId)
        if (!list.length) {
          return this.$message.warning(this.$t('grid.others.pleaseSelectAtLeastOneData'))
        }
        this.$confirm(this.$t('tips.isItOkToDelete'), this.$t('grid.others.prompt'), {
          confirmButtonText: this.$t('btns.confirm'),
          cancelButtonText: this.$t('btns.cancel'),
          type: 'info'
        })
          .then(() => {
            extBalanceRemoves(list).then(res => {
              this.$message({
                message: this.$t('tips.deletedSuccessfully'),
                type: 'success'
              })
              this.$refs.aggrid.reloadTableData()
            })
          })
      })
    },
    downloadTemplate() {
      extBalanceTemplate(this.invoiceId).then(v => {
        if (v.type === 'application/json') {
          const fileReader = new FileReader()
          fileReader.readAsText(v, 'utf-8')
          fileReader.onload = () => {
            const result = JSON.parse(fileReader.result)
            if (!result.data && result.message === 'ok') {
              this.$message.error('暂无模板')
            } else {
              this.$message.error(result.message)
            }
          }
        } else {
          convertRes2Blob(v, '导入模板', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
          this.$nextTick(() => {
            this.$message.success(this.$t('tips.downloadSuccessful'))
          })
        }
      })
    },
    // 导入
    successImport() {
      this.$message({
        message: this.$t('tips.addSuccess'),
        type: 'success'
      })
      this.$emit('close')
      this.$refs.aggrid.reloadTableData()
    }
  }
}
</script>

<style scoped>

</style>
