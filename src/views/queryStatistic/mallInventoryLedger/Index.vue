<template>
  <div class="page-container">
    <p class="page-title">商城库存台账对比</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form ref="searchForm" :form-items="formItems" @search="onSearch" />
      <div class="btn-group mt-10" style="display: flex">
        <div class="text" style="flex:1">
          商城库存台账对比列表
        </div>
        <div>
          <export-btn
            class="ml-10"
            :file-name="'商城库存台账对比'"
            api-url="/esc/stock/ledger/compare/export"
            :post-params="searchInfo"
            :export-config="exportConfig"
            :get-grid-api="getGridApi"
          />
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :header-total="headerCount"
        :footer-total="footerCount"
        :auto-load-data="false"
        table-selection="multiple"
        @selectedChange="handleFooterCount"
        @cellValueChanged="cellValueChanged"
      />
    </div>
  </div>
</template>

<script>
import { Moment } from 'cnd-utils'
import moment from 'moment'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import exportBtn from '@/components/exportBtnV2'
import { SteelFormat } from 'cnd-horizon-utils'
import {
  mallLedgerPage,
  mallLedgerPageUpdate
} from '@/api/logistics/saleDelivery/retailMall.js'
export default {
  name: 'MallInventoryLedger',
  components: { steelTradeAggrid, exportBtn },
  data() {
    return {
      searchInfo: null,
      formItems: [
        {
          label: '上架单号',
          value: 'sInventoryDetailCode',
          type: 'elInput'
        },
        {
          label: '采购合同号',
          value: 'sPurContractCode',
          type: 'elInput'
        },
        {
          label: '原始单据号',
          value: 'sStockReceiptCode',
          type: 'elInput'
        },
        {
          label: '经营单位',
          value: 'sManagementId',
          type: 'cndInputDialog',
          dialogType: 'escOrg',
          itemType: 'occultation'
        },
        {
          label: '项目号',
          value: 'sProjectCode',
          type: 'elInput'
        },
        {
          label: '日期',
          value: ['sCreateTime', 'sCreateTimeTo'],
          default: [
            moment().subtract(1, 'day').startOf('day').format('YYYY-MM-DDTHH:mm:ss'),
            moment().subtract(1, 'day').endOf('day').format('YYYY-MM-DDTHH:mm:ss')
          ],
          unlinkPanels: true,
          type: 'elDatePicker'
        },
        {
          label: '处理人',
          value: 'sHandledBy',
          type: 'elInput'
        },
        {
          label: '当日新增问题',
          value: 'sIsCurrentDayProblem',
          type: 'elCheckbox'
        }
      ],
      columnDefs: [
        {
          headerName: '上架单号',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sInventoryDetailCode',
              headerClass: 'c-header_child',
              pinned: 'left'
              // cellStyle: {
              //   color: '#409EFF',
              //   textDecoration: 'underline',
              //   cursor: 'pointer'
              // },
              // onCellClicked: e => {
              //   this.$router.push({
              //     path: '/purchaseQuerys',
              //     query: {
              //       sCode: e.value,
              //       name: this.$t('grid.others.procurementContractProcess'),
              //       activeId: localStorage.getItem('menuId')
              //     }
              //   })
              // }
            }
          ]
        },
        {
          headerName: '采购合同号',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sPurContractCode',
              headerClass: 'c-header_child'
            }
          ]
        },
        {
          headerName: '原始单据号',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sStockReceiptCode',
              headerClass: 'c-header_child'
            }
          ]
        },
        {
          headerName: '钢贸剩余库存',
          children: [
            {
              field: 'sEscStockLeftQtx',
              headerName: '数量',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatThousandthSign(params.value, 4)
              }
            },
            {
              field: 'sEscStockLeftQty',
              headerName: '件数',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatThousandthSign(params.value)
              }
            }
          ]
        },
        {
          headerName: '商城剩余库存',
          children: [
            {
              field: 'sMallStockLeftQtx',
              headerName: '数量',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatThousandthSign(params.value, 4)
              }
            },
            {
              field: 'sMallStockLeftQty',
              headerName: '件数',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatThousandthSign(params.value)
              }
            }
          ]
        },
        {
          headerName: '剩余库存差异',
          children: [
            {
              field: 'sStockLeftQtxVariance',
              headerName: '数量',
              cellStyle: { textAlign: 'right' },
              cellRenderer: params => {
                const text = SteelFormat.formatThousandthSign(params.value, 4)
                const sHtml = '<span style=color:red>' + text + '</span>'
                return params.value !== 0 ? sHtml : text
              }
            },
            {
              field: 'sStockLeftQtyVariance',
              headerName: '件数',
              cellStyle: { textAlign: 'right' },
              cellRenderer: params => {
                const text = SteelFormat.formatThousandthSign(params.value)
                const sHtml = '<span style=color:red>' + text + '</span>'
                return params.value !== 0 ? sHtml : text
              }
            }
          ]
        },
        {
          headerName: '钢贸单价',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sEscUnitPrice',
              headerClass: 'c-header_child',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.value)
              }
            }
          ]
        },
        {
          headerName: '商城单价',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sMallUnitPrice',
              headerClass: 'c-header_child',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.value)
              }
            }
          ]
        },
        {
          headerName: '单价差异',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sUnitPriceVariance',
              headerClass: 'c-header_child',
              cellStyle: { textAlign: 'right' },
              cellRenderer: params => {
                const text = SteelFormat.formatPrice(params.value)
                const sHtml = '<span style=color:red>' + text + '</span>'
                return params.value !== 0 ? sHtml : text
              }
            }
          ]
        },
        {
          headerName: '钢贸冻结',
          children: [
            {
              field: 'sEscFrozenQtx',
              headerName: '数量',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatThousandthSign(params.value, 4)
              }
            },
            {
              field: 'sEscFrozenQty',
              headerName: '件数',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatThousandthSign(params.value)
              }
            }
          ]
        },

        {
          headerName: '商城冻结',
          children: [
            {
              field: 'sMallFrozenQtx',
              headerName: '数量',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatThousandthSign(params.value, 4)
              }
            },
            {
              field: 'sMallFrozenQty',
              headerName: '件数',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatThousandthSign(params.value)
              }
            }
          ]
        },
        {
          headerName: '冻结差异',
          children: [
            {
              field: 'sFrozenQtxVariance',
              headerName: '数量',
              cellStyle: { textAlign: 'right' },
              cellRenderer: params => {
                const text = SteelFormat.formatThousandthSign(params.value, 4)
                const sHtml = '<span style=color:red>' + text + '</span>'
                return params.value !== 0 ? sHtml : text
              }
            },
            {
              field: 'sFrozenQtyVariance',
              headerName: '件数',
              cellStyle: { textAlign: 'right' },
              cellRenderer: params => {
                const text = SteelFormat.formatThousandthSign(params.value)
                const sHtml = '<span style=color:red>' + text + '</span>'
                return params.value !== 0 ? sHtml : text
              }
            }
          ]
        },

        {
          headerName: '备注',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sRemark',
              headerClass: 'c-header_child',
              width: 400,
              editable: true
            }
          ]
        },
        {
          headerName: '处理人',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sHandledBy',
              headerClass: 'c-header_child',
              width: 400,
              editable: true
            }
          ]
        },
        {
          headerName: '日期',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sCreateTime',
              headerClass: 'c-header_child',
              valueFormatter(params) {
                return Moment.time('YYYY-MM-DD', params.data.sCreateTime)
              }
            }
          ]
        },
        {
          headerName: '经营单位',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'vManagementName',
              headerClass: 'c-header_child'
            }
          ]
        }
      ],
      exportConfig: [
        { label: '上架单号', value: 'sInventoryDetailCode' },
        { label: '采购合同号', value: 'sPurContractCode' },
        { label: '原始单据号', value: 'sStockReceiptCode' },
        { label: '钢贸剩余库存数量', value: 'sEscStockLeftQtx' },
        { label: '钢贸剩余库存件数', value: 'sEscStockLeftQty' },
        { label: '商城剩余库存数量', value: 'sMallStockLeftQtx' },
        { label: '商城剩余库存件数', value: 'sMallStockLeftQty' },
        { label: '剩余库存差异数量', value: 'sStockLeftQtxVariance' },
        { label: '剩余库存差异件数', value: 'sStockLeftQtyVariance' },
        { label: '钢贸单价', value: 'sEscUnitPrice' },
        { label: '商城单价', value: 'sMallUnitPrice' },
        { label: '单价差异', value: 'sUnitPriceVariance' },
        { label: '钢贸冻结数量', value: 'sEscFrozenQtx' },
        { label: '钢贸冻结件数', value: 'sEscFrozenQty' },
        { label: '商城冻结数量', value: 'sMallFrozenQtx' },
        { label: '商城冻结件数', value: 'sMallFrozenQty' },
        { label: '冻结差异数量', value: 'sFrozenQtxVariance' },
        { label: '冻结差异件数', value: 'sFrozenQtyVariance' },
        { label: '备注', value: 'sRemark' },
        { label: '处理人', value: 'sHandledBy' },
        { label: '日期', value: 'sCreateTime',
          setValue(value) {
            return Moment.time('YYYY-MM-DD', value)
          }
        },
        { label: '经营单位', value: 'vManagementName' }
      ],
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      headerCount: null,
      footerCount: null,
      rowData: []
    }
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        mallLedgerPage(this.searchInfo, pagination).then(res => {
          this.rowData = res.data.content.map(item => {
            item._selected = false
            item._selectedKeys = []
            return item
          })
          resolve(res.data)
        }).catch(() => {
          reject([])
        })
      })
    },
    handleFooterCount(rowData) {
      console.log(rowData)
    },
    cellValueChanged({ data }) {
      mallLedgerPageUpdate(data).then(() => {
        this.$message.success(this.$t('grid.others.modifySuccessfully'))
        this.$refs.aggrid.reloadTableData()
      })
    }
  }
}
</script>

<style scoped>

</style>
