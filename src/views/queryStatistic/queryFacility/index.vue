<template>
  <div class="page-container">
    <p class="page-title">查询工具</p>
    <div class="layout-content auto-page-title flexV">
      <el-form ref="form" class="el-form-w100" label-width="110px" :inline="true" :model="form" size="small">
        <cnd-form-card-list
          ref="panelSerch"
          :active-panel="activeCollapseName"
          class-name="filter-collapse-container"
        >
          <!-- 钢卷号占用查询 -->
          <cnd-form-card title="钢卷号占用查询" name="1">
            <el-row>
              <cnd-form-item label="钢卷号">
                <el-input v-model="form.steelNo" placeholder="请输入钢卷号" />
              </cnd-form-item>
              <cnd-form-item>
                <el-button
                  style="margin-left: -80px;"
                  :loading="loadings.steelNo"
                  type="primary"
                  size="mini"
                  @click="handleSearch('steelNo')"
                >查询</el-button>
              </cnd-form-item>
            </el-row>
            <el-row style="margin-top:5px">
              <steelTradeAggrid
                ref="aggridSteelNo"
                :column-defs="columnDefs.steelNo"
                :row-data="tableData.steelNo"
                :load-data="loadSteelNoData"
                :auto-load-data="false"
                row-key="sCode"
                :heightinif="250"
                :paginationinif="false"
              />
            </el-row>
          </cnd-form-card>

          <!-- 款项占用查询 -->
          <cnd-form-card title="款项占用查询" name="2">
            <el-row>
              <cnd-form-item label="单据号">
                <el-input v-model="form.documentNo" placeholder="请输入单据号" />
              </cnd-form-item>
              <cnd-form-item>
                <el-button
                  style="margin-left: -80px;"
                  :loading="loadings.document"
                  type="primary"
                  size="mini"
                  @click="handleSearch('document')"
                >查询</el-button>
              </cnd-form-item>
            </el-row>
            <el-row>
              <steelTradeAggrid
                ref="aggridDocument"
                :column-defs="columnDefs.document"
                :row-data="tableData.document"
                :load-data="loadDocumentData"
                :auto-load-data="false"
                row-key="sCode"
                :heightinif="250"
                :paginationinif="false"
              />
            </el-row>
          </cnd-form-card>

          <!-- E建签查询 -->
          <cnd-form-card title="发送E建签查询" name="3">
            <el-row>
              <cnd-form-item label="单据号">
                <el-input v-model="form.documentNoV3" placeholder="请输入单据号" />
              </cnd-form-item>
              <cnd-form-item>
                <el-button
                  style="margin-left: -80px;"
                  :loading="loadings.ejq"
                  type="primary"
                  size="mini"
                  @click="handleSearch('ejq')"
                >查询</el-button>
              </cnd-form-item>
            </el-row>
            <el-row>
              <steelTradeAggrid
                ref="aggridEjq"
                :column-defs="columnDefs.ejq"
                :row-data="tableData.ejq"
                :load-data="loadEjqData"
                :auto-load-data="false"
                row-key="sCode"
                :heightinif="250"
                :paginationinif="false"
              />
            </el-row>
          </cnd-form-card>

          <!-- 客户注册查询 -->
          <cnd-form-card title="客户注册查询" name="4">
            <el-row>
              <cnd-form-item :label="$t('grid.others.customer')">
                <horizon-search-select-item
                  v-model="form.customerName"
                  type="customer"
                  multiple
                  reserve-keyword
                  :default-url="`/esc/customer/page`"
                  :placeholder="$t('components.pleaseSelect')"
                  @change="handleChangeSelect"
                />
              </cnd-form-item>
              <cnd-form-item>
                <el-button
                  style="margin-left: -80px;"
                  :loading="loadings.customer"
                  type="primary"
                  size="mini"
                  @click="handleSearch('customer')"
                >查询</el-button>
              </cnd-form-item>
            </el-row>
            <el-row>
              <steelTradeAggrid
                ref="aggridCustomer"
                :column-defs="columnDefs.customer"
                :row-data="tableData.customer"
                :load-data="loadCustomerData"
                :auto-load-data="false"
                row-key="sId"
                :heightinif="250"
                :paginationinif="false"
              />
            </el-row>
          </cnd-form-card>

          <!-- 车船号占用查询 -->
          <cnd-form-card title="车船号占用查询" name="5">
            <el-row>
              <cnd-form-item label="车船号">
                <el-input v-model="form.sVesselNo" placeholder="请输入车船号" />
              </cnd-form-item>
              <cnd-form-item label="采购合同号">
                <el-input v-model="form.sPurContractCode" placeholder="请输入采购合同号" />
              </cnd-form-item>
              <cnd-form-item label="核算组">
                <horizon-search-select
                  type="cost"
                  @change="handleChange($event, 'sCheckGroupId')"
                />
              </cnd-form-item>
              <cnd-form-item>
                <el-button
                  style="margin-left: -80px;"
                  :loading="loadings.vessel"
                  type="primary"
                  size="mini"
                  @click="handleVesselSearch"
                >执行</el-button>
              </cnd-form-item>
            </el-row>
            <el-row>
              <cnd-form-item label="已占用单号">
                <el-input v-model="form.vesselNoResult" type="textarea" :rows="3" disabled />
              </cnd-form-item>
            </el-row>
          </cnd-form-card>

        </cnd-form-card-list>
      </el-form>
    </div>
  </div>
</template>

<script>
import steelTradeAggrid from '@/components/steelTradeAggrid'
import {
  getIsExistSteelNo,
  getDocumentOccupation,
  getDocumentEsbSignStatus,
  getbdaCustomerUser,
  getIsExistsVesselNo
} from '@/api/queryStatistic/purchase'

export default {
  name: 'QueryFacility',
  components: { steelTradeAggrid },

  data() {
    return {
      activeCollapseName: ['1', '2', '3', '4', '5'],

      // 表单数据
      form: {
        steelNo: '',
        documentNo: '',
        documentNoV3: '',
        customerName: '',
        sVesselNo: '',
        sPurContractCode: '',
        sCheckGroupId: '',
        vesselNoResult: ''
      },

      // loading状态
      loadings: {
        steelNo: false,
        document: false,
        ejq: false,
        customer: false,
        vessel: false
      },

      // 表格数据
      tableData: {
        steelNo: [],
        document: [],
        ejq: [],
        customer: []
      },

      // 表格列定义
      columnDefs: {
        steelNo: [
          { headerName: '钢卷号', field: 'sCode', minWidth: 300 },
          {
            headerName: '已占用单号',
            field: 'sMessage',
            minWidth: 500,
            wrapText: true,
            autoHeight: true,
            editable: true,
            cellEditor: 'TableTextarea',
            onCellValueChanged(data) {
              // 回车不改变数据
              data.data.sMessage = data.oldValue
            }
          },
          { headerName: '销售合同号', field: 'sSaleContractCode', minWidth: 400 }
        ],
        document: [
          { headerName: '单据号', field: 'sCode', minWidth: 400 },
          {
            headerName: '已占用单号',
            field: 'sMessage',
            minWidth: 500,
            wrapText: true,
            autoHeight: true,
            editable: true,
            cellEditor: 'TableTextarea',
            onCellValueChanged(data) {
              data.data.sMessage = data.oldValue
            }
          }
        ],
        ejq: [
          { headerName: '单据号', field: 'sCode', minWidth: 400 },
          { headerName: '发送状态', field: 'sMessage', minWidth: 500 }
        ],
        customer: [
          { headerName: '客户', field: 'customerName', minWidth: 350 },
          {
            headerName: '账号',
            field: 'accountName',
            minWidth: 500,
            wrapText: true,
            autoHeight: true,
            editable: true,
            cellEditor: 'TableTextarea',
            onCellValueChanged(data) {
              data.data.accountName = data.oldValue
            }
          },
          {
            headerName: '手机号',
            field: 'phone',
            minWidth: 500,
            wrapText: true,
            autoHeight: true,
            editable: true,
            cellEditor: 'TableTextarea',
            onCellValueChanged(data) {
              data.data.accountName = data.oldValue
            }
          },
          { headerName: '认证状态', field: 'sVerifiedStatusDesc', minWidth: 200 }
        ]
      }
    }
  },

  methods: {
    // 统一的搜索处理
    async handleSearch(type) {
      const value = this.getSearchValue(type)
      if (!this.validateSearch(type, value)) return

      this.loadings[type] = true
      try {
        await this.$refs[`aggrid${this.capitalize(type)}`].loadTableData()
      } finally {
        this.loadings[type] = false
      }
    },

    // 获取搜索值
    getSearchValue(type) {
      const fieldMap = {
        steelNo: 'steelNo',
        document: 'documentNo',
        ejq: 'documentNoV3',
        customer: 'customerName'
      }
      const value = this.form[fieldMap[type]]
      // customer 是多选,直接返回数组值
      if (type === 'customer') {
        return value
      }
      // 其他类型需要 trim
      return value?.trim()
    },

    // 验证搜索条件
    validateSearch(type, value) {
      if (!value || (type === 'customer' && !value.length)) {
        const msgMap = {
          steelNo: '请输入钢卷号',
          document: '请输入单据号',
          ejq: '请输入单据号',
          customer: '请选择客户'
        }
        this.$message.error(msgMap[type])
        return false
      }
      return true
    },

    // 车船号查询
    async handleVesselSearch() {
      const { sVesselNo, sPurContractCode, sCheckGroupId } = this.form
      if (!this.validateVesselSearch(sVesselNo, sPurContractCode, sCheckGroupId)) return

      this.loadings.vessel = true
      this.form.vesselNoResult = ''
      try {
        const res = await getIsExistsVesselNo({ sVesselNo, sPurContractCode, sCheckGroupId })
        if (res.code === '0000') {
          this.$message.success('执行成功')
          this.form.vesselNoResult = res.data
        }
      } catch {
        this.form.vesselNoResult = ''
      } finally {
        this.loadings.vessel = false
      }
    },

    // 验证车船号查询
    validateVesselSearch(sVesselNo, sPurContractCode, sCheckGroupId) {
      if (!sVesselNo) {
        this.$message.error('请输入车船号')
        return false
      }
      if (!sPurContractCode && !sCheckGroupId) {
        this.$message.error('采购合同号、核算组不能同时为空！')
        return false
      }
      return true
    },

    // 加载表格数据
    async loadSteelNoData() {
      const steelNos = this.form.steelNo.trim().split(',')
      const res = await getIsExistSteelNo(steelNos)
      this.tableData.steelNo = this.formatTableData(res.data)
      return res.data
    },

    async loadDocumentData() {
      const docs = this.form.documentNo.trim().split(',')
      const res = await getDocumentOccupation(docs)
      this.tableData.document = this.formatTableData(res.data)
      return res.data
    },

    async loadEjqData() {
      const docs = this.form.documentNoV3.trim().split(',')
      const res = await getDocumentEsbSignStatus(docs)
      this.tableData.ejq = this.formatTableData(res.data)
      return res.data
    },

    async loadCustomerData() {
      const sIds = this.form.customerName.map(e => e.sId)
      const res = await getbdaCustomerUser(sIds)
      this.tableData.customer = this.formatTableData(res.data)
      return res.data
    },

    // 工具方法
    formatTableData(data) {
      return data.map(item => ({
        ...item,
        _selected: false
      }))
    },

    capitalize(str) {
      return str.charAt(0).toUpperCase() + str.slice(1)
    },

    // 事件处理
    handleChangeSelect() {
      // 选择事件处理
    },

    handleChange(val, key) {
      this.form[key] = val?.sId || ''
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .ag-theme-balham {
  .ag-cell-inline-editing {
    height: auto !important;
  }
}
</style>
