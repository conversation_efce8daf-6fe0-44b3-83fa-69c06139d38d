<template>
  <steelTradeAggrid
    ref="aggrid"
    class="stellTradeAggridBor"
    row-key="sId"
    :auto-height="true"
    :row-data="rowData"
    :load-data="loadData"
    :column-defs="columnDefs"
    :header-total="headerTotal"
    :footer-total="footerTotal"
    table-selection="multiple"
    :paginationinif="false"
    @selectedChange="gridSelectedChange"
  />
</template>

<script>
import {
  SteelFormat,
  DictUtil
} from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
var Decimal = window.Decimal
import steelTradeAggrid from '@/components/steelTradeAggrid'
import {
  getContractSaleProcessPayment
} from '@/api/queryStatistic/sale'

export default {
  components: {
    steelTradeAggrid
  },
  props: {
    sId: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      rowData: [],
      columnDefs: [{
        headerName: this.$t('grid.others.receiptDate'),
        field: 'sApplyDate',
        valueGetter: params => Moment.time('YYYY-MM-DD', params.data.sApplyDate),
        valueFormatter(params) {
          return Moment.time('YYYY-MM-DD', params.data.sApplyDate)
        }
      }, {
        headerName: this.$t('grid.others.customer'),
        field: 'vCustomerName'
      }, {
        headerName: this.$t('grid.others.amountReceived'),
        field: 'sTaxAmt',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatPrice(params.value)
        }
      }, {
        headerName: this.$t('grid.others.transactionType'),
        field: 'sInteractionSubType',
        valueGetter: (params) => {
          const status = this.payTypeList.filter(item => item.sCodeValue === params.data.sInteractionSubType)
          return status.length ? status[0].sCodeName : params.data.sInteractionSubType
        }
      }, {
        headerName: this.$t('grid.others.paragraphType'),
        field: 'sFundType',
        valueGetter: (params) => {
          const status = this.payPaymentTypeList.filter(item => item.sCodeValue === params.data.sFundType)
          return status.length ? status[0].sCodeName : params.data.sFundType
        }
      }, {
        headerName: this.$t('grid.others.noteNumber'),
        field: 'sBillCode'
      }, {
        headerName: this.$t('grid.others.maturityDateOfAcceptance'),
        field: 'sMaturityDate',
        valueGetter: params => Moment.time('YYYY-MM-DD', params.data.sMaturityDate),
        valueFormatter(params) {
          return Moment.time('YYYY-MM-DD', params.data.sMaturityDate)
        }
      }, {
        headerName: this.$t('grid.others.documentType'),
        field: 'sUpSheetType',
        valueGetter: (params) => {
          const status = this.paySheetTypeList.filter(item => item.sCodeValue === params.data.sUpSheetType)
          return status.length ? status[0].sCodeName : params.data.sUpSheetType
        }
      }, {
        headerName: this.$t('grid.others.originalDocumentNumber'),
        field: 'sUpCode'
      }, {
        headerName: this.$t('grid.title.status'),
        field: 'sSheetStatus',
        valueGetter: (params) => {
          const status = this.sSheetStatusList.filter(item => item.sCodeValue === params.data.sSheetStatus)
          return status.length ? status[0].sCodeName : params.value
        }
      }],
      headerTotal: [],
      footerTotal: [],
      payModeList: [],
      paySubtypeList: [],
      paySheetTypeList: [],
      sSheetStatusList: []
    }
  },
  watch: {
    sId() {
      this.$refs.aggrid.loadTableData()
    }
  },
  mounted() {
    this.loadDict()
  },
  methods: {
    loadDict() {
      DictUtil.getDict([
        'pay.subtype',
        'pay.payment.type',
        'sheet.payment.all',
        'dev.common.sheet.status'
      ], res => {
        this.payTypeList = res[0].dicts
        this.payPaymentTypeList = res[1].dicts
        this.paySheetTypeList = res[2].dicts
        this.sSheetStatusList = res[3].dicts
      })
    },
    loadData() {
      return new Promise((resolve, reject) => {
        getContractSaleProcessPayment({
          sContractId: this.sId
        }).then(res => {
          this.rowData = res.data.map(item => {
            item._selected = false
            return item
          })
          resolve(res.data)
          this.headerTotal = res.data.reduce((prev, next, index) => {
            prev[1].count = +new Decimal(prev[1].count).add(next.sTaxAmt || 0)
            if (index + 1 === res.data.length) {
              prev[1].count = SteelFormat.formatPrice(prev[1].count)
            }
            return prev
          }, [{
            key: 'count',
            count: res.data.length,
            unit: this.$t('pagination.items')
          }, {
            title: this.$t('grid.others.amountReceived'),
            count: 0,
            unit: this.$t('grid.others.yuan')
          }])
        })
      })
    },
    gridSelectedChange(list) {
      this.footerTotal = list.reduce((prev, next, index) => {
        if (next._selected) {
          prev[1].count = +new Decimal(prev[1].count).add(next.sTaxAmt || 0)
        }
        if (index + 1 === list.length) {
          prev[1].count = SteelFormat.formatPrice(prev[1].count)
        }
        return prev
      }, [{
        key: 'count',
        count: list.filter(item => item._selected).length,
        unit: this.$t('pagination.items')
      }, {
        title: this.$t('grid.others.amountReceived'),
        count: 0,
        unit: this.$t('grid.others.yuan')
      }])
    }
  }
}
</script>
<style lang="scss" scoped>
.stellTradeAggridBor {
  ::v-deep .steel-trade-aggrid-header {
    border-right: 0;
    border-left: 0;
  }
  ::v-deep .ag-root-wrapper {
    border-right: 0;
    border-left: 0;
  }
  ::v-deep .steel-trade-aggrid-footer {
    border: 0;
  }
}
</style>
