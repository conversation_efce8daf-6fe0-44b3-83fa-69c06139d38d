<template>
  <steelTradeAggrid
    ref="aggrid"
    class="stellTradeAggridBor"
    row-key="sId"
    :auto-height="true"
    :row-data="rowData"
    :load-data="loadData"
    :column-defs="columnDefs"
    :header-total="headerTotal"
    :footer-total="footerTotal"
    table-selection="multiple"
    :paginationinif="false"
    @selectedChange="gridSelectedChange"
  />
</template>

<script>
import {
  SteelFormat,
  DictUtil
} from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
var Decimal = window.Decimal
import steelTradeAggrid from '@/components/steelTradeAggrid'
import {
  getContractSaleProcessInvoice
} from '@/api/queryStatistic/sale'

export default {
  components: {
    steelTradeAggrid
  },
  props: {
    sId: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      rowData: [],
      columnDefs: [{
        headerName: this.$t('grid.others.invoiceRegistrationNumber'),
        field: 'sCode'
      }, {
        headerName: this.$t('grid.title.status'),
        field: 'sInvoiceStatus',
        valueGetter: params => {
          const filterItem = this.invoiceStatus.filter(item => item.sCodeValue === params.data.sInvoiceStatus)
          return filterItem.length ? filterItem[0].sCodeName : params.value
        }
      }, {
        headerName: this.$t('grid.others.invoiceDateKey'),
        field: 'sInvoiceDate',
        valueGetter(params) {
          return Moment.time('YYYY-MM-DD', params.data.sInvoiceDate)
        }
      }, {
        headerName: this.$t('grid.others.vatInvoiceNumber'),
        field: 'vInvoiceCode'
      }, {
        headerName: this.$t('grid.others.invoiceQuantity'),
        field: 'vInvoiceQty',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatThousandthSign(params.value, 4)
        }
      }, {
        headerName: this.$t('grid.others.invoiceAmount'),
        field: 'vInvoiceAmt',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatPrice(params.value)
        }
      }],
      footerTotal: [],
      headerTotal: [],
      invoiceStatus: []
    }
  },
  watch: {
    sId() {
      this.$refs.aggrid.loadTableData()
    }
  },
  mounted() {
    this.loadDict()
  },
  methods: {
    loadDict() {
      DictUtil.getDict([
        'dev.common.sheet.status'
      ], res => {
        this.invoiceStatus = res[0].dicts
      })
    },
    loadData() {
      return new Promise((resolve, reject) => {
        getContractSaleProcessInvoice({
          sContractId: this.sId
        }).then(res => {
          this.rowData = res.data.map(item => {
            item._selected = false
            return item
          })
          resolve(res.data)
          this.headerTotal = res.data.reduce((prev, next, index) => {
            prev[1].count = +new Decimal(prev[1].count).add(next.vInvoiceQty || 0)
            prev[2].count = +new Decimal(prev[2].count).add(next.vInvoiceAmt || 0)
            if (index + 1 === res.data.length) {
              prev[1].count = SteelFormat.formatThousandthSign(prev[1].count, 4)
              prev[2].count = SteelFormat.formatPrice(prev[2].count)
            }
            return prev
          }, [{
            key: 'count',
            count: res.data.length,
            unit: this.$t('pagination.items')
          }, {
            title: this.$t('grid.others.invoiceQuantity'),
            count: 0,
            unit: this.$t('grid.others.ton')
          }, {
            title: this.$t('grid.others.invoiceAmount'),
            count: 0,
            unit: this.$t('grid.others.yuan')
          }])
        })
      })
    },
    gridSelectedChange(list) {
      this.footerTotal = list.reduce((prev, next, index) => {
        if (next._selected) {
          prev[1].count = +new Decimal(prev[1].count).add(next.vInvoiceQty || 0)
          prev[2].count = +new Decimal(prev[2].count).add(next.vInvoiceAmt || 0)
        }
        if (index + 1 === list.length) {
          prev[1].count = SteelFormat.formatThousandthSign(prev[1].count, 4)
          prev[2].count = SteelFormat.formatPrice(prev[2].count)
        }
        return prev
      }, [{
        key: 'count',
        count: list.filter(item => item._selected).length,
        unit: this.$t('pagination.items')
      }, {
        title: this.$t('grid.others.invoiceQuantity'),
        count: 0,
        unit: this.$t('grid.others.ton')
      }, {
        title: this.$t('grid.others.invoiceAmount'),
        count: 0,
        unit: this.$t('grid.others.yuan')
      }])
    }
  }
}

</script>
<style lang="scss" scoped>
.stellTradeAggridBor {
  ::v-deep .steel-trade-aggrid-header {
    border-right: 0;
    border-left: 0;
  }
  ::v-deep .ag-root-wrapper {
    border-right: 0;
    border-left: 0;
  }
  ::v-deep .steel-trade-aggrid-footer {
    border: 0;
  }
}
</style>
