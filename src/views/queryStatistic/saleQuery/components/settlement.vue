<template>
  <div class="auto-page-title">
    <div class="btn-group nobtn-group no-border-right no-border-left">
      <div class="text">
        {{ $t('grid.title.settlementMethod') }}
      </div>
    </div>
    <steelTradeAggrid
      ref="aggrid"
      class="stellTradeAggridBor"
      :heightinif="150"
      :column-defs="paymentColumnDefs"
      :row-data="paymentRowData"
      :load-data="loadPaymentData"
      :paginationinif="false"
    />
    <div class="btn-group nobtn-group no-border-left no-border-right no-border-top" style="border-bottom:1px solid #dfe4ed;">
      <div class="text">
        {{ $t('grid.others.settlementTerms') }}
      </div>
    </div>
    <div style="padding:10px 20px 0 0;background-color: #ffffff">
      <cnd-form :model="form" disabled label-width="130px" style="margin-bottom: 0px">
        <el-row>
          <cnd-form-item :label="$t('grid.others.numberOfDaysUnderwritten')">
            <el-input v-model="form.sMinDays" disabled />
          </cnd-form-item>
          <cnd-form-item :label="$t('grid.others.settlementDays')">
            <el-input v-model="form.sPayDays" disabled />
          </cnd-form-item>
          <cnd-form-item v-show="form.sIsCashRt === '0'" :label="$t('grid.tips.doesItInvolveCashBackInterest')">
            <el-select v-model="form.sIsCashRt" filterable disabled :placeholder="$t('components.pleaseSelect')">
              <el-option
                v-for="item in baseYesNo"
                :key="item.sCodeValue"
                :label="item.sCodeName"
                :value="item.sCodeValue"
              />
            </el-select>
          </cnd-form-item>
          <!-- <cnd-form-item v-show="form.sIsCashRt === '1'" :label="this.$t('grid.others.cashRebateMethod')">
            <el-select v-model="form.sCashRtType" filterable disabled :placeholder="$t('components.pleaseSelect')">
              <el-option
                v-for="item in dictList['trade.irt.return.type']"
                :key="item.sCodeValue"
                :label="item.sCodeName"
                :value="item.sCodeValue"
              />
            </el-select>
          </cnd-form-item> -->
          <cnd-form-item v-show="form.sIsCashRt === '1' && form.sCashRtType==='20'" :label="$t('grid.others.cashRefundRateMonthlyInterest')">
            <cnd-input-number v-model="form.sCashRtRate" type="percent" disabled />
          </cnd-form-item>
          <cnd-form-item v-show="form.sIsCashRt === '1' && form.sCashRtType==='10'" :label="$t('grid.others.singleTonInterestRebate')">
            <el-input v-model="form.sCashSignalRate" disabled />
          </cnd-form-item>
          <cnd-form-item v-show="form.sIsAcceptRt === '0'" :label="$t('grid.tips.doesItInvolveThePromissoryNote')">
            <el-select v-model="form.sIsAcceptRt" filterable disabled :placeholder="$t('components.pleaseSelect')">
              <el-option
                v-for="item in baseYesNo"
                :key="item.sCodeValue"
                :label="item.sCodeName"
                :value="item.sCodeValue"
              />
            </el-select>
          </cnd-form-item>
          <!-- <cnd-form-item v-show="form.sIsAcceptRt === '1'" :label="$t('grid.others.methodOfDiscounting')">
            <el-select v-model="form.sAcceptRtType" filterable disabled :placeholder="$t('components.pleaseSelect')">
              <el-option
                v-for="item in dictList['trade.irt.return.type']"
                :key="item.sCodeValue"
                :label="item.sCodeName"
                :value="item.sCodeValue"
              />
            </el-select>
          </cnd-form-item> -->
          <cnd-form-item v-show="form.sIsAcceptRt === '1' && form.sAcceptRtType==='20'" :label="$t('grid.others.acceptanceDiscountRnterestKey')">
            <cnd-input-number v-model="form.sAcceptRtRate" type="percent" disabled />
          </cnd-form-item>
          <cnd-form-item v-show="form.sIsAcceptRt === '1' && form.sAcceptRtType==='10'" :label="$t('grid.others.singleTonDiscount')">
            <el-input v-model="form.sAcceptSignalRate" disabled />
          </cnd-form-item>
        </el-row>
      </cnd-form>
    </div>
    <div class="btn-group nobtn-group no-border-right no-border-left" style="border-right: 0;">
      <div class="text">
        <!-- 结算条款明细 -->
        {{ $t('grid.others.settlementTermsDetail') }}
      </div>
    </div>
    <steelTradeAggrid
      ref="clauseGrid"
      class="stellTradeAggridBor"
      :heightinif="150"
      :column-defs="clauseDetailDefs"
      :row-data="clauseDetailRowData"
      :load-data="loadClauseData"
      :paginationinif="false"
      :auto-load-data="false"
    />
  </div>
</template>

<script>
import {
  DictUtil,
  SteelFormat
} from 'cnd-horizon-utils'
import { getCnDitc } from '@/utils/common'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import {
  getContractSaleSettlementList,
  getContractSaleSettlementClause,
  getSettlementClauseDetail
} from '@/api/queryStatistic/sale'

export default {
  components: { steelTradeAggrid },
  props: {
    sId: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      paymentColumnDefs: [{
        headerName: this.$t('grid.others.beforeAndAfterTheGoods'),
        field: 'sSettlementType',
        valueGetter: (params) => {
          const status = this.settlementList.filter(item => item.sCodeValue === params.data.sSettlementType)
          return status.length ? status[0].sCodeName : params.data.sSettlementType
        }
      }, {
        headerName: this.$t('grid.others.whetherRiskTransfer'),
        field: 'sIsRiskTransfer',
        valueGetter: params => {
          const status = this.baseYesNo.filter(item => item.sCodeValue === params.data.sIsRiskTransfer)
          return status.length ? status[0].sCodeName : params.data.sIsRiskTransfer
        }
      }, {
        headerName: this.$t('grid.title.paymentMethod'),
        field: 'sPaymentType',
        valueGetter: params => {
          const status = this.payModes.filter(item => item.sCodeValue === params.data.sPaymentType)
          return status.length ? status[0].sCodeName : params.data.sPaymentType
        }
      }, {
        headerName: this.$t('grid.others.dateMethod'),
        field: 'sDateType',
        valueGetter: params => {
          const status = this.dateTypes.filter(item => item.sCodeValue === params.data.sDateType)
          return status.length ? status[0].sCodeName : params.data.sDateType
        }
      }, {
        headerName: this.$t('grid.others.Days'),
        field: 'sDays'
      }, {
        headerName: this.$t('grid.others.dateMethodDescription'),
        field: 'sDateTypeDescribe'
      },
      // {
      //   headerName: '支付凭据',
      //   field: 'sPayReceipts'
      // },
      {
        headerName: this.$t('grid.title.paymentRatio'),
        field: 'sPaymentRate',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.toPercent(params.value)
        }
      }, {
        headerName: this.$t('grid.others.originalCurrencyAmount'),
        field: 'sTaxAmt',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatPrice(params.value)
        }
      }],
      paymentRowData: [],
      clauseDetailDefs: [
        {
          headerName: this.$t('grid.others.interestBearingItems'),
          field: 'sField1',
          valueGetter: (params) => {
            return getCnDitc(params, this.options['esc.clause.item'], 'sField1')
          }
        },
        {
          headerName: this.$t('grid.others.startDays'),
          field: 'sField11',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value)
          }
        },
        {
          headerName: this.$t('grid.others.cutOffDays'),
          field: 'sField12',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return params.data.sField2 === '1' ? params.value : SteelFormat.formatThousandthSign(params.value)
          }
        },
        {
          headerName: this.$t('grid.others.whetherUnlimited'),
          cellStyle: { textAlign: 'right' },
          field: 'sField2',
          valueGetter: params => {
            // return params.value ? this.$t('grid.others.yes') : this.$t('btns.no')
            return getCnDitc(params, this.baseYesNo, 'sField2')
          }
        },
        {
          headerName: this.$t('grid.others.interestRateTag'),
          field: 'sField6',
          cellStyle: { textAlign: 'right' },
          valueFormatter(params) {
            if (params.value) {
              const Digits = String(params.value).length - (String(params.value).indexOf('.') + 1)
              if (Digits > 3) {
                return SteelFormat.toPercent(params.value)
              } else {
                return SteelFormat.toPercent(params.value, 2)
              }
            } else {
              return ''
            }
          }
        },
        {
          headerName: this.$t('grid.others.singleTonMarkup'),
          field: 'sField7',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return params.value ? SteelFormat.formatPrice(params.value) : ''
          }
        }
      ],
      clauseDetailRowData: [],
      settlementList: [],
      payModes: [],
      baseYesNo: [],
      dateTypes: [],
      form: {},
      dictList: {
        'trade.irt.return.type': null
      },
      options: {
        'esc.clause.item': []
      }
    }
  },
  watch: {
    sId() {
      this.$refs.aggrid.loadTableData()
      this.loadDict()
      this.loadDetail()
    }
  },
  mounted() {
    this.loadDict()
    this.loadDetail()
  },
  methods: {
    loadDict() {
      DictUtil.getDict(['trade.settlement.type', 'trade.pay.mode', 'base.yes-no', 'trade.irt.return.type', 'trade.date.type', 'esc.clause.item'], res => {
        this.settlementList = res[0].dicts
        this.payModes = res[1].dicts
        this.baseYesNo = res[2].dicts
        this.dictList['trade.irt.return.type'] = res[3].dicts
        this.dateTypes = res[4].dicts
        this.options['esc.clause.item'] = res[5].dicts
      })
    },
    loadPaymentData() {
      return new Promise((resolve, reject) => {
        getContractSaleSettlementList({
          sContractId: this.sId
        }).then(res => {
          this.paymentRowData = res.data
          resolve(res.data)
        }).catch(() => {
          reject(0)
        })
      })
    },
    loadDetail() {
      getContractSaleSettlementClause({
        sContractId: this.sId
      }).then(res => {
        console.log('hhh2', res)
        this.form = res.data
        this.$refs.clauseGrid.loadTableData()
      })
    },
    loadClauseData() {
      return new Promise((resolve, reject) => {
        getSettlementClauseDetail({
          sUpId: this.sId,
          // sUpEntity: 'com.chinacnd.horizon.rock.bizkit.contract.sale.entity.ConSaleContract',
          classifyId: '136111111111111'
        }).then(res => {
          console.log('hhh3', res)
          this.clauseDetailRowData = res.data
          resolve(res.data)
        }).catch(() => {
          reject(0)
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.nobtn-group{
  height:34px;
  line-height:17px;
}
.stellTradeAggridBor {
  ::v-deep .steel-trade-aggrid-header {
    border-right: 0;
    border-left: 0;
  }
  ::v-deep .ag-root-wrapper {
    border-right: 0;
    border-left: 0;
  }
}
</style>
