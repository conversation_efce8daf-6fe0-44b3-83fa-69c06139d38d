<template>
  <div class="page-container">
    <p class="page-title">{{ $t('grid.others.salesContractProcess') }}</p>
    <div class="layout-content auto-page-title">
      <steel-search-form
        ref="searchForm"
        :form-items="formItems"
        @search="onSearch"
      />
      <div class="btn-group mt-10">
        <div class="text">
          {{ $t('grid.others.salesContractProcess') }}{{ $t('grid.others.list') }}
        </div>
        <div>
          <el-button
            type="primary"
            size="mini"
            @click="processQuery"
          >相关采购进程查询</el-button>
          <export-btn
            class="ml-10"
            :file-name="$t('grid.others.salesContractProcess')"
            api-url="/esc/contract/sale/process/excel/export"
            :post-params="searchInfo"
            :export-config="exportConfig"
            :get-grid-api="getGridApi"
            :children-post-params="childrenPostParams"
            children-api-url="/esc/contract/sale/process/detail/excel/export"
            :children-file-name="childrenFileName"
          />
        </div>
      </div>
      <!-- 销售合同进程 -->
      <steelTradeAggrid
        ref="aggrid"
        :heightinif="300"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        table-selection="multiple"
        row-key="sId"
        @selectedChange="handleChange"
        @pageChange="selectContractId = null"
        @rowClicked="handleChangeContract"
      />
      <el-tabs
        v-model="activeName"
        style="height:auto"
        class="mt-10"
      >
        <el-tab-pane
          v-for="tab in tabList"
          :key="tab.name"
          :label="tab.label"
          :name="tab.name"
        >
          <component
            :is="tab.name"
            v-if="activeName === tab.name"
            :ref="tab.name"
            :s-id="selectContractId"
          />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import {
  SteelFormat,
  DictUtil
} from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
var Decimal = window.Decimal
import {
  getContractSaleProcessList
} from '@/api/queryStatistic/sale'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import contract from './components/contract'
import enterWare from './components/enterWare'
import payment from './components/payment'
import otherCollections from './components/otherCollections'
import settlement from './components/settlement'
import invoice from './components/invoice'
import { statusDict3 } from '@/utils/dict'
import exportBtn from '@/components/exportBtnV2'
import { handleDict } from '@/utils/common'
const natureTypeDict = [
  { sCodeName: '未结算', sCodeValue: '00' },
  { sCodeName: '预结算', sCodeValue: '10' },
  { sCodeName: '终结算', sCodeValue: '20' }
]
export default {
  name: 'SaleQuery',
  components: {
    steelTradeAggrid,
    contract,
    enterWare,
    payment,
    otherCollections,
    settlement,
    invoice,
    exportBtn
  },
  data() {
    return {
      activeName: 'contract',
      tabList: [{
        label: this.$t('grid.others.contractDetails'),
        name: 'contract'
      }, {
        label: this.$t('grid.others.outOfPositionDetails'),
        name: 'enterWare'
      }, {
        label: this.$t('grid.others.receiptAndPaymentDetails'),
        name: 'payment'
      }, {
        label: '其他收款',
        name: 'otherCollections'
      }, {
        label: this.$t('grid.tabs.invoiceDetails'),
        name: 'invoice'
      }, {
        label: this.$t('grid.tabs.settlementInformation'),
        name: 'settlement'
      }],
      formItems: [{
        label: this.$t('grid.title.salesContractNumber'),
        value: 'sCode',
        type: 'elInput',
        placeholder: this.$t('grid.others.pleaseEnterTheSalesContractNumber')
      }, {
        label: this.$t('grid.others.supplier'), // 供应商,
        value: 'sSupplierId',
        type: 'cndInputDialogItem',
        dialogType: 'customer',
        defaultUrl: '/esc/customer/page',
        option: { valueKey: 'sPath' },
        placeholder: this.$t('grid.others.pleaseSelectCompany')
      }, {
        label: this.$t('grid.others.customer'),
        value: 'sCustomerId',
        type: 'cndInputDialogItem',
        dialogType: 'customer',
        defaultUrl: '/esc/customer/page',
        option: { valueKey: 'sPath' },
        placeholder: this.$t('grid.others.pleaseSelectTheCustomer')
      }, {
        label: this.$t('grid.others.itemNumberTag'),
        value: 'sProjectCode',
        type: 'elInput',
        placeholder: this.$t('grid.others.pleaseEnterTheItemNumberTag')
      }, {
        label: this.$t('grid.title.company'),
        value: 'sCompanyId',
        type: 'cndInputDialog',
        dialogType: 'company',
        placeholder: this.$t('grid.others.pleaseSelectCompany')
      }, {
        label: this.$t('grid.title.accountingGroup'),
        value: 'sCheckGroupId',
        type: 'cndInputDialog',
        dialogType: 'cost'
      }, {
        label: this.$t('grid.title.personnel'),
        value: 'sStaffId',
        type: 'cndInputDialogItem',
        option: { valueKey: 'sPath' },
        dialogType: 'staff'
      }, {
        label: this.$t('grid.title.status'),
        value: 'sSheetStatus',
        type: 'elSelect',
        multiple: true,
        firstCall: true,
        dict: statusDict3
      },
      {
        label: this.$t('grid.title.createdAt'),
        value: ['sCreateTime', 'vCreateTimeTo'],
        placeholder: [this.$t('grid.others.pleaseSelectTheDate'), this.$t('grid.others.pleaseSelectTheDate')],
        default: ['', ''],
        type: 'elDatePicker',
        unlinkPanels: true,
        itemType: 'occultation'
      },
      {
        label: this.$t('grid.others.executionTime'),
        value: ['sRatifyDate', 'vRatifyDateTo'],
        placeholder: [this.$t('grid.others.pleaseSelectTheDate'), this.$t('grid.others.pleaseSelectTheDate')],
        type: 'elDatePicker',
        unlinkPanels: true,
        itemType: 'occultation'
      },
      {
        label: this.$t('grid.title.createdBy'),
        value: 'sCreator',
        type: 'cndInputDialog',
        dialogType: 'creater',
        itemType: 'occultation'
      },
      {
        label: '结算状态',
        value: 'sNatureType',
        type: 'elSelect',
        itemType: 'occultation',
        dict: natureTypeDict,
        placeholder: this.$t('components.pleaseSelect')
      },
      {
        label: '经营单位',
        value: 'sManagementId',
        type: 'cndInputDialog',
        dialogType: 'escOrg',
        itemType: 'occultation'
      }
      ],
      columnDefs: [
        {
          headerName: this.$t('grid.others.itemNumberTag'),
          field: 'sProjectCode',
          pinned: 'left'
        },
        {
          headerName: this.$t('grid.title.salesContractNumber'),
          field: 'sCode',
          pinned: 'left'
        },
        {
          headerName: this.$t('grid.others.supplier'), // 供应商,
          field: 'vSupplierName'
        },
        {
          headerName: this.$t('grid.others.customer'),
          field: 'vCustomerName'
        },
        {
          headerName: this.$t('grid.title.status'),
          field: 'sSheetStatus',
          valueGetter: (params) => {
            const status = this.sheetStatus.filter(item => item.sCodeValue === params.data.sSheetStatus)
            return status.length ? status[0].sCodeName : params.data.sSheetStatus
          }
        },
        {
          headerName: '结算状态',
          field: 'sNatureType',
          valueGetter: (params) => {
            const status = this.natureTypeDict.filter(item => item.sCodeValue === params.data.sNatureType)
            return params.data._hiddenCheckbox ? '' : status.length ? status[0].sCodeName : '未结算'
          }
        },
        {
          headerName: this.$t('grid.others.contractQuantity'),
          field: 'vContractQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: this.$t('grid.title.contractAmount'),
          field: 'vContractAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: this.$t('grid.others.marginAmount'),
          field: 'vMarginAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: this.$t('grid.others.receivedDeposit'),
          field: 'vRecMarginAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: this.$t('grid.others.quantityOut'),
          field: 'vStockQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: this.$t('grid.others.amountReceived'),
          field: 'vPaymentAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: this.$t('grid.others.invoiceAmount'),
          field: 'vInvoiceAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: '其他收款',
          field: 'sTaxAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: this.$t('grid.title.company'),
          field: 'vCompanyName'
        },
        {
          headerName: this.$t('grid.title.accountingGroup'),
          field: 'vCheckGroupName'
        },
        {
          field: 'vDepartmentName',
          headerName: this.$t('grid.others.department')
        },
        {
          headerName: this.$t('grid.title.personnel'),
          field: 'vStaffName'
        },
        {
          headerName: this.$t('grid.title.createdBy'),
          field: 'vCreatorName'
        },
        {
          headerName: this.$t('grid.title.createdAt'),
          field: 'sCreateTime',
          minWidth: 150,
          valueGetter(params) {
            return params.data._hiddenCheckbox ? '' : Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
          }
        },
        {
          headerName: this.$t('grid.others.executionTime'),
          field: 'sRatifyDate',
          valueGetter(params) {
            return params.data._hiddenCheckbox ? '' : Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sRatifyDate)
          }
        },
        {
          field: 'vManagementName',
          headerName: '经营单位'
        }
      ],
      exportConfig: [
        { label: this.$t('grid.others.itemNumberTag'), value: 'sProjectCode' },
        { label: this.$t('grid.title.salesContractNumber'), value: 'sCode' },
        { label: this.$t('grid.others.supplier'), value: 'vSupplierName' },
        { label: this.$t('grid.others.customer'), value: 'vCustomerName' },
        { label: this.$t('grid.title.status'), value: 'sSheetStatus',
          setValue: value => {
            return handleDict(value, this.sheetStatus)
          }
        },
        { label: '结算状态', value: 'sNatureType',
          setValue: value => {
            return handleDict(value, this.natureTypeDict) || '未结算'
          }
        },
        { label: this.$t('grid.others.contractQuantity'), value: 'vContractQty',
          setValue: (value) => { return Number((+value).toFixed(4)) }
        },
        { label: this.$t('grid.title.contractAmount'), value: 'vContractAmt',
          setValue: (value) => { return Number((+value).toFixed(2)) }
        },
        { label: this.$t('grid.others.marginAmount'), value: 'vMarginAmt',
          setValue: (value) => { return Number((+value).toFixed(2)) }
        },
        { label: this.$t('grid.others.receivedDeposit'), value: 'vRecMarginAmt',
          setValue: (value) => { return Number((+value).toFixed(2)) }
        },
        { label: this.$t('grid.others.quantityOut'), value: 'vStockQty',
          setValue: (value) => { return Number((+value).toFixed(4)) }
        },
        { label: this.$t('grid.others.amountReceived'), value: 'vPaymentAmt',
          setValue: (value) => { return Number((+value).toFixed(2)) }
        },
        { label: this.$t('grid.others.invoiceAmount'), value: 'vInvoiceAmt',
          setValue: (value) => { return Number((+value).toFixed(2)) }
        },
        { label: '其他收款', value: 'sTaxAmt',
          setValue: (value) => { return Number((+value).toFixed(2)) }
        },
        { label: this.$t('grid.title.company'), value: 'vCompanyName' },
        { label: this.$t('grid.title.accountingGroup'), value: 'vCheckGroupName' },
        { value: 'vDepartmentName', label: this.$t('grid.others.department') },
        { label: this.$t('grid.title.personnel'), value: 'vStaffName' },
        { label: this.$t('grid.title.createdBy'), value: 'vCreatorName' },
        { label: this.$t('grid.title.createdAt'), value: 'sCreateTime',
          setValue: value => { return Moment.time('YYYY-MM-DD HH:mm:ss', value) }
        },
        { label: this.$t('grid.others.executionTime'), value: 'sRatifyDate',
          setValue: value => { return Moment.time('YYYY-MM-DD HH:mm:ss', value) }
        },
        { label: '经营单位', value: 'vManagementName' }
      ],
      rowData: [],
      sheetStatus: [],
      natureTypeDict: [],
      selectContractId: null,
      sCode: null,
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      searchInfo: null,
      childrenPostParams: {},
      childrenFileName: null
    }
  },
  beforeCreate() {
    DictUtil.getDict(['dev.common.sheet.status', 'esc.settle.nature.type'], res => {
      this.sheetStatus = res[0].dicts
      this.natureTypeDict = res[1].dicts
      this.natureTypeDict.push({ sCodeName: '未结算', sCodeValue: '00' })
      console.log('this.natureTypeDict: ', this.natureTypeDict)
    })
  },
  created() {
    if (this.$route.query) {
      this.sCode = this.$route.query.sCode || ''
      this.formItems[0].default = this.$route.query.sCode || ''
      this.formItems[3].default = this.$route.query.sProjectCode || ''
      this.formItems[9].default = [undefined, undefined]
      this.$nextTick(() => {
        this.$refs.searchForm.submit()
      })
    } else {
      this.formItems[0].default = ''
      this.formItems[3].default = ''
    }
  },
  mounted() {
    console.log(this.formItems[0])
    if (!this.$route.query.sCode) {
      // this.sCode = this.$route.query.sCode
      this.formItems[0].default = ''
    }
    if (!this.$route.query.sProjectCode) {
      this.formItems[3].default = ''
    }
    if (this.$route.query.sCode || this.$route.query.sProjectCode) {
      this.onSearch()
    } else {
      this.onSearch(false)
    }
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      searchInfo.sSheetStatus = searchInfo.sSheetStatus?.toString()
      this.searchInfo = searchInfo
      if (load) {
        this.selectContractId = null
        this.$refs.aggrid.loadTableData()
      }
    },
    loadData(pagination = { page: 0, limit: 30 }) {
      const _this = this
      return new Promise((resolve, reject) => {
        getContractSaleProcessList(
          this.searchInfo,
          pagination
        ).then(res => {
          this.activeName = 'contract'
          this.rowData = res.data.contractPage.content.map((item, index) => {
            if (!this.selectContractId) {
              item._selected = index === 0
            } else {
              item._selected = item.sId === this.selectContractId
            }
            return item
          })
          if (res.data.contractPage.content.length === 0) {
            this.selectContractId = null
          } else {
            _this.handleChangeContract({ data: res.data.contractPage.content[0] })
          }
          // setTimeout(() => {
          //   this.$refs.aggrid.gridApi.setPinnedBottomRowData([this.computeTotal()])
          // }, 0)
          resolve(res.data.contractPage)
        }).catch(() => {
          reject(0)
        })
      })
    },
    computeTotal(type = 'all') {
      return Object.assign(this.rowData.reduce((prev, next) => {
        Object.keys(prev).forEach(key => {
          if (type === 'all') {
            prev[key] = new Decimal(+prev[key]).add(+next[key] || 0)
          } else {
            if (next._selected) {
              prev[key] = new Decimal(+prev[key]).add(+next[key] || 0)
            }
          }
        })
        return prev
      }, {
        vContractQty: 0,
        vContractAmt: 0,
        vMarginAmt: 0,
        vRecMarginAmt: 0,
        vStockQty: 0,
        vPaymentAmt: 0,
        vInvoiceAmt: 0,
        sTaxAmt: 0
      }), {
        sProjectCode: type === 'all' ? this.$t('grid.others.amountTo') : `${this.$t('components.selected')}${this.rowData.filter(item => item._selected).length}${this.$t('pagination.items')}`,
        sCreateTime: null,
        sRatifyDate: null,
        _selected: false,
        _hiddenCheckbox: true
      })
    },
    handleChange() {
      this.$refs.aggrid.getSelectedData(res => {
        setTimeout(() => {
          this.$refs.aggrid.gridApi.setPinnedBottomRowData([this.computeTotal(res.length ? 'selected' : 'all')])
        }, 0)
      })
    },
    handleChangeContract(params) {
      if (!params.data._hiddenCheckbox) {
        this.selectContractId = params.data.sId
        // const activeObj = this.tabList.find(el => el.name === this.activeName)
        this.childrenFileName = params.data.sCode + '子表明细'
        this.childrenPostParams = {
          sId: params.data.sId
        }
      }
      // this.activeName = 'contract'
      // params.node.setSelected(true, true)
    },
    processQuery() {
      this.$refs.aggrid.getSelectedData(res => {
        if (res.length === 1) {
          this.$router.push({
            path: '/purchaseQuerys',
            query: {
              sProjectCode: res[0].sProjectCode,
              activeId: localStorage.getItem('menuId')
            }
          })
        } else {
          this.$message.warning(this.$t('grid.others.pleaseSelectAData'))
        }
      })
    }
  }
}
</script>
