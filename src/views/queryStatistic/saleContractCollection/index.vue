<template>
  <div class="page-container">
    <p class="page-title">合同收汇明细</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form ref="searchForm" :form-items="formItems" @search="onSearch" />
      <div class="btn-group mt-10" style="display: flex">
        <div class="text" style="flex:1">
          合同收汇明细列表
        </div>
        <!-- <div>
          <export-btn
            class="ml-10"
            :file-name="'合同收汇明细'"
            api-url=""
            :post-params="searchInfo"
            :export-config="exportConfig"
            :get-grid-api="getGridApi"
          />
        </div> -->
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        :header-total="headerCount"
        :footer-total="footerCount"
        table-selection="multiple"
        @selectedChange="handleFooterCount"
        @cellValueChanged="cellValueChanged"
      />
    </div>
  </div>
</template>

<script>
// import { Moment } from 'cnd-utils'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import exportBtn from '@/components/exportBtnV2'
import { SteelFormat, DictUtil } from 'cnd-horizon-utils'
import {
  getLedgerSaleRecPage
} from '@/api/queryStatistic/purchase'
export default {
  name: 'SaleContractCollection',
  components: { steelTradeAggrid, exportBtn },
  data() {
    return {
      searchInfo: null,
      formItems: [
        {
          label: this.$t('grid.others.contractNumber'),
          value: 'sCode',
          default: '',
          type: 'elInput',
          placeholder: this.$t('grid.others.pleaseEnterTheContractNumber')
        }
      ],
      columnDefs: [
        {
          headerName: '销售合同号',
          field: 'sCode'
        },
        {
          headerName: '客户',
          field: 'sCustomerName'
        },
        {
          headerName: '发货单号',
          field: 'deliveryNo'
        },
        {
          headerName: '往来类型',
          field: 'sInteractionSubType',
          valueGetter: (params) => {
            const status = this.payTypeList.filter(item => item.sCodeValue === params.data.sInteractionSubType)
            return status.length ? status[0].sCodeName : params.data.sInteractionSubType
          }
        },
        {
          headerName: 'ERP来源单据号',
          field: 'erpUpCode'
        },
        {
          headerName: '云钢来源单据号',
          field: 'sUpCode'
        },
        {
          headerName: 'ERP配款金额',
          field: 'erpTaxAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: '云钢配款金额',
          field: 'sTaxAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: '金额差异',
          field: 'taxAmtDiff',
          cellStyle: { textAlign: 'right' },
          cellRenderer: params => {
            const text = SteelFormat.formatPrice(params.value)
            const sHtml = '<span  style=color:red>' + text + '</span>'
            return params.value !== 0 ? sHtml : text
          }
        }
      ],
      exportConfig: [

      ],
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      headerCount: null,
      footerCount: null,
      rowData: [],
      payTypeList: []
    }
  },
  mounted() {
    this.loadDict()
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      searchInfo.sCustomerIds = searchInfo.sCustomerIds?.toString()
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    loadDict() {
      DictUtil.getDict([
        'pay.subtype'
      ], res => {
        this.payTypeList = res[0].dicts
      })
    },
    loadData(pagination) {
      this.rowData = []
      return new Promise((resolve, reject) => {
        getLedgerSaleRecPage(this.searchInfo, pagination).then(res => {
          this.rowData = res.data.content.map(item => {
            item._selected = false
            item._selectedKeys = []
            return item
          })
          resolve(res.data)
        }).catch(() => {
          reject([])
        })
      })
    },
    handleFooterCount(rowData) {
      console.log(rowData)
    },
    cellValueChanged({ data }) {

    }
  }
}
</script>

<style scoped>

</style>
