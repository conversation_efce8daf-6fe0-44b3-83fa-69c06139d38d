<template>
  <steelTradeAggrid
    ref="aggrid"
    class="stellTradeAggridBor"
    row-key="sId"
    :auto-height="true"
    :row-data="rowData"
    :load-data="loadData"
    :column-defs="columnDefs"
    :paginationinif="false"
  />
</template>

<script>
import {
  DictUtil,
  SteelFormat
} from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import {
  getContractPurWarehouseList
} from '@/api/queryStatistic/purchase'

export default {
  components: {
    steelTradeAggrid
  },
  props: {
    sId: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      rowData: [],
      columnDefs: [{
        headerName: this.$t('grid.others.warehouse'),
        field: 'vWarehouseName'
      }, {
        headerName: this.$t('grid.others.logisticsSupplier'),
        field: 'vLogisticsSupplierName'
      }, {
        headerName: this.$t('grid.others.estimatedArrivalPercentage'),
        field: 'sArivRate',
        cellStyle: { textAlign: 'right' },
        valueFormatter(params) {
          return SteelFormat.toPercent(params.value, 2)
        }
      }, {
        headerName: this.$t('grid.others.virtualEntityType'),
        field: 'sWarehouseType',
        valueGetter: (params) => {
          const status = this.warehouseTypes.filter(item => item.sCodeValue === params.data.sWarehouseType)
          return status.length ? status[0].sCodeName : params.data.sWarehouseType
        }
      }, {
        headerName: this.$t('grid.others.approvalAttribution'),
        field: 'sApprovalBelong',
        valueGetter: (params) => {
          const status = this.approvas.filter(item => item.sCodeValue === params.data.sApprovalBelong)
          return status.length ? status[0].sCodeName : params.data.sApprovalBelong
        }
      }, {
        headerName: this.$t('grid.title.createdBy'),
        field: 'vCreatorName'
      }, {
        headerName: this.$t('grid.title.createdAt'),
        field: 'sCreateTime',
        minWidth: 150,
        valueGetter(params) {
          return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
        }
      }
      //  {
      //   headerName: this.$t('grid.title.modifiedBy'),
      //   field: 'vModifierName'
      // }, {
      //   headerName: this.$t('grid.title.modifiedAt'),
      //   field: 'sModifyTime',
      //   valueFormatter(params) {
      //     return Moment.time('YYYY-MM-DD HH:mm:ss', params.value)
      //   }
      // }
      ],

      warehouseTypes: [],
      approvas: []
    }
  },
  watch: {
    sId() {
      this.$refs.aggrid.loadTableData()
    }
  },
  mounted() {
    this.loadDict()
  },
  methods: {
    loadDict() {
      DictUtil.getDict(['base.warehouse.type', 'base.warehouse.belong.org.type'], res => {
        this.warehouseTypes = res[0].dicts
        this.approvas = res[1].dicts
      })
    },
    loadData() {
      return new Promise((resolve, reject) => {
        getContractPurWarehouseList({
          sContractId: this.sId
        }).then(res => {
          this.rowData = res.data
          resolve(res.data)
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.stellTradeAggridBor {
  ::v-deep .steel-trade-aggrid-header {
    border-right: 0;
    border-left: 0;
  }
  ::v-deep .ag-root-wrapper {
    border-right: 0;
    border-left: 0;
    border-bottom: 0;
  }
  ::v-deep .steel-trade-aggrid-footer {
    border: 0;
  }
}
</style>
