<template>
  <steelTradeAggrid
    ref="aggrid"
    class="stellTradeAggridBor"
    :auto-height="true"
    :column-defs="columnDefs"
    :child-column-defs="childColumnDefs"
    :row-data="rowData"
    :load-data="loadData"
    table-selection="multiple"
    row-key="sId"
    child-row-key="sId"
    children-list-key="stockReceiptDetailVos"
    :header-total="headerTotal"
    :footer-total="footerTotal"
    is-subtable
    @selectedChange="gridSelectedChange"
  />
</template>

<script>
import {
  SteelFormat,
  DictUtil
} from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
var Decimal = window.Decimal
import steelTradeAggrid from '@/components/steelTradeAggrid'
import {
  getContractPurReceiptArrivalList,
  getContractPurReceiptDetailList
} from '@/api/queryStatistic/purchase'

export default {
  components: {
    steelTradeAggrid
  },
  props: {
    sId: {
      type: String,
      default: null
    },
    sCode: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      rowData: [],
      columnDefs: [{
        headerName: this.$t('grid.others.arrivalOrderNumber'),
        field: 'sCode'
      }, {
        headerName: this.$t('grid.title.status'),
        field: 'sSheetStatus',
        valueGetter: (params) => {
          const status = this.sheetStatus.filter(item => item.sCodeValue === params.data.sSheetStatus)
          return status.length ? status[0].sCodeName : params.value
        }
      }, {
        headerName: this.$t('grid.title.quantity'),
        field: 'sSumsQtx',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatThousandthSign(params.value, 4)
        }
      }, {
        headerName: this.$t('grid.others.numberOfPiecesTag'),
        field: 'sSumsQty',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatThousandthSign(params.value)
        }
      }, {
        headerName: this.$t('grid.title.amount'),
        field: 'sSumsAmt',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatPrice(params.value)
        }
      }, {
        headerName: this.$t('grid.others.warehouse'),
        field: 'vWarehouseName'
      }, {
        headerName: this.$t('grid.title.createdBy'),
        field: 'vCreatorName'
      }, {
        headerName: this.$t('grid.title.createdAt'),
        field: 'sCreateTime',
        minWidth: 150,
        valueGetter(params) {
          return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
        }
      }],
      childColumnDefs: [{
        headerName: this.$t('grid.others.item'),
        field: 'vArtName'
      }, {
        headerName: this.$t('grid.others.steelCoilNumber'),
        field: 'sExtend4'
      }, {
        headerName: this.$t('grid.others.carriageNumber'),
        field: 'sExtend5'
      }, {
        headerName: this.$t('grid.title.quantity'),
        field: 'sQtx',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatThousandthSign(params.value, 4)
        }
      }, {
        headerName: this.$t('grid.others.numberOfPiecesTag'),
        field: 'sQty',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatThousandthSign(params.value)
        }
      }, {
        headerName: this.$t('grid.title.unitPrice'),
        field: 'unitPrice',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatPrice(params.value)
        }
      }, {
        headerName: this.$t('grid.title.amount'),
        field: 'sSumAmt',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatPrice(params.value)
        }
      }, {
        headerName: this.$t('grid.others.salesContractBillNumber'),
        field: 'sSaleContractCode',
        cellStyle: { textAlign: 'right' }
      }],

      headerTotal: [],
      footerTotal: [],
      sheetStatus: []
    }
  },
  watch: {
    sId(v) {
      this.rowData = []
      if (v) {
        this.$refs.aggrid.loadTableData()
      }
    }
  },
  mounted() {
    this.loadDict()
  },
  methods: {
    loadDict() {
      DictUtil.getDict(['dev.common.sheet.status'], res => {
        this.sheetStatus = res[0].dicts
      })
    },
    loadData() {
      return new Promise((resolve, reject) => {
        getContractPurReceiptArrivalList({
          sContractId: this.sId
        }, { sProjectCode: this.sCode }).then(res => {
          this.rowData = res.data.map(item => {
            item._selected = false
            item._selectedKeys = []
            item._details = item._details || []
            return item
          })
          resolve(res.data)
          this.headerTotal = res.data.reduce((prev, next, index) => {
            prev[1].count = +new Decimal(prev[1].count).add(next.sSumsQtx || 0)
            prev[2].count = +new Decimal(prev[2].count).add(next.sSumsQty || 0)
            prev[3].count = +new Decimal(prev[3].count).add(next.sSumsAmt || 0)
            if (index + 1 === res.data.length) {
              prev[1].count = SteelFormat.formatThousandthSign(prev[1].count, 4)
              prev[2].count = SteelFormat.formatThousandthSign(prev[2].count)
              prev[3].count = SteelFormat.formatPrice(prev[3].count)
            }
            return prev
          }, [{
            key: 'count',
            count: res.data.length,
            unit: this.$t('pagination.items')
          }, {
            title: this.$t('grid.title.quantity'),
            count: 0,
            unit: this.$t('grid.others.ton')
          }, {
            title: this.$t('grid.others.numberOfPiecesTag'),
            count: 0,
            unit: this.$t('grid.others.pieces')
          }, {
            title: this.$t('grid.title.amount'),
            count: 0,
            unit: this.$t('grid.others.yuan')
          }])
        })
      })
    },
    loadDetail(data) {
      return new Promise((resolve, reject) => {
        getContractPurReceiptDetailList({
          sContractId: data.sId,
          sId: this.sId
        }).then(res => {
          resolve(res.data)
        }).catch(() => {
          reject([])
        })
      })
    },
    gridSelectedChange() {
      this.$refs.aggrid.getSelectedData(list => {
        this.footerTotal = list.reduce((prev, next, index) => {
          prev[1].count = +new Decimal(prev[1].count).add(next.sQtx || 0)
          prev[2].count = +new Decimal(prev[2].count).add(next.sQty || 0)
          prev[3].count = +new Decimal(prev[3].count).add(next.sSumAmt || 0)
          if (index + 1 === list.length) {
            prev[1].count = SteelFormat.formatThousandthSign(prev[1].count, 4)
            prev[2].count = SteelFormat.formatThousandthSign(prev[2].count)
            prev[3].count = SteelFormat.formatPrice(prev[3].count)
          }
          return prev
        }, [{
          key: 'count',
          count: list.length,
          unit: this.$t('pagination.items')
        }, {
          title: this.$t('grid.title.quantity'),
          count: 0,
          unit: this.$t('grid.others.ton')
        }, {
          title: this.$t('grid.others.numberOfPiecesTag'),
          count: 0,
          unit: this.$t('grid.others.pieces')
        }, {
          title: this.$t('grid.title.amount'),
          count: 0,
          unit: this.$t('grid.others.yuan')
        }])
      }, 'margeChild')
    },
    // 子表合计
    handleDetailCount(list) {
      return list.reduce((prev, next, index) => {
        prev[1].count = +new Decimal(prev[1].count).add(next.sQtx || 0)
        prev[2].count = +new Decimal(prev[2].count).add(next.sQty || 0)
        prev[3].count = +new Decimal(prev[3].count).add(next.sSumAmt || 0)
        if (index + 1 === list.length) {
          prev[1].count = SteelFormat.formatThousandthSign(prev[1].count, 4)
          prev[2].count = SteelFormat.formatThousandthSign(prev[2].count)
          prev[3].count = SteelFormat.formatPrice(prev[3].count)
        }
        return prev
      }, [{
        key: 'count',
        count: list.filter(item => item._selected).length,
        unit: this.$t('pagination.items')
      }, {
        title: this.$t('grid.title.quantity'),
        count: 0,
        unit: this.$t('grid.others.ton')
      }, {
        title: this.$t('grid.others.numberOfPiecesTag'),
        count: 0,
        unit: this.$t('grid.others.pieces')
      }, {
        title: this.$t('grid.title.amount'),
        count: 0,
        unit: this.$t('grid.others.yuan')
      }])
    }
  }
}
</script>
<style lang="scss" scoped>
.stellTradeAggridBor {
  ::v-deep .steel-trade-aggrid-header {
    border-right: 0;
    border-left: 0;
  }
  ::v-deep .ag-root-wrapper {
    border-right: 0;
    border-left: 0;
  }
  ::v-deep .cnd-pagination {
    border: 0;
  }
  ::v-deep .steel-trade-aggrid-footer {
    border-right: 0;
    border-left: 0;
  }
}
</style>
