<template>
  <steelTradeAggrid
    v-if="gridShow"
    ref="aggrid"
    class="stellTradeAggridBor"
    row-key="sId"
    :auto-height="true"
    :min-height="150"
    :row-data="rowData"
    :load-data="loadData"
    :column-defs="columnDefs"
    table-selection="multiple"
    :paginationinif="false"
    @selectedChange="gridSelectedChange"
  />
</template>

<script>
import {
  SteelFormat
} from 'cnd-horizon-utils'
import { computeCellTotal } from '@/utils/common'
import { getArtnoDetail } from '@/api/contract'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import {
  getContractLogisticList
} from '@/api/queryStatistic/purchase'
export default {
  components: {
    steelTradeAggrid
  },
  props: {
    sId: {
      type: String,
      default: null
    },
    sCode: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      gridShow: true,
      rowData: [],
      columnDefs: [
        {
          headerName: this.$t('grid.others.itemNumberTag'),
          field: 'sProjectCode'
        },
        {
          headerName: this.$t('grid.others.transportContractNumber'),
          field: 'sCode'
        },
        {
          headerName: this.$t('grid.others.logisticsSupplier'),
          field: 'sSupplierName'
        },
        {
          headerName: this.$t('grid.others.transportationMode'),
          field: 'sTransportType',
          valueGetter: (params) => {
            const status = this.options.artnoList.filter(item => item.sArtnoCode === params.data.sTransportType)
            return status.length ? status[0].sCnName : params.data.sTransportType
          }
        },
        {
          headerName: this.$t('grid.others.shipName'),
          field: 'sVesselName'
        },
        {
          headerName: this.$t('grid.title.quantity'),
          field: 'sContractQty'
        },
        {
          headerName: this.$t('grid.others.freightUnitPrice'),
          field: 'sFeePrice',
          valueFormatter: (params) => {
            return SteelFormat.formatPrice(params.value)
          }
        }
        // {
        //   headerName: this.$t('grid.others.itemNumberTag'),
        //   headerClass: 'c-header_parent',
        //   children: [
        //     {
        //       headerName: '',
        //       field: 'sProjectCode',
        //       headerClass: 'c-header_child'
        //     }
        //   ]
        // },
        // {
        //   headerName: this.$t('grid.others.transportContractNumber'),
        //   headerClass: 'c-header_parent',
        //   children: [
        //     {
        //       headerName: '',
        //       field: 'sCode',
        //       headerClass: 'c-header_child'
        //     }
        //   ]
        // },
        // {
        //   headerName: this.$t('grid.others.logisticsSupplier'),
        //   headerClass: 'c-header_parent',
        //   children: [
        //     {
        //       headerName: '',
        //       field: 'sSupplierName',
        //       headerClass: 'c-header_child'
        //     }
        //   ]
        // },
        // {
        //   headerName: this.$t('grid.others.transportationMode'),
        //   headerClass: 'c-header_parent',
        //   children: [
        //     {
        //       headerName: '',
        //       field: 'sTransportType',
        //       headerClass: 'c-header_child',
        //       valueGetter: (params) => {
        //         const status = this.options.artnoList.filter(item => item.sArtnoCode === params.data.sTransportType)
        //         return status.length ? status[0].sCnName : params.data.sTransportType
        //       }
        //     }
        //   ]
        // },
        // {
        //   headerName: this.$t('grid.others.shipName'),
        //   headerClass: 'c-header_parent',
        //   children: [
        //     {
        //       headerName: '',
        //       field: 'sVesselName',
        //       headerClass: 'c-header_child'
        //     }
        //   ]
        // },
        // {
        //   headerName: this.$t('grid.title.quantity'),
        //   headerClass: 'c-header_parent',
        //   children: [
        //     {
        //       headerName: '',
        //       field: 'sContractQty',
        //       headerClass: 'c-header_child'
        //     }
        //   ]
        // },
        // {
        //   headerName: this.$t('grid.others.freightUnitPrice'),
        //   headerClass: 'c-header_parent',
        //   children: [
        //     {
        //       headerName: '',
        //       field: 'sFeePrice',
        //       headerClass: 'c-header_child',
        //       valueFormatter: (params) => {
        //         return SteelFormat.formatPrice(params.value)
        //       }
        //     }
        //   ]
        // }
        // {
        //   headerName: '应收金额',
        //   children: [
        //     {
        //       headerName: this.$t('grid.others.seaFreightUnitPrice'),
        //       field: 'sLogisticsPrice',
        //       valueFormatter: (params) => {
        //         return SteelFormat.formatPrice(params.value)
        //       }
        //     },
        //     {
        //       headerName: this.$t('grid.others.terminalUnitPrice'),
        //       field: 'sWharfPrice',
        //       valueFormatter: (params) => {
        //         return SteelFormat.formatPrice(params.value)
        //       }
        //     }, {
        //       headerName: this.$t('grid.others.portBuiltUnitPrice'),
        //       field: 'sPortPrice',
        //       valueFormatter: (params) => {
        //         return SteelFormat.formatPrice(params.value)
        //       }
        //     }, {
        //       headerName: this.$t('grid.others.landTransportUnitPrice'),
        //       field: 'sLandPrice',
        //       valueFormatter: (params) => {
        //         return SteelFormat.formatPrice(params.value)
        //       }
        //     }, {
        //       headerName: this.$t('grid.others.otherUnitPrice'),
        //       field: 'sOtherPrice',
        //       valueFormatter: (params) => {
        //         return SteelFormat.formatPrice(params.value)
        //       }
        //     }
        //   ]
        // },
        // {
        //   headerName: this.$t('grid.others.systemInsuredUnitPrice'),
        //   headerClass: 'c-header_parent',
        //   children: [
        //     {
        //       headerName: '',
        //       field: 'sSysPrice',
        //       headerClass: 'c-header_child',
        //       valueFormatter: (params) => {
        //         return SteelFormat.formatPrice(params.value)
        //       }
        //     }
        //   ]
        // },
        // {
        //   headerName: this.$t('grid.others.systemTaxDifference'),
        //   headerClass: 'c-header_parent',
        //   children: [
        //     {
        //       headerName: '',
        //       field: 'sTaxPrice',
        //       headerClass: 'c-header_child',
        //       valueFormatter: (params) => {
        //         return SteelFormat.formatPrice(params.value)
        //       }
        //     }
        //   ]
        // },
        // {
        //   headerName: this.$t('grid.others.systemLogisticsCostTotal'),
        //   headerClass: 'c-header_parent',
        //   children: [
        //     {
        //       headerName: '',
        //       field: 'vSumFeeAmt',
        //       headerClass: 'c-header_child',
        //       valueFormatter: (params) => {
        //         return SteelFormat.formatPrice(params.value)
        //       }
        //     }
        //   ]
        // }
      ],
      headerTotal: [],
      footerTotal: [],
      options: {
        artnoList: []
      }
    }
  },
  watch: {
    sId() {
      this.gridShow = false
      this.$nextTick(() => {
        this.gridShow = true
        this.$refs.aggrid.loadTableData()
      })
    }
  },
  created() {
    getArtnoDetail({ supParam: '122111111111150' }).then(res => {
      this.options.artnoList = res.data
    })
  },
  methods: {
    loadData() {
      return new Promise((resolve, reject) => {
        getContractLogisticList({
          sContractId: this.sId
        }).then(res => {
          this.rowData = res.data.map((item, index) => {
            item._selected = false
            return item
          })
          this.handleCellTotal()
          resolve(res.data.length)
        })
      })
    },
    handleCellTotal(type = 'all') {
      setTimeout(() => {
        const totalList = computeCellTotal(
          this.rowData,
          {
            sContractQty: 0
          },
          {
            sProjectCode: type === 'all' ? this.$t('grid.others.amountTo') : `${this.$t('components.selected')}${this.rowData1.filter(item => item._selected).length}${this.$t('pagination.items')}`,
            sCode: null,
            sBillCode: null,
            sMaturityDate: null,
            sFreeExpiryDate: null,
            sMonthRate: null,
            sIrtDay: null,
            sSingleRate: null,
            sFeePrice: null,
            _selected: false,
            _hiddenCheckbox: true
          },
          type
        )
        if (this.rowData.length) {
          this.$refs.aggrid.gridApi.setPinnedBottomRowData([totalList])
        }
      }, 0)
    },
    gridSelectedChange(list) {
      this.$refs.aggrid.getSelectedData(res => {
        this.handleCellTotal(res.length ? 'selected' : 'all')
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.stellTradeAggridBor {
  ::v-deep .steel-trade-aggrid-header {
    border-right: 0;
    border-left: 0;
  }
  ::v-deep .ag-root-wrapper {
    border-right: 0;
    border-left: 0;
  }
  ::v-deep .steel-trade-aggrid-footer {
    border: 0;
  }
}
</style>
