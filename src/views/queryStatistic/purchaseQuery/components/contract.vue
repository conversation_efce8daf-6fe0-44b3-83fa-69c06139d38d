<template>
  <steelTradeAggrid
    ref="aggrid"
    class="stellTradeAggridBor"
    row-key="sId"
    :auto-height="true"
    :row-data="rowData"
    :load-data="loadData"
    :column-defs="columnDefs"
    :header-total="headerTotal"
    :footer-total="footerTotal"
    table-selection="multiple"
    :paginationinif="false"
    @selectedChange="gridSelectedChange"
  />
</template>

<script>
import {
  SteelFormat
} from 'cnd-horizon-utils'
var Decimal = window.Decimal
import steelTradeAggrid from '@/components/steelTradeAggrid'
import {
  getContractPurDetail
} from '@/api/queryStatistic/purchase'

export default {
  components: {
    steelTradeAggrid
  },
  props: {
    sId: {
      type: String,
      default: null
    },
    sCode: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      rowData: [],
      columnDefs: [{
        headerName: this.$t('grid.others.itemNumberTag'),
        field: 'sProjectCode'
      }, {
        headerName: this.$t('grid.title.purchaseContractNumber'),
        field: 'sPurContractCode'
      },
      // {
      //   headerName: this.$t('grid.title.salesContractNumber'),
      //   field: 'sSaleContractCode'
      // },
      {
        headerName: this.$t('grid.others.item'),
        field: 'sGoodsName'
      }, {
        headerName: this.$t('grid.title.quantity'),
        field: 'sContractQty',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatThousandthSign(params.value, 4)
        }
      }, {
        headerName: this.$t('grid.title.unitPrice'),
        field: 'sTaxPrice',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatPrice(params.value)
        }
      }, {
        headerName: this.$t('grid.title.amount'),
        field: 'sTaxAmt',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatPrice(params.value)
        }
      }],
      headerTotal: [],
      footerTotal: []
    }
  },
  watch: {
    sId(v) {
      this.rowData = []
      if (v) {
        this.$refs.aggrid.loadTableData()
      }
    }
  },
  methods: {
    loadData() {
      return new Promise((resolve, reject) => {
        getContractPurDetail({
          sContractId: this.sId
        }, { sProjectCode: this.sCode }).then(res => {
          this.rowData = res.data.map(item => {
            item._selected = false
            return item
          })
          resolve(res.data)
          this.headerTotal = res.data.reduce((prev, next, index) => {
            prev[1].count = +new Decimal(prev[1].count).add(next.sContractQty || 0)
            prev[2].count = +new Decimal(prev[2].count).add(next.sTaxAmt || 0)
            if (index + 1 === res.data.length) {
              prev[1].count = SteelFormat.formatThousandthSign(prev[1].count, 4)
              prev[2].count = SteelFormat.formatPrice(prev[2].count)
            }
            return prev
          }, [{
            key: 'count',
            count: res.data.length,
            unit: this.$t('pagination.items')
          }, {
            title: this.$t('grid.title.quantity'),
            count: 0,
            unit: this.$t('grid.others.ton')
          }, {
            title: this.$t('grid.title.amount'),
            count: 0,
            unit: this.$t('grid.others.yuan')
          }])
        })
      })
    },
    gridSelectedChange() {
      this.$refs.aggrid.getSelectedData(list => {
        this.footerTotal = list.reduce((prev, next, index) => {
          prev[1].count = +new Decimal(prev[1].count).add(next.sContractQty || 0)
          prev[2].count = +new Decimal(prev[2].count).add(next.sTaxAmt || 0)
          if (index + 1 === list.length) {
            prev[1].count = SteelFormat.formatThousandthSign(prev[1].count, 4)
            prev[2].count = SteelFormat.formatPrice(prev[2].count)
          }
          return prev
        }, [{
          key: 'count',
          count: list.length,
          unit: this.$t('pagination.items')
        }, {
          title: this.$t('grid.title.quantity'),
          count: 0,
          unit: this.$t('grid.others.ton')
        }, {
          title: this.$t('grid.title.amount'),
          count: 0,
          unit: this.$t('grid.others.yuan')
        }])
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.stellTradeAggridBor {
  ::v-deep .steel-trade-aggrid-header {
    border-right: 0;
    border-left: 0;
  }
  ::v-deep .ag-root-wrapper {
    border-right: 0;
    border-left: 0;
  }
  ::v-deep .steel-trade-aggrid-footer {
    border: 0;
  }
}
</style>
