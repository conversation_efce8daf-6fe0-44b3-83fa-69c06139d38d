<template>
  <steelTradeAggrid
    ref="aggrid"
    class="stellTradeAggridBor"
    :auto-height="true"
    :column-defs="columnDefs"
    :child-column-defs="childColumnDefs"
    :row-data="rowData"
    :load-data="loadData"
    :load-detail="loadDetail"
    table-selection="multiple"
    row-key="sId"
    child-row-key="sId"
    children-list-key="detailVoList"
    :header-total="headerTotal"
    :footer-total="footerTotal"
    is-subtable
    @selectedChange="gridSelectedChange"
  />
</template>

<script>
import {
  SteelFormat,
  DictUtil
} from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
var Decimal = window.Decimal
import steelTradeAggrid from '@/components/steelTradeAggrid'
import {
  conThirdDeliveryList,
  conThirdDeliveryListDetail
} from '@/api/queryStatistic/purchase'

export default {
  components: {
    steelTradeAggrid
  },
  props: {
    sId: {
      type: String,
      default: null
    },
    sCode: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      rowData: [],
      columnDefs: [{
        headerName: this.$t('grid.others.steelFactoryDirectRrNumberKey'),
        field: 'sCode'
      }, {
        headerName: this.$t('grid.title.status'),
        field: 'sSheetStatus',
        valueGetter: (params) => {
          const status = this.sheetStatus.filter(item => item.sCodeValue === params.data.sSheetStatus)
          return status.length ? status[0].sCodeName : params.value
        }
      }, {
        headerName: this.$t('grid.others.quantityNumberOfPieces'),
        field: 'vSumQty',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          if (params.data.sIsWeightPiece === '1') {
            return `${SteelFormat.formatThousandthSign(params.data.sUsedMaxOutQty, 4)}/${SteelFormat.formatThousandthSign(params.data.sUsedMaxOutQtx)}`
          } else {
            return `${SteelFormat.formatThousandthSign(params.data.vSumQty, 4)}/${SteelFormat.formatThousandthSign(params.data.vSumPkgQty)}`
          }
        }
      },
      {
        headerName: this.$t('grid.others.remainingQuantityRemainingPieces'),
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          if (params.data.sIsWeightPiece === '1') {
            return `${SteelFormat.formatThousandthSign(params.data.sLeftMaxOutQty, 4)}/-`
          } else {
            return `${SteelFormat.formatThousandthSign(params.data.vSumFallbackQtx, 4)}/${SteelFormat.formatThousandthSign(params.data.vSumFallbackQty)}`
          }
        }
      },
      {
        headerName: '是否件重',
        field: 'sIsWeightPiece',
        valueGetter: (params) => {
          const status = this.yesorno.filter(item => item.sCodeValue === params.data.sIsWeightPiece)
          return status.length ? status[0].sCodeName : params.data.sIsWeightPiece
        }
      },
      {
        headerName: this.$t('grid.title.createdAt'),
        field: 'sCreateTime',
        minWidth: 150,
        valueFormatter(params) {
          return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
        }
      },
      {
        headerName: this.$t('grid.title.createdBy'),
        field: 'vCreatorName'
      }],
      childColumnDefs: [{
        headerName: this.$t('grid.others.item'),
        field: 'vArtName'
      },
      {
        headerName: this.$t('grid.others.steelCoilNumber'),
        field: 'sExtend4'
      },
      {
        headerName: '件重',
        field: 'sExtend7',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatThousandthSign(params.data.sExtend7, 4)
        }
      },
      {
        headerName: this.$t('grid.others.quantityNumberOfPieces'),
        field: 'sContractQty',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          if (params.data._sIsWeightPiece === '1') {
            return `${SteelFormat.formatThousandthSign(params.data.vSumRightQtx, 4)}/${SteelFormat.formatThousandthSign(params.data.vSumRightQty)}`
          } else {
            return `${SteelFormat.formatThousandthSign(params.data.sContractQty, 4)}/${SteelFormat.formatThousandthSign(params.data.sQty)}`
          }
        }
      },
      {
        headerName: this.$t('grid.others.remainingQuantityRemainingPieces'),
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          if (params.data._sIsWeightPiece === '1') {
            return `-/-`
          } else {
            return `${SteelFormat.formatThousandthSign(params.data.vSumFallbackQtx, 4)}/${SteelFormat.formatThousandthSign(params.data.vSumFallbackQty)}`
          }
        }
      }, {
        headerName: this.$t('grid.others.carriageNumber'),
        field: 'sExtend5'
      }, {
        headerName: this.$t('grid.title.salesContractNumber'),
        field: 'sSaleContractCode'
      }
      ],

      headerTotal: [],
      footerTotal: [],
      sheetStatus: [],
      yesorno: []
    }
  },
  watch: {
    sId() {
      this.$refs.aggrid.loadTableData()
    }
  },
  mounted() {
    this.loadDict()
  },
  methods: {
    loadDict() {
      DictUtil.getDict(['dev.common.sheet.status', 'base.yes-no'], res => {
        this.sheetStatus = res[0].dicts
        this.yesorno = res[1].dicts
      })
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        conThirdDeliveryList({
          sPurContractId: this.sId,
          ...pagination
        }, { sProjectCode: this.sCode }).then(res => {
          this.rowData = res.data.page.content.map(item => {
            item._selected = false
            item._selectedKeys = []
            item.detailVoList = []
            return item
          })
          resolve(res.data.page)
          const { sumQty, sumQtx, sumLeftQty, sumLeftQtx } = res.data
          this.headerTotal = [
            { count: res.data.page.totalElements, key: 'count' },
            { title: this.$t('grid.title.quantity'), count: SteelFormat.formatThousandthSign(sumQty, 4), unit: this.$t('grid.others.ton') },
            { title: this.$t('grid.others.numberOfPiecesTag'), count: SteelFormat.formatThousandthSign(sumQtx), unit: this.$t('grid.others.pieces') },
            { title: '剩余数量', count: SteelFormat.formatThousandthSign(sumLeftQty, 4), unit: this.$t('grid.others.ton') },
            { title: '剩余件数', count: SteelFormat.formatThousandthSign(sumLeftQtx), unit: this.$t('grid.others.pieces') }
          ]
        })
      })
    },
    loadDetail(data) {
      return new Promise((resolve, reject) => {
        conThirdDeliveryListDetail({
          sThirdDeliveryId: data.sId
        }, { sProjectCode: this.sCode, sId: this.sId }).then(res => {
          const result = res.data.map(item => {
            item._sIsWeightPiece = data.sIsWeightPiece
            return item
          })
          resolve(result)
        }).catch(() => {
          reject([])
        })
      })
    },
    gridSelectedChange() {
      this.$refs.aggrid.getSelectedData(list => {
        this.footerTotal = list.reduce((prev, next, index) => {
          if (next._sIsWeightPiece === '1') {
            prev[1].count = +new Decimal(prev[1].count).add(next.vSumRightQtx || 0)
            prev[2].count = +new Decimal(prev[2].count).add(next.vSumRightQty || 0)
            prev[3].count = +new Decimal(prev[3].count).add(0)
            prev[4].count = +new Decimal(prev[4].count).add(0)
          } else {
            prev[1].count = +new Decimal(prev[1].count).add(next.sContractQty || 0)
            prev[2].count = +new Decimal(prev[2].count).add(next.sQty || 0)
            prev[3].count = +new Decimal(prev[3].count).add(next.vSumFallbackQtx || 0)
            prev[4].count = +new Decimal(prev[4].count).add(next.vSumFallbackQty || 0)
          }
          if (index + 1 === list.length) {
            prev[1].count = SteelFormat.formatThousandthSign(prev[1].count, 4)
            prev[2].count = SteelFormat.formatThousandthSign(prev[2].count)
            prev[3].count = SteelFormat.formatThousandthSign(prev[3].count, 4)
            prev[4].count = SteelFormat.formatThousandthSign(prev[4].count)
          }
          return prev
        }, [{
          key: 'count',
          count: list.length,
          unit: this.$t('pagination.items')
        }, {
          title: this.$t('grid.title.quantity'),
          count: 0,
          unit: this.$t('grid.others.ton')
        }, {
          title: this.$t('grid.others.numberOfPiecesTag'),
          count: 0,
          unit: this.$t('grid.others.pieces')
        }, {
          title: '剩余数量',
          count: 0,
          unit: this.$t('grid.others.ton')
        }, {
          title: '剩余件数',
          count: 0,
          unit: this.$t('grid.others.pieces')
        }])
      }, 'margeChild')
    },
    // 子表合计
    handleDetailCount(list) {
      return list.reduce((prev, next, index) => {
        prev[1].count = +new Decimal(prev[1].count).add(next.sQtx || 0)
        prev[2].count = +new Decimal(prev[2].count).add(next.sQty || 0)
        prev[3].count = +new Decimal(prev[3].count).add(next.sSumAmt || 0)
        if (index + 1 === list.length) {
          prev[1].count = SteelFormat.formatThousandthSign(prev[1].count, 4)
          prev[2].count = SteelFormat.formatThousandthSign(prev[2].count)
          prev[3].count = SteelFormat.formatPrice(prev[3].count)
        }
        return prev
      }, [{
        key: 'count',
        count: list.filter(item => item._selected).length,
        unit: this.$t('pagination.items')
      }, {
        title: this.$t('grid.title.quantity'),
        count: 0,
        unit: this.$t('grid.others.ton')
      }, {
        title: this.$t('grid.others.numberOfPiecesTag'),
        count: 0,
        unit: this.$t('grid.others.pieces')
      }, {
        title: this.$t('grid.title.amount'),
        count: 0,
        unit: this.$t('grid.others.yuan')
      }])
    }
  }
}
</script>
<style lang="scss" scoped>
.stellTradeAggridBor {
  ::v-deep .steel-trade-aggrid-header {
    border-right: 0;
    border-left: 0;
  }
  ::v-deep .ag-root-wrapper {
    border-right: 0;
    border-left: 0;
  }
  ::v-deep .cnd-pagination {
    border: 0;
  }
  ::v-deep .steel-trade-aggrid-footer {
    border-right: 0;
    border-left: 0;
  }
}
</style>
