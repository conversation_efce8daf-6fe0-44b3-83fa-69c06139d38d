<template>
  <div class="page-container">
    <p class="page-title">{{ $t('grid.others.procurementContractProcess') }}</p>
    <div class="layout-content auto-page-title">
      <steel-search-form
        ref="searchForm"
        :form-items="formItems"
        :auto-collapse-on-search="false"
        :active-panel="formActivePanel"
        @search="onSearch"
      />
      <!-- 采购合同进程 -->
      <div class="btn-group mt-10">
        <div class="text">
          {{ $t('grid.others.procurementContractProcess') }}{{ $t('grid.others.list') }}
        </div>
        <div>
          <el-button
            type="primary"
            size="mini"
            @click="processQuery"
          >相关销售进程查询</el-button>
          <export-btn
            class="ml-10"
            :file-name="$t('grid.others.procurementContractProcess')"
            api-url="/esc/contract/pur/process/excel/export"
            :post-params="searchInfo"
            :export-config="exportConfig"
            :get-grid-api="getGridApi"
            :children-post-params="childrenPostParams"
            children-api-url="/esc/contract/pur/process/detail/excel/export"
            :children-file-name="childrenFileName"
            :children-selected-data="selectedData"
            :need-selected-data="true"
          />
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :heightinif="300"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        table-selection="multiple"
        row-key="sId"
        @pageChange="pageChange"
        @rowClicked="handleChangeContract"
        @selectedChange="handleChange"
      />
      <el-tabs
        v-model="activeName"
        style="height:auto"
        class="mt-10"
      >
        <el-tab-pane
          v-for="tab in tabList"
          :key="tab.name"
          :label="tab.label"
          :name="tab.name"
        >
          <component
            :is="tab.name"
            v-if="activeName === tab.name"
            :ref="tab.name"
            :s-id="selectContractId"
            :s-code="selectProjectCode"
          />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import {
  SteelFormat,
  DictUtil
} from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
var Decimal = window.Decimal
import {
  getContractPurProcessList
} from '@/api/queryStatistic/purchase'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import contract from './components/contract'
import intransit from './components/intransit'
import arrival from './components/arrival'
import payment from './components/payment'
import invoice from './components/invoice'
import cost from './components/cost'
import recordWarehouse from './components/recordWarehouse'
import stockAdjust from './components/stockAdjust'
import carriageContract from './components/carriageContract'
import steelPlant from './components/steelPlant'
import exportBtn from '@/components/exportBtnV2'
import { handleDict, getStringLength } from '@/utils/common'
import { statusDict3 } from '@/utils/dict'
export default {
  name: 'PurchaseQuerys',
  components: {
    steelTradeAggrid,
    contract,
    intransit,
    arrival,
    payment,
    invoice,
    cost,
    recordWarehouse,
    stockAdjust,
    carriageContract,
    exportBtn,
    steelPlant
  },
  data() {
    return {
      formActivePanel: '1',
      activeName: 'contract',
      tabList: [{
        label: this.$t('grid.others.contractDetails'),
        name: 'contract'
      }, {
        label: this.$t('grid.others.inTransitDetails'),
        name: 'intransit'
      }, {
        label: this.$t('grid.tabs.arrivalDetails'),
        name: 'arrival'
      }, {
        label: '钢厂直放明细',
        name: 'steelPlant'
      }, {
        label: this.$t('grid.tabs.paymentDetails'),
        name: 'payment'
      }, {
        label: this.$t('grid.tabs.invoiceDetails'),
        name: 'invoice'
      }, {
        label: this.$t('grid.others.expenseDetails'),
        name: 'cost'
      }, {
        label: this.$t('grid.others.transportationContract'),
        name: 'carriageContract'
      }, {
        label: this.$t('grid.others.recordWarehouse'),
        name: 'recordWarehouse'
      }, {
        label: this.$t('grid.others.inventoryAdjustment'),
        name: 'stockAdjust'
      }],
      formItems: [{
        label: this.$t('grid.title.purchaseContractNumber'),
        value: 'sCode',
        type: 'elInput',
        placeholder: this.$t('grid.others.enterPurchaseContractNumber')
      }, {
        label: this.$t('grid.others.supplier'), // 供应商,
        value: 'sSupplierId',
        type: 'cndInputDialogItem',
        dialogType: 'customer',
        defaultUrl: '/esc/customer/page',
        option: { valueKey: 'sPath' },
        placeholder: this.$t('grid.others.pleaseSelectCompany')
      }, {
        label: this.$t('grid.others.itemNumberTag'),
        value: 'sProjectCode',
        type: 'elInput',
        placeholder: this.$t('grid.others.pleaseEnterTheItemNumberTag')
      }, {
        label: this.$t('grid.title.company'),
        value: 'sCompanyId',
        type: 'cndInputDialog',
        dialogType: 'company',
        placeholder: this.$t('grid.others.pleaseSelectCompany')
      }, {
        label: this.$t('grid.title.accountingGroup'),
        value: 'sCheckGroupId',
        type: 'cndInputDialog',
        dialogType: 'cost'
      }, {
        label: this.$t('grid.title.personnel'),
        value: 'sStaffId',
        type: 'cndInputDialogItem',
        option: { valueKey: 'sPath' },
        dialogType: 'staff'
      },
      {
        label: this.$t('grid.title.status'),
        value: 'sSheetStatus',
        type: 'elSelect',
        multiple: true,
        firstCall: true,
        dict: statusDict3
      },
      {
        label: this.$t('grid.title.createdBy'),
        value: 'sCreator',
        type: 'cndInputDialog',
        dialogType: 'creater'
      }, {
        label: this.$t('grid.title.createdAt'),
        value: ['sCreateTime', 'vCreateTimeTo'],
        default: ['', ''],
        placeholder: [this.$t('grid.others.pleaseSelectTheDate'), this.$t('grid.others.pleaseSelectTheDate')],
        type: 'elDatePicker',
        unlinkPanels: true,
        itemType: 'occultation'
      }, {
        label: this.$t('grid.others.executionTime'),
        value: ['sRatifyDate', 'sRatifyDateTo'],
        placeholder: [this.$t('grid.others.pleaseSelectTheDate'), this.$t('grid.others.pleaseSelectTheDate')],
        type: 'elDatePicker',
        unlinkPanels: true,
        itemType: 'occultation'
      }, {
        label: '经营单位',
        value: 'sManagementId',
        type: 'cndInputDialog',
        dialogType: 'escOrg',
        itemType: 'occultation'
      }],
      columnDefs: [
        {
          headerName: this.$t('grid.others.itemNumberTag'),
          field: 'sProjectCode',
          pinned: 'left'
        },
        {
          headerName: this.$t('grid.title.purchaseContractNumber'),
          field: 'sCode',
          pinned: 'left'
        },
        {
          headerName: this.$t('grid.others.supplier'), // 供应商,
          field: 'vSupplierName'
        },
        {
          headerName: this.$t('grid.title.status'),
          field: 'sSheetStatus',
          valueGetter: (params) => {
            const status = this.sheetStatus.filter(item => item.sCodeValue === params.data.sSheetStatus)
            return status.length ? status[0].sCodeName : params.value
          }
        },
        {
          headerName: this.$t('grid.others.contractQuantity'),
          field: 'vSumQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: this.$t('grid.title.contractAmount'),
          field: 'sOriginalAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: this.$t('grid.others.dateOfFirstPayment'),
          field: 'sRecDate',
          valueFormatter: params => {
            return Moment.time('YYYY-MM-DD', params.value)
          }
        },
        {
          headerName: this.$t('grid.others.paymentAmount'),
          field: 'vPayAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: this.$t('grid.others.inTransitQuantity'),
          field: 'vTransitQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: this.$t('grid.others.arrivalQuantity'),
          field: 'vStockQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: this.$t('grid.others.invoiceQuantity'),
          field: 'vInvoiceQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: this.$t('grid.others.invoiceAmount'),
          field: 'vInvoiceAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: this.$t('grid.title.company'),
          field: 'vCompanyName'
        },
        {
          headerName: this.$t('grid.title.accountingGroup'),
          field: 'vCheckGroupName'
        },
        {
          field: 'vDepartmentName',
          headerName: this.$t('grid.others.department')
        },
        {
          headerName: this.$t('grid.title.personnel'),
          field: 'vStaffName'
        },
        {
          headerName: this.$t('grid.title.createdBy'),
          field: 'vCreatorName'
        },
        {
          headerName: this.$t('grid.title.createdAt'),
          field: 'sCreateTime',
          minWidth: 150,
          valueGetter(params) {
            return params.data._hiddenCheckbox ? '' : Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
          }
        },
        {
          headerName: this.$t('grid.others.executionTime'),
          field: 'sRatifyDate',
          valueGetter(params) {
            return params.data._hiddenCheckbox ? '' : Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sRatifyDate)
          }
        },
        {
          field: 'vManagementName',
          headerName: '经营单位'
        }
      ],
      rowData: [],
      sheetStatus: [],
      sCode: '',
      selectContractId: null,
      selectProjectCode: null,
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      searchInfo: null,
      childrenPostParams: {},
      childrenFileName: null,
      exportConfig: [
        { label: this.$t('grid.others.itemNumberTag'), value: 'sProjectCode' },
        { label: this.$t('grid.title.purchaseContractNumber'), value: 'sCode' },
        { label: this.$t('grid.others.supplier'), value: 'vSupplierName' },
        { label: this.$t('grid.title.status'), value: 'sSheetStatus',
          setValue: value => {
            return handleDict(value, this.sheetStatus)
          }
        },
        { label: this.$t('grid.others.contractQuantity'), value: 'vSumQty',
          setValue: (value) => { return Number((+value).toFixed(4)) }
        },
        { label: this.$t('grid.title.contractAmount'), value: 'sOriginalAmt',
          setValue: (value) => { return Number((+value).toFixed(2)) }
        },
        { label: this.$t('grid.others.dateOfFirstPayment'), value: 'sRecDate',
          setValue: value => { return Moment.time('YYYY-MM-DD', value) }
        },
        { label: this.$t('grid.others.paymentAmount'), value: 'vPayAmt',
          setValue: (value) => { return Number((+value).toFixed(2)) }
        },
        { label: this.$t('grid.others.inTransitQuantity'), value: 'vTransitQty',
          setValue: (value) => { return Number((+value).toFixed(4)) }
        },
        { label: this.$t('grid.others.arrivalQuantity'), value: 'vStockQty',
          setValue: (value) => { return Number((+value).toFixed(4)) }
        },
        { label: this.$t('grid.others.invoiceQuantity'), value: 'vInvoiceQty',
          setValue: (value) => { return Number((+value).toFixed(4)) }
        },
        { label: this.$t('grid.others.invoiceAmount'), value: 'vInvoiceAmt',
          setValue: (value) => { return Number((+value).toFixed(4)) }
        },
        { label: this.$t('grid.title.company'), value: 'vCompanyName' },
        { label: this.$t('grid.title.accountingGroup'), value: 'vCheckGroupName' },
        { value: 'vDepartmentName', label: this.$t('grid.others.department') },
        { label: this.$t('grid.title.personnel'), value: 'vStaffName' },
        { label: this.$t('grid.title.createdBy'), value: 'vCreatorName' },
        { label: this.$t('grid.title.createdAt'), value: 'sCreateTime',
          setValue: value => { return Moment.time('YYYY-MM-DD HH:mm:ss', value) }
        },
        { label: this.$t('grid.others.executionTime'), value: 'sRatifyDate',
          setValue: value => { return Moment.time('YYYY-MM-DD HH:mm:ss', value) }
        },
        { label: '经营单位', value: 'vManagementName' }
      ],
      selectedData: []
    }
  },
  created() {
    if (Object.keys(this.$route.query).length) {
      this.sCode = this.$route.query.sCode || ''
      this.formItems[0].default = this.$route.query.sCode || ''
      this.formItems[2].default = this.$route.query.sProjectCode || ''
      this.formItems[9].default = [undefined, undefined]
      this.$nextTick(() => {
        this.$refs.searchForm.submit()
      })
    } else {
      this.formItems[0].default = ''
      this.formItems[2].default = ''
    }
  },
  mounted() {
    this.loadDict()
    if (!this.$route.query.sCode) {
      this.formItems[0].default = ''
    }
    if (!this.$route.query.sProjectCode) {
      this.formItems[2].default = ''
    }
    if (this.$route.query.sCode || this.$route.query.sProjectCode) {
      this.onSearch()
    } else {
      this.onSearch(false)
    }
  },
  methods: {
    pageChange() {
      this.selectContractId = null
      this.selectProjectCode = null
    },
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      searchInfo.sSheetStatus = searchInfo.sSheetStatus?.toString()
      this.searchInfo = searchInfo
      if (getStringLength(searchInfo.sCode.trim()) < 4) {
        this.$message.warning('合同号查询条件至少需要输入4个字节')
        this.formActivePanel = '1'
        return
      }
      if (load) {
        this.selectContractId = null
        this.selectProjectCode = null
        this.$refs.aggrid.loadTableData()
      }
    },
    loadDict() {
      DictUtil.getDict(['dev.common.sheet.status'], res => {
        this.sheetStatus = res[0].dicts
      })
    },
    loadData(pagination = { page: 0, limit: 30 }) {
      this.formActivePanel = ''
      const _this = this
      return new Promise((resolve, reject) => {
        getContractPurProcessList(
          this.searchInfo,
          pagination
        ).then(res => {
          this.activeName = 'contract'
          this.rowData = res.data.datas.content.map((item, index) => {
            if (!this.selectContractId) {
              item._selected = index === 0
            } else {
              item._selected = item.sId === this.selectContractId
            }
            return item
          })
          if (res.data.datas.content.length === 0) {
            this.selectContractId = null
            this.selectProjectCode = null
          } else {
            _this.handleChangeContract({ data: res.data.datas.content[0] })
          }
          setTimeout(() => {
            this.$refs.aggrid.gridApi.setPinnedBottomRowData([this.computeTotal()])
          }, 0)
          resolve(res.data.datas)
        }).catch(() => {
          reject(0)
        })
      })
    },
    handleChange() {
      this.$refs.aggrid.getSelectedData(res => {
        this.selectedData = res
        setTimeout(() => {
          this.$refs.aggrid.gridApi.setPinnedBottomRowData([this.computeTotal(res.length ? 'selected' : 'all')])
        }, 0)
      })
    },
    computeTotal(type = 'all') {
      return Object.assign(this.rowData.reduce((prev, next) => {
        Object.keys(prev).forEach(key => {
          if (type === 'all') {
            prev[key] = new Decimal(+prev[key]).add(+next[key] || 0)
          } else {
            if (next._selected) {
              prev[key] = new Decimal(+prev[key]).add(+next[key] || 0)
            }
          }
        })
        return prev
      }, {
        vSumQty: 0,
        sOriginalAmt: 0,
        vPayAmt: 0,
        vTransitQty: 0,
        vStockQty: 0,
        vInvoiceQty: 0,
        vInvoiceAmt: 0
      }), {
        sProjectCode: type === 'all' ? this.$t('grid.others.amountTo') : `${this.$t('components.selected')}${this.rowData.filter(item => item._selected).length}${this.$t('pagination.items')}`,
        sCreateTime: null,
        sRatifyDate: null,
        sRecDate: null,
        _selected: false,
        _hiddenCheckbox: true
      })
    },
    handleChangeContract(params) {
      if (!params.data._hiddenCheckbox) {
        this.selectContractId = params.data.sId
        this.selectProjectCode = params.data.sProjectCode
        this.childrenFileName = params.data.sCode + '子表明细'
        this.childrenPostParams = {
          sId: params.data.sId
        }
      }
    },
    processQuery() {
      this.$refs.aggrid.getSelectedData(res => {
        if (res.length === 1) {
          this.$router.push({
            path: '/SaleQuery',
            query: {
              sProjectCode: res[0].sProjectCode,
              activeId: localStorage.getItem('menuId')
            }
          })
        } else {
          this.$message.warning(this.$t('grid.others.pleaseSelectAData'))
        }
      })
    }
  }
}
</script>
