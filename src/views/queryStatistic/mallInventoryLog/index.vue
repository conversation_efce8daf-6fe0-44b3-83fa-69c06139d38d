<template>
  <div class="page-container">
    <p class="page-title">商城库存日志</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form ref="searchForm" :form-items="formItems" @search="onSearch" />
      <div class="btn-group mt-10">
        <div class="text">
          商城库存日志
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :header-total="headerCount"
        :footer-total="footerCount"
        :auto-load-data="false"
        table-selection="multiple"
        @selectedChange="selectedChange"
        @cellValueChanged="cellValueChanged"
      />
    </div>
  </div>
</template>

<script>
import { Moment } from 'cnd-utils'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import { SteelFormat } from 'cnd-horizon-utils'
import {
  inventoryLogsPage
} from '@/api/logistics/saleDelivery/retailMall'
import { getDictList, getCnDitc } from '@/utils/dict'

export default {
  name: 'MallInventoryLog',
  components: { steelTradeAggrid },
  data() {
    return {
      searchInfo: null,
      formItems: [
        {
          label: this.$t('grid.title.purchaseContractNumber'),
          value: 'sPurContractCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.others.supplier'),
          value: 'sSupplierId',
          type: 'cndInputDialogItem',
          dialogType: 'customer',
          defaultUrl: '/esc/customer/page',
          option: { valueKey: 'sPath' }
        },
        {
          label: this.$t('grid.others.warehouse'),
          value: 'sWarehouseId',
          type: 'cndInputDialogItem',
          defaultUrl: '/esc/warehouse/info/dialog/v2/page-dialog',
          option: { valueKey: 'sPath' }
        },
        {
          label: '上架单号',
          value: 'sInventoryCode',
          type: 'elInput'
        },
        {
          label: '原始单据号',
          value: 'sStockReceiptCode',
          type: 'elInput'
        },

        {
          label: '品名',
          value: 'sArtName',
          type: 'elInput'
        },
        {
          label: this.$t('grid.others.carriageNumber'),
          value: 'sVesselNo',
          type: 'elInput'
        },
        {
          label: this.$t('grid.others.steelCoilNumber'),
          value: 'sSteelNo',
          type: 'elInput'
        },
        {
          label: this.$t('grid.title.company'),
          value: 'sCompanyId',
          type: 'cndInputDialog',
          dialogType: 'company'
        },
        {
          label: '经营单位',
          value: 'sManagementId',
          type: 'cndInputDialog',
          dialogType: 'escOrg'
        },
        {
          label: this.$t('grid.title.createdBy'),
          value: 'sCreator',
          type: 'cndInputDialog',
          placeholder: this.$t('grid.others.pleaseSelectTheCreator'),
          dialogType: 'applicant'
        },
        {
          label: this.$t('grid.title.createdAt'),
          value: ['vCreateTimeStart', 'vCreateTimeEnd'],
          placeholder: [this.$t('grid.others.pleaseSelectTheDate'), this.$t('grid.others.pleaseSelectTheDate')],
          unlinkPanels: true,
          type: 'elDatePicker'
        },
        {
          label: this.$t('grid.others.itemNumberTag'),
          value: 'sProjectCode',
          type: 'elInput'
        },
        // {
        //   label: this.$t('grid.others.accountingFilter'),
        //   value: 'vIsCor',
        //   type: 'elSelect',
        //   itemType: 'occultation',
        //   dict: 'dev.common.verify.finish.type',
        //   placeholder: this.$t('grid.others.pleaseSelectTheUnderwritingFilter')
        // },
        {
          label: this.$t('grid.title.accountingGroup'),
          value: 'sCheckGroupId',
          type: 'cndInputDialog',
          dialogType: 'cost'
        },
        {
          label: this.$t('grid.others.department'),
          value: 'sDepartmentId',
          type: 'cndInputDialog',
          dialogType: 'depart',
          placeholder: this.$t('grid.others.pleaseSelectTheDepartment')
        }
      ],
      columnDefs: [
        {
          headerName: this.$t('grid.title.createdBy'),
          field: 'vCreatorName'
        },
        {
          headerName: this.$t('grid.title.createdAt'),
          field: 'sCreateTime',
          cellStyle: { textAlign: 'right' },
          minWidth: 150,
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
          }
        },
        {
          headerName: '操作类型',
          field: 'sOperationType',
          valueGetter: (params) => {
            return getCnDitc(params, this.options['esc.inventory.log.type'], 'sOperationType')
          }
        },
        {
          headerName: this.$t('grid.others.supplier'),
          field: 'vSupplierName'
        },
        {
          headerName: this.$t('grid.others.warehouse'),
          field: 'vWarehouseName'
        },
        {
          headerName: this.$t('grid.others.item'),
          field: 'sArtName'
        },
        {
          headerName: this.$t('grid.others.steelCoilNumber'),
          field: 'sSteelNo'
        },
        {
          headerName: this.$t('grid.others.carriageNumber'),
          field: 'sVesselNo'
        },
        {
          headerName: '操作数量/件数',
          field: 'sOperateQtx',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return `${SteelFormat.formatThousandthSign(params.data.sOperateQtx, 4)}／${SteelFormat.formatThousandthSign(params.data.sOperateQty, 0)}`
          }
        },
        {
          headerName: '上架数量/件数',
          field: 'sUpQtx',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return `${SteelFormat.formatThousandthSign(params.data.sUpQtx, 4)}／${SteelFormat.formatThousandthSign(params.data.sUpQty, 0)}`
          }
        },
        {
          headerName: '单位',
          field: 'vQtyUnitName'
        },
        {
          headerName: '冻结数量/件数',
          field: 'sFrozenQtx',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return `${SteelFormat.formatThousandthSign(params.data.sFrozenQtx, 4)}／${SteelFormat.formatThousandthSign(params.data.sFrozenQty, 0)}`
          }
        },
        {
          headerName: '出仓数量/件数',
          field: 'sDeliveryQtx',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return `${SteelFormat.formatThousandthSign(params.data.sDeliveryQtx, 4)}／${SteelFormat.formatThousandthSign(params.data.sDeliveryQty, 0)}`
          }
        },
        {
          headerName: '剩余数量/件数',
          field: 'sLeftQtx',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return `${SteelFormat.formatThousandthSign(params.data.sLeftQtx, 4)}／${SteelFormat.formatThousandthSign(params.data.sLeftQty, 0)}`
          }
        },
        {
          headerName: '单价',
          field: 'sPrice',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        }, {
          headerName: '上架时间',
          field: 'sUpTime',
          valueFormatter(params) {
            return params.data.sUpTime ? Moment.time('YYYY-MM-DD', params.data.sUpTime) : ''
          }
        },
        {
          headerName: '下架时间',
          field: 'sTakeoffShelfTime',
          valueFormatter(params) {
            return params.data.sTakeoffShelfTime ? Moment.time('YYYY-MM-DD', params.data.sTakeoffShelfTime) : ''
          }
        },
        {
          headerName: '业务联系人',
          field: 'sContactName'
        },
        {
          headerName: '业务联系方式',
          field: 'sContactMobile'
        },
        {
          headerName: this.$t('grid.others.itemNumberTag'),
          field: 'sProjectCode'
        },
        {
          headerName: '原始单据号',
          field: 'sStockReceiptCode'
        },
        {
          headerName: this.$t('grid.title.purchaseContractNumber'),
          field: 'sPurContractCode'
        },
        {
          headerName: this.$t('grid.title.company'),
          field: 'vCompanyName'
        },
        {
          headerName: '上架单号',
          field: 'sInventoryCode'
        },
        {
          headerName: '经营单位',
          field: 'vManagementName'
        },
        {
          headerName: this.$t('grid.title.accountingGroup'),
          field: 'vCheckGroupName'
        },
        {
          headerName: this.$t('grid.others.department'),
          field: 'vDepartmentName'
        }
      ],
      headerCount: null,
      footerCount: null,
      rowData: [],
      options: {
        'esc.inventory.log.type': []
      }
    }
  },
  mounted() {
    getDictList(this.options)
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        inventoryLogsPage(this.searchInfo, pagination).then(res => {
          this.rowData = res.data.content.map(item => {
            item._selected = false
            item._selectedKeys = []
            return item
          })
          resolve(res.data)
        }).catch(() => {
          reject([])
        })
      })
    },
    selectedChange(rowData) {
      console.log(rowData)
    }
  }
}
</script>

<style scoped>

</style>
