<template>
  <div class="page-container">
    <p class="page-title">中拓出仓回单查询</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form ref="searchForm" :form-items="formItems" @search="onSearch" />
      <div class="btn-group mt-10">
        <div class="text">
          中拓出仓回单列表
        </div>
        <div>
          <!-- <export-btn
            file-name=""
            api-url="/esc/steel/factory/contract/export"
            :post-params="searchInfo"
            :export-config="exportConfig"
            :get-grid-api="getGridApi"
            :isload-detail="true"
            :detail-params="detailParams"
          /> -->
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        :show-header-select="false"
        table-selection="multiple"
        :header-total="headerCount"
        :footer-total="footerCount"
        @rowClicked="rowClicked"
        @selectedChange="handleFooterCount"
        @rowDoubleClicked="onRowDoubleClicked"
      />
      <div class="btn-group mt-10">
        <div class="text">
          中拓出仓回单明细
        </div>
      </div>
      <steelTradeAggrid
        ref="detailAggrid"
        :load-data="loadDetail"
        :column-defs="childColumnDefs"
        :row-data="rowDataChild"
        :header-total="childHeaderCount"
        :footer-total="childFooterCount"
        :auto-load-data="false"
        table-selection="multiple"
        @selectedChange="handleChildFooterCount"
      />
    </div>
  </div>
</template>

<script>
var Decimal = window.Decimal
import { SteelFormat } from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
// import { handleDict } from '@/utils/common'
import {
  getDictet,
  getCnDitc
} from '@/api/logistics/saleDelivery/saleorder'
import { extbalanceOutreceiptPageAll, extbalanceOutreceiptPage } from '@/api/queryStatistic/purchase'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import exportBtn from '@/components/exportBtnV2'
export default {
  name: 'ZhongtuoQuery',
  components: { steelTradeAggrid, exportBtn },
  data() {
    return {
      disRemove: true,
      options: {
        'stock.out.goods.type': []
      },
      headerCount: null,
      footerCount: null,
      childHeaderCount: null,
      childFooterCount: null,
      searchInfo: null,
      formItems: [
        {
          label: this.$t('grid.others.salesOrderNumber'),
          value: 'sCode',
          type: 'elInput',
          placeholder: this.$t('grid.others.pleaseEnterTheSalesOrderNumber')
        },
        {
          label: this.$t('grid.others.customer'),
          value: 'sCustomerId',
          type: 'cndInputDialogItem',
          dialogType: 'customer',
          defaultUrl: '/esc/customer/page',
          option: { valueKey: 'sPath' },
          placeholder: this.$t('grid.others.pleaseSelectTheCustomer')
        },
        {
          label: this.$t('grid.title.company'),
          value: 'sCompanyId',
          type: 'cndInputDialog',
          dialogType: 'company',
          placeholder: this.$t('grid.others.pleaseSelectCompany')
        },
        {
          label: this.$t('grid.others.warehouse'),
          value: 'sWarehouseId',
          type: 'cndInputDialog',
          // dialogType: 'warehouse',
          defaultUrl: '/esc/extbalance/config/page',
          option: {
            label: 'sBdaWarehouseName',
            value: 'sBdaWarehouseId'
          },
          placeholder: this.$t('grid.others.pleaseSelectAWarehouse')
        },
        {
          label: this.$t('grid.others.department'),
          value: 'sDepartmentId',
          type: 'cndInputDialog',
          dialogType: 'depart',
          placeholder: this.$t('grid.others.pleaseSelectTheDepartment')
        },
        {
          label: this.$t('grid.title.accountingGroup'),
          value: 'sCheckGroupId',
          type: 'cndInputDialog',
          dialogType: 'cost',
          placeholder: this.$t('grid.others.pleaseSelectAccountingGroup')
        },
        {
          label: '货权转移方式',
          value: 'sOutGoodsType',
          type: 'elSelect',
          dict: 'stock.out.goods.type'
        },
        {
          label: '数量差异筛选',
          value: 'sQtxDiff',
          default: '1',
          type: 'elSelect',
          allHide: true,
          dict: 'base.yes-no'
        },
        {
          label: this.$t('grid.title.personnel'),
          value: 'sStaffId',
          type: 'cndInputDialogItem',
          option: { valueKey: 'sPath' },
          dialogType: 'staff',
          placeholder: this.$t('grid.others.pleaseSelectPersonnel'),
          itemType: 'occultation'
        },
        {
          label: this.$t('grid.title.createdBy'),
          value: 'sCreator',
          type: 'cndInputDialog',
          itemType: 'occultation',
          placeholder: this.$t('grid.others.pleaseSelectTheCreator'),
          dialogType: 'applicant'
        },
        {
          label: this.$t('grid.title.createdAt'),
          value: ['sCreateTime', 'vCreateTimeTo'],
          placeholder: [this.$t('grid.others.pleaseSelectTheDate'), this.$t('grid.others.pleaseSelectTheDate')],
          itemType: 'occultation',
          unlinkPanels: true,
          type: 'elDatePicker'
        }
      ],
      columnDefs: [
        {
          headerName: this.$t('grid.others.salesOrderNumber'),
          field: 'sCode'
        },
        {
          headerName: '发货数量',
          field: 'vSumContractQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: '发货件数',
          field: 'vSumQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value)
          }
        },
        {
          headerName: '实发数量',
          field: 'vSumActualQtx',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: '实发件数',
          field: 'vSumActualQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value)
          }
        },
        {
          headerName: this.$t('grid.others.shipmentAmount'),
          field: 'vSumAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: this.$t('grid.others.warehouse'),
          field: 'vWarehouseName'
        },
        {
          headerName: this.$t('grid.others.customer'),
          field: 'vCustomerName'
        },
        {
          headerName: '货权转移方式',
          field: 'sOutGoodsType',
          valueGetter: (params) => {
            return getCnDitc(params, this.options['stock.out.goods.type'], 'sOutGoodsType')
          }
        },
        {
          headerName: this.$t('grid.title.company'),
          field: 'vCompanyName'
        },
        {
          headerName: this.$t('grid.title.accountingGroup'),
          field: 'vCheckGroupName'
        },
        // {
        //   headerName: this.$t('grid.others.department'),
        //   field: 'vDepartmentName'
        // },
        {
          headerName: this.$t('grid.title.personnel'),
          field: 'vStaffName'
        },
        {
          headerName: this.$t('grid.title.createdBy'),
          field: 'vCreatorName'
        },
        {
          headerName: this.$t('grid.title.createdAt'),
          field: 'sCreateTime',
          cellStyle: { textAlign: 'right' },
          minWidth: 150,
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
          }
        },
        {
          field: 'vManagementName',
          headerName: '经营单位'
        }
      ],
      childColumnDefs: [
        {
          headerName: '仓库出仓单号',
          field: 'sExtBalanceCode'
        },
        {
          headerName: '出仓时间',
          field: 'sOutDate',
          cellStyle: { textAlign: 'right' },
          minWidth: 150,
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD', params.data.sOutDate)
          }
        },
        {
          headerName: this.$t('grid.others.item'),
          field: 'vArtName'
        },
        {
          headerName: '发货数量',
          field: 'sContractQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: '发货件数',
          field: 'sQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value)
          }
        },
        {
          headerName: '实发数量',
          field: 'sActualQtx',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: '实发件数',
          field: 'sActualQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value)
          }
        },
        {
          headerName: this.$t('grid.others.steelCoilNumber'),
          field: 'sSteelNo'
        }
      ],
      rowData: [],
      selectCode: null,
      rowDataChild: [],
      dialogVisibleadd: false

      // getGridApi: () => {
      //   return this.$refs.aggrid.gridApi
      // }
    }
  },
  computed: {
    detailParams() {
      const sIds = []
      const selectIds = []
      this.rowData.forEach(item => {
        sIds.push(item.sId)
        if (item && item._selected) {
          selectIds.push(item.sId)
        }
      })
      return {
        sIds: sIds.toString(),
        selectIds: selectIds.toString()
      }
    }
  },
  beforeCreate() {
    getDictet([
      'stock.out.goods.type'
    ]).then(result => {
      this.options['stock.out.goods.type'] = result.data[0].dicts
    }).catch(() => {
    })
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      this.selectCode = null
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },

    setCount(vCount = 0, vSumQty = 0, vSumPkg = 0, flag) {
      this[flag] = [
        { count: vCount, key: 'count' },
        { title: '订货件数', count: SteelFormat.formatThousandthSign(vSumPkg), unit: this.$t('grid.others.pieces') },
        { title: '订货数量', count: SteelFormat.formatThousandthSign(vSumQty, 4), unit: this.$t('grid.others.ton') }
        // { title: this.$t('grid.others.shipmentAmount'), count: SteelFormat.formatPrice(vSumAmt), unit: this.$t('grid.others.yuan') }
      ]
    },
    hidebody(id) {
      this.dialogVisibleadd = false
      this.purInvoiceId = id
      // this.purVisible = true
    },
    // 点击 表格
    onRowDoubleClicked(params) {

    },

    onCloseAdd() {
      this.dialogVisibleadd = false
      this.$refs.aggrid.reloadTableData()
    },
    // 选中
    handleFooterCount(rowData) {
      this.$refs.aggrid.getSelectedData(res => {
        const vCount = res.length
        let vSumContractQty = new Decimal(0)
        let vSumQty = new Decimal(0)
        let vSumAmt = new Decimal(0)
        res.forEach(el => {
          vSumContractQty = vSumContractQty.add(el.vSumContractQty)
          vSumQty = vSumQty.add(el.vSumQty)
          vSumAmt = vSumAmt.add(el.vSumAmt)
        })
        this.setTotal(vCount, +vSumContractQty, +vSumQty, +vSumAmt, 'footerCount')
      })
    },
    setChildTotal(vCount = 0, sumsQtx = 0, sumsQty = 0, flag) {
      this[flag] = [
        { count: vCount, key: 'count' },
        { title: '实发数量', count: SteelFormat.formatThousandthSign(sumsQtx, 4), unit: this.$t('grid.others.ton') },
        { title: '实发件数', count: SteelFormat.formatThousandthSign(sumsQty), unit: this.$t('grid.others.pieces') }
      ]
    },
    setTotal(vCount = 0, sumsQtx = 0, sumsQty = 0, sumsTaxAmt = 0, flag) {
      this[flag] = [
        { count: vCount, key: 'count' },
        { title: '发货数量', count: SteelFormat.formatThousandthSign(sumsQtx, 4), unit: this.$t('grid.others.ton') },
        { title: '发货件数', count: SteelFormat.formatThousandthSign(sumsQty), unit: this.$t('grid.others.pieces') },
        { title: '发货金额', count: SteelFormat.formatPrice(sumsTaxAmt), unit: this.$t('grid.others.yuan') }
      ]
    },
    handleChildFooterCount(rowData) {
      this.$refs.detailAggrid.getSelectedData(res => {
        const vCount = res.length
        let sActualQtx = new Decimal(0)
        let sActualQty = new Decimal(0)
        res.forEach(el => {
          sActualQtx = sActualQtx.add(el.sActualQtx)
          sActualQty = sActualQty.add(el.sActualQty)
        })
        this.setChildTotal(vCount, +sActualQtx, +sActualQty, 'childFooterCount')
      })
    },
    rowClicked({ data }) {
      if (!data) {
        this.selectCode = null
        this.$refs.detailAggrid.loadTableData()
      } else if (data && this.selectCode !== data.sCode) {
        this.selectCode = data.sCode
        this.$refs.detailAggrid.loadTableData()
      }
    },
    // 主表
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        extbalanceOutreceiptPageAll(this.$refs.searchForm.getSearchData(), {
          ...pagination
        }).then(res => {
          this.rowData = res.data.deliveryPage.content.map((item, index) => {
            if (this.selectCode) {
              item._selected = this.selectCode === item?.sCode
            } else {
              item._selected = index === 0
            }
            item._selectedKeys = []
            return item
          })
          const { vCount, vSumContractQty, vSumQty, vSumAmt } = res.data
          this.setTotal(vCount, vSumContractQty, vSumQty, vSumAmt, 'headerCount')
          resolve(res.data.deliveryPage)
          this.rowClicked({ data: res.data.deliveryPage.content[0] })
        }).catch(() => {
          this.totalRowData = null
          this.rowData = []
          reject(0)
        })
      })
    },
    loadDetail(pagination) {
      return new Promise((resolve, reject) => {
        extbalanceOutreceiptPage(this.selectCode, pagination).then(res => {
          this.rowDataChild = res.data.page.content.map(item => {
            item._selected = false
            item._selectedKeys = []
            return item
          })
          const { vCount, vSumQtx, vSumQty } = res.data
          this.setChildTotal(vCount, vSumQtx, vSumQty, 'childHeaderCount')
          resolve(res.data.page)
        }).catch(() => {
          reject([])
        })
      })
    }
  }
}
</script>
