<template>
  <div class="page-container">
    <p class="page-title">项目发货单</p>
    <div class="layout-content auto-page-title">
      <steel-search-form
        ref="searchForm"
        :form-items="formItems"
        @search="onSearch"
      />
      <div class="btn-group mt-10">
        <div class="text">
          项目发货单{{ $t('grid.others.list') }}
        </div>
        <div>
          <export-btn
            class="ml-10"
            :file-name="'项目发货单'"
            ids-key="saleContractIds"
            api-url="/esc/contract/sale/process/detail/projectExcel/export"
            :post-params="searchInfo"
            :export-config="exportConfig"
            :get-grid-api="getGridApi"
            :detail-params="detailParams"
            isload-detail
          />
        </div>
      </div>
      <!-- 销售合同进程 -->
      <steelTradeAggrid
        ref="aggrid"
        :heightinif="300"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        table-selection="multiple"
        row-key="sId"
        @selectedChange="handleChange"
        @pageChange="selectContractId = null"
        @rowClicked="handleChangeContract"
      />
      <div style="height:auto" class="mt-10">
        <component
          :is="tab[0].name"
          :ref="tab[0].name"
          :s-id="selectContractId"
          :select-data="selectData"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { Moment } from 'cnd-utils'
import {
  getContractSaleProcessList
} from '@/api/queryStatistic/saleV2'
import exportBtn from '@/components/exportBtnV2'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import enterWare from './components/enterWare'
var Decimal = window.Decimal
export default {
  name: 'ProjectInvoice',
  components: {
    steelTradeAggrid,
    enterWare,
    exportBtn
  },
  data() {
    return {
      activeName: 'enterWare',
      tab: [
        {
          label: this.$t('grid.others.outOfPositionDetails'),
          name: 'enterWare'
        }
      ],
      formItems: [{
        label: this.$t('grid.others.itemNumberTag'),
        value: 'sProjectCode',
        type: 'elInput',
        placeholder: this.$t('grid.others.pleaseEnterTheItemNumberTag')
      }, {
        label: this.$t('grid.title.purchaseContractNumber'),
        value: 'sPurCode',
        type: 'elInput',
        placeholder: this.$t('grid.others.enterPurchaseContractNumber')
      }, {
        label: this.$t('grid.title.salesContractNumber'),
        value: 'sCode',
        type: 'elInput',
        placeholder: this.$t('grid.others.pleaseEnterTheSalesContractNumber')
      }, {
        label: this.$t('grid.others.supplier'), // 供应商,
        value: 'sSupplierId',
        type: 'cndInputDialogItem',
        dialogType: 'customer',
        defaultUrl: '/esc/customer/page',
        option: { valueKey: 'sPath' },
        placeholder: this.$t('grid.others.pleaseSelectCompany')
      }, {
        label: this.$t('grid.others.customer'),
        value: 'sCustomerId',
        type: 'cndInputDialogItem',
        dialogType: 'customer',
        defaultUrl: '/esc/customer/page',
        option: { valueKey: 'sPath' },
        placeholder: this.$t('grid.others.pleaseSelectTheCustomer')
      }, {
        label: this.$t('grid.title.personnel'),
        value: 'sStaffId',
        type: 'cndInputDialogItem',
        option: { valueKey: 'sPath' },
        dialogType: 'staff'
      },
      {
        label: this.$t('grid.title.createdBy'),
        value: 'sCreator',
        type: 'cndInputDialog',
        dialogType: 'creater',
        itemType: 'occultation'
      },
      {
        label: this.$t('grid.title.createdAt'),
        value: ['sCreateTime', 'vCreateTimeTo'],
        placeholder: [this.$t('grid.others.pleaseSelectTheDate'), this.$t('grid.others.pleaseSelectTheDate')],
        type: 'elDatePicker',
        unlinkPanels: true,
        itemType: 'occultation'
      },
      {
        label: '经营单位',
        value: 'sManagementId',
        type: 'cndInputDialog',
        dialogType: 'escOrg',
        itemType: 'occultation'
      }
      ],
      columnDefs: [
        {
          headerName: this.$t('grid.others.itemNumberTag'),
          field: 'sProjectCode',
          pinned: 'left'
        },
        {
          headerName: this.$t('grid.title.purchaseContractNumber'),
          field: 'sPurCode',
          pinned: 'left'
        },
        {
          headerName: this.$t('grid.title.salesContractNumber'),
          field: 'sCode',
          pinned: 'left'
        },
        {
          headerName: this.$t('grid.others.customer'),
          field: 'vCustomerName'
        },
        {
          headerName: this.$t('grid.title.company'),
          field: 'vCompanyName'
        },
        {
          headerName: this.$t('grid.others.supplier'), // 供应商,
          field: 'vSupplierName'
        },
        {
          headerName: this.$t('grid.title.personnel'),
          field: 'vStaffName'
        },
        {
          headerName: this.$t('grid.title.createdBy'),
          field: 'vCreatorName'
        },
        {
          headerName: this.$t('grid.title.createdAt'),
          field: 'sCreateTime',
          minWidth: 150,
          valueGetter(params) {
            return params.data._hiddenCheckbox ? '' : Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
          }
        },
        {
          field: 'vManagementName',
          headerName: '经营单位'
        }
      ],
      exportConfig: [
        { label: this.$t('grid.others.itemNumberTag'), value: 'sProjectCode' },
        { label: this.$t('grid.title.purchaseContractNumber'), value: 'sPurCode' },
        { label: this.$t('grid.title.salesContractNumber'), value: 'sCode' },
        { label: this.$t('grid.others.customer'), value: 'vCustomerName' },
        { label: this.$t('grid.title.company'), value: 'vCompanyName' },
        { label: this.$t('grid.others.supplier'), value: 'vSupplierName' },
        { label: this.$t('grid.title.personnel'), value: 'vStaffName' },
        { label: this.$t('grid.title.createdBy'), value: 'vCreatorName' },
        { label: this.$t('grid.title.createdAt'), value: 'sCreateTime' },
        { label: '经营单位', value: 'vManagementName' }
      ],
      rowData: [],
      sheetStatus: [],
      natureTypeDict: [],
      selectContractId: null,
      selectData: null,
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      searchInfo: null
    }
  },
  computed: {
    detailParams() {
      const sIds = []
      const selectIds = []
      this.rowData.forEach(item => {
        sIds.push(item.sId)
        if (item && item._selected) {
          selectIds.push(item.sId)
        }
      })
      return {
        sIds: sIds,
        selectIds: selectIds
      }
    }
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      searchInfo.sSheetStatus = searchInfo.sSheetStatus?.toString()
      this.searchInfo = searchInfo
      if (load) {
        this.selectContractId = null
        this.selectData = null
        this.$refs.aggrid.loadTableData()
      }
    },
    loadData(pagination = { page: 0, limit: 30 }) {
      const _this = this
      return new Promise((resolve, reject) => {
        getContractSaleProcessList(
          this.searchInfo,
          pagination
        ).then(res => {
          this.rowData = res.data.contractPage.content.map((item, index) => {
            if (!this.selectContractId) {
              item._selected = index === 0
            } else {
              item._selected = item.sId === this.selectContractId
            }
            return item
          })
          if (res.data.contractPage.content.length === 0) {
            this.selectContractId = null
            this.selectData = null
          } else {
            _this.handleChangeContract({ data: res.data.contractPage.content[0] })
          }
          resolve(res.data.contractPage)
        }).catch(() => {
          reject(0)
        })
      })
    },
    handleChange() {
      this.$refs.aggrid.getSelectedData(res => {
        setTimeout(() => {
          this.$refs.aggrid.gridApi.setPinnedBottomRowData([this.computeTotal(res.length ? 'selected' : 'all')])
        }, 0)
      })
    },
    computeTotal(type = 'all') {
      return Object.assign(this.rowData.reduce((prev, next) => {
        Object.keys(prev).forEach(key => {
          if (type === 'all') {
            prev[key] = new Decimal(+prev[key]).add(+next[key] || 0)
          } else {
            if (next._selected) {
              prev[key] = new Decimal(+prev[key]).add(+next[key] || 0)
            }
          }
        })
        return prev
      }, {
        vContractQty: 0,
        vContractAmt: 0,
        vMarginAmt: 0,
        vRecMarginAmt: 0,
        vStockQty: 0,
        vPaymentAmt: 0,
        vFrozenAmt: 0,
        vInvoiceAmt: 0,
        sTaxAmt: 0
      }), {
        sProjectCode: type === 'all' ? this.$t('grid.others.amountTo') : `${this.$t('components.selected')}${this.rowData.filter(item => item._selected).length}${this.$t('pagination.items')}`,
        sCreateTime: null,
        sRatifyDate: null,
        _selected: false,
        _hiddenCheckbox: true
      })
    },
    handleChangeContract(params) {
      if (!params.data._hiddenCheckbox) {
        this.selectContractId = params.data.sId
        this.selectData = params.data
        // const activeObj = this.tabList.find(el => el.name === this.activeName)
      }
      // this.activeName = 'contract'
      // params.node.setSelected(true, true)
    }
  }
}
</script>
