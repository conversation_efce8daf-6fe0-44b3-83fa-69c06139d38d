<template>
  <div class="page-container">
    <p class="page-title">加工成品到货台账</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form ref="searchForm" :form-items="formItems" @search="onSearch" />
      <div class="btn-group mt-10" style="display: flex">
        <div class="text" style="flex:1">
          加工成品到货台账列表
        </div>
        <div>
          <importBtn
            v-has:esc_ledger_process_import
            class="mr-10 ml-10"
            btn-text="导入原料采购单价"
            :action="`/api/esc/ledger/process/import`"
            :action-success-url="`/esc/ledger/process/importSuccessData`"
            success-mark="success"
            @success="successImport"
          />
          <el-button
            type="primary"
            size="mini"
            @click="save"
          >{{ $t('btns.save') }}</el-button>
          <export-btn
            class="ml-10"
            :file-name="'加工成品到货台账'"
            api-url="/esc/ledger/process/export"
            :post-params="searchInfo"
            :export-config="exportConfig"
            :get-grid-api="getGridApi"
          />
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        table-selection="multiple"
        full-row-type="parent"
        @selectedChange="handleFooterCount"
        @rowValueChanged="rowValueChanged"
      />
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import { Middleware } from 'cndinfo-ui'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import exportBtn from '@/components/exportBtnV2'
import { SteelFormat } from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
import { getDictList, getCnDitc } from '@/utils/dict'
import { handleDict } from '@/utils/common'
import importBtn from '@/components/importBtn'

import {
  processLedgerPage,
  // processLedgerModify,
  processLedgerBatchModify
} from '@/api/queryStatistic/purchase'
export default {
  name: 'ProcessFinishedProductLedger',
  components: { steelTradeAggrid, exportBtn, importBtn },
  data() {
    return {
      searchInfo: null,
      formItems: [
        {
          label: '成品钢卷号',
          value: 'sSteelNo',
          type: 'elInput'
        },
        {
          label: '原料钢卷号',
          value: 'sRawSteelNo',
          type: 'elInput'
        },
        {
          label: '品名',
          value: 'sArtName',
          type: 'elInput'
        },
        {
          label: '项目号',
          value: 'sProjectCode',
          type: 'elInput'
        },
        {
          label: '加工单号',
          value: 'sTaskAllotCode',
          type: 'elInput'
        },
        {
          label: '进成品仓单号',
          value: 'sPurNoticeCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.others.warehouse'),
          value: 'sWarehouseId',
          type: 'cndInputDialogItem',
          defaultUrl: '/esc/warehouse/info/dialog/v2/page-dialog',
          option: { valueKey: 'sPath' },
          placeholder: this.$t('grid.others.pleaseSelectAWarehouse')
        },
        {
          label: '入库类别',
          value: 'sStoreType',
          type: 'elSelect',
          itemType: 'occultation',
          dict: 'store.type',
          placeholder: this.$t('components.pleaseSelect')
        },
        {
          label: '销售客户',
          value: 'sSaleCustomerId',
          type: 'cndInputDialogItem',
          dialogType: 'customer',
          customerType: '10',
          defaultUrl: '/esc/customer/page',
          option: { valueKey: 'sPath' }
        },
        {
          label: '入库日期',
          value: ['sArrivalNoticeDate', 'sArrivalNoticeDateTo'],
          placeholder: [this.$t('grid.others.pleaseSelectTheDate'), this.$t('grid.others.pleaseSelectTheDate')],
          itemType: 'occultation',
          unlinkPanels: true,
          type: 'elDatePicker'
        },
        {
          label: '经营单位',
          value: 'sManagementId',
          type: 'cndInputDialog',
          dialogType: 'escOrg',
          itemType: 'occultation'
        },
        {
          label: this.$t('grid.title.accountingGroup'),
          value: 'sCheckGroupIds',
          type: 'cndInputDialogItem',
          dialogType: 'cost',
          // required: true,
          errorMessage: this.$t('grid.others.pleaseSelectAccountingGroup'),
          multiple: true,
          reserveKeyword: false,
          placeholder: this.$t('grid.others.pleaseSelectAccountingGroup')
        },
        {
          label: '发货单号',
          value: 'sNoticeSaleDeliveryCode',
          type: 'elInput'
        },
        {
          label: '原料规格',
          value: 'sRawSpecId',
          type: 'cndInputDialog',
          defaultUrl: '/esc/goods/ext/attribute',
          otherOptions: {
            sArtNameId: '***************'
          }
        },
        {
          label: '发票日期',
          value: ['sInvoiceSaleDate', 'sInvoiceSaleDateTo'],
          placeholder: [this.$t('grid.others.pleaseSelectTheDate'), this.$t('grid.others.pleaseSelectTheDate')],
          type: 'elDatePicker',
          default: ['', ''],
          unlinkPanels: true
        }
      ],
      columnDefs: [
        {
          headerName: '成品钢卷号',
          field: 'sSteelNo'
        },
        {
          headerName: '原料钢卷号',
          field: 'sRawSteelNo'
        },
        {
          headerName: '品名',
          field: 'sArtName'
        },
        {
          headerName: '原料品名',
          field: 'sRawArtName'
        },
        {
          headerName: '牌号',
          field: 'sMaterial'
        },
        {
          headerName: '规格',
          field: 'sSpec'
        },
        {
          headerName: '原料规格',
          field: 'sRawSpec'
        },
        {
          headerName: '原料材质',
          field: 'sRawMaterial'
        },
        {
          headerName: '件重/吨',
          field: 'sQtx',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: '件数',
          field: 'sQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value)
          }
        },
        {
          headerName: '入库日期',
          field: 'sArrivalNoticeDate',
          cellStyle: { textAlign: 'right' },
          minWidth: 150,
          valueGetter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sArrivalNoticeDate)
          }
        },
        {
          headerName: '项目号',
          field: 'sProjectCode'
        },
        {
          headerName: '加工单号',
          field: 'sTaskAllotCode'
        },
        {
          headerName: '进成品仓单号',
          field: 'sPurNoticeCode'
        },
        {
          headerName: '仓库',
          field: 'sWarehouseName'
        },
        {
          headerName: '原产地',
          field: 'sOriginPlace'
        },
        {
          headerName: '原卷重',
          field: 'sOriginQtx'
        },
        {
          headerName: '入库类别',
          field: 'sStoreType',
          valueGetter: params => {
            return getCnDitc(params, this.selectOps['store.type'], 'sStoreType')
          }
        },
        {
          headerName: '物料号',
          field: 'sMaterialNo'
        },
        {
          headerName: '木架',
          field: 'sWoodenFrame'
        },
        {
          headerName: '张数',
          field: 'sPageNum'
        },
        {
          headerName: '库位号',
          field: 'sLibraryNo'
        },
        {
          headerName: '出库日期',
          field: 'sDepositDate',
          cellStyle: { textAlign: 'right' },
          minWidth: 150,
          valueGetter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sDepositDate)
          }
        },
        {
          headerName: '发货单号',
          field: 'sNoticeSaleDeliveryCode'
        },
        {
          headerName: '销售发票单号',
          field: 'sInvoiceSaleCode'
        },
        {
          headerName: '发票日期',
          field: 'sInvoiceSaleDate',
          cellStyle: { textAlign: 'right' },
          minWidth: 150,
          valueGetter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sInvoiceSaleDate)
          }
        },
        {
          headerName: '销售客户',
          field: 'sSaleCustomerName'
        },
        {
          headerName: '销售单价',
          field: 'sSaleTaxPrice',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return params.value !== null && params.value !== '' ? SteelFormat.formatPrice(params.value) : ''
          }
        },
        {
          headerName: '销售金额',
          field: 'sSaleTaxAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return params.value !== null && params.value !== '' ? SteelFormat.formatPrice(params.value) : ''
          }
        },
        {
          headerName: '所属销售合同',
          field: 'sSaleContractCode'
        },
        {
          headerName: '备注',
          field: 'sRemark'
        },
        {
          headerName: '对账时间',
          field: 'sReconciliationTime',
          cellStyle: { textAlign: 'right' },
          minWidth: 150,
          editable: true,
          valueGetter(params) {
            return Moment.time('YYYY-MM-DD', params.data.sReconciliationTime)
          },
          cellEditorFramework: Vue.extend(Middleware.createComponent(
            'AgGridDate',
            {
              mark: 'sReconciliationTime',
              type: 'date',
              format: 'yyyy-MM-dd',
              defaultCurrentDate: false,
              placeholder: this.$t('grid.others.selectDate')
            },
            {
              change: ({ value, rowData, middleware }) => {
                this.$refs.aggrid.gridApi.refreshCells(rowData)
              }
            }
          ))
        },
        {
          headerName: '原料采购单价',
          field: 'sPickPurPrice',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return params.value !== null && params.value !== '' ? SteelFormat.formatPrice(params.value) : ''
          },
          editable: true,
          cellEditorFramework: Vue.extend(Middleware.createComponent(
            'CndInputNumber',
            {
              mark: 'sPickPurPrice',
              type: 'number',
              decimalDigit: 2
            },
            {
              blur: ({ event, rowData, middleware }) => {
                this.$refs.aggrid.gridApi.refreshCells(rowData)
              }
            }
          ))
        },
        {
          headerName: '加工费',
          field: 'sProcessingFee',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return params.value !== null && params.value !== '' ? SteelFormat.formatPrice(params.value) : ''
          }
        },
        {
          headerName: '月度中标单价',
          field: 'sMonthlyWinningPrice',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return params.value !== null && params.value !== '' ? SteelFormat.formatPrice(params.value) : ''
          },
          editable: true,
          cellEditorFramework: Vue.extend(Middleware.createComponent(
            'CndInputNumber',
            {
              mark: 'sMonthlyWinningPrice',
              type: 'number',
              decimalDigit: 2,
              negative: true
            },
            {
              blur: ({ event, rowData, middleware }) => {
                this.$refs.aggrid.gridApi.refreshCells(rowData)
              }
            }
          ))
        },
        {
          headerName: '平均销售运费',
          field: 'sAvgSaleFreightCost',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return params.value !== null && params.value !== '' ? SteelFormat.formatPrice(params.value) : ''
          },
          editable: true,
          cellEditorFramework: Vue.extend(Middleware.createComponent(
            'CndInputNumber',
            {
              mark: 'sAvgSaleFreightCost',
              type: 'number',
              decimalDigit: 2,
              negative: true
            },
            {
              blur: ({ event, rowData, middleware }) => {
                this.$refs.aggrid.gridApi.refreshCells(rowData)
              }
            }
          ))
        },
        {
          headerName: '平均加工单价',
          field: 'sAvgProcessPrice',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return params.value !== null && params.value !== '' ? SteelFormat.formatPrice(params.value) : ''
          },
          editable: true,
          cellEditorFramework: Vue.extend(Middleware.createComponent(
            'CndInputNumber',
            {
              mark: 'sAvgProcessPrice',
              type: 'number',
              decimalDigit: 2,
              negative: true
            },
            {
              blur: ({ event, rowData, middleware }) => {
                this.$refs.aggrid.gridApi.refreshCells(rowData)
              }
            }
          ))
        },
        {
          headerName: '备忘',
          field: 'sMemo',
          minWidth: 200,
          editable: true
        },
        {
          headerName: this.$t('grid.title.accountingGroup'),
          field: 'vCheckGroupName'
        },
        {
          headerName: '经营单位',
          field: 'vManagementName'
        }
      ],
      exportConfig: [
        {
          label: '成品钢卷号',
          value: 'sSteelNo'
        },
        {
          label: '原料钢卷号',
          value: 'sRawSteelNo'
        },
        {
          label: '品名',
          value: 'sArtName'
        },
        {
          label: '原料品名',
          value: 'sRawArtName'
        },
        {
          label: '牌号',
          value: 'sMaterial'
        },
        {
          label: '规格',
          value: 'sSpec'
        },
        {
          label: '原料规格',
          value: 'sRawSpec'
        },
        {
          label: '原料材质',
          value: 'sRawMaterial'
        },
        {
          label: '件重/吨',
          value: 'sQtx'
          // setValue: value => { return Number((+value).toFixed(4)) }
        },
        {
          label: '件数',
          value: 'sQty'
        },
        {
          label: '入库日期',
          value: 'sArrivalNoticeDate',
          setValue: value => { return Moment.time('YYYY-MM-DD', value) }
        },
        {
          label: '项目号',
          value: 'sProjectCode'
        },
        {
          label: '加工单号',
          value: 'sTaskAllotCode'
        },
        {
          label: '进成品仓单号',
          value: 'sPurNoticeCode'
        },
        {
          label: '仓库',
          value: 'sWarehouseName'
        },
        {
          label: '原产地',
          value: 'sOriginPlace'
        },
        {
          label: '原卷重',
          value: 'sOriginQtx'
        },
        {
          label: '入库类别',
          value: 'sStoreType',
          setValue: value => {
            return handleDict(value, this.selectOps['store.type'])
          }
        },
        {
          label: '物料号',
          value: 'sMaterialNo'
        },
        {
          label: '木架',
          value: 'sWoodenFrame'
        },
        {
          label: '张数',
          value: 'sPageNum'
        },
        {
          label: '库位号',
          value: 'sLibraryNo'
        },
        {
          label: '出库日期',
          value: 'sDepositDate',
          setValue: value => { return Moment.time('YYYY-MM-DD', value) }
        },
        {
          label: '发货单号',
          value: 'sNoticeSaleDeliveryCode'
        },
        {
          label: '销售发票单号',
          value: 'sInvoiceSaleCode'
        },
        {
          label: '发票日期',
          value: 'sInvoiceSaleDate',
          setValue: value => { return Moment.time('YYYY-MM-DD', value) }
        },
        {
          label: '销售客户',
          value: 'sSaleCustomerName'
        },
        {
          label: '销售单价',
          value: 'sSaleTaxPrice',
          setValue: value => { return Number((+value).toFixed(2)) }
        },
        {
          label: '销售金额',
          value: 'sSaleTaxAmt',
          setValue: value => { return Number((+value).toFixed(2)) }
        },
        {
          label: '所属销售合同',
          value: 'sSaleContractCode'
        },
        {
          label: '备注',
          value: 'sRemark'
        },
        {
          label: '对账时间',
          value: 'sReconciliationTime',
          setValue: value => { return Moment.time('YYYY-MM-DD', value) }
        },
        {
          label: '原料采购单价',
          value: 'sPickPurPrice',
          setValue: value => { return Number((+value).toFixed(2)) }
        },
        {
          label: '月度中标单价',
          value: 'sMonthlyWinningPrice',
          setValue: value => { return Number((+value).toFixed(2)) }
        },
        {
          label: '平均销售运费',
          value: 'sAvgSaleFreightCost',
          setValue: value => { return Number((+value).toFixed(2)) }
        },
        {
          label: '平均加工单价',
          value: 'sAvgProcessPrice',
          setValue: value => { return Number((+value).toFixed(2)) }
        },
        {
          label: '备忘',
          value: 'sMemo'
        },
        {
          label: this.$t('grid.title.accountingGroup'),
          value: 'vCheckGroupName'
        },
        {
          label: '经营单位',
          value: 'vManagementName'
        }
      ],
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      selectOps: {
        'store.type': []
      },
      rowData: []
    }
  },
  mounted() {
    getDictList(this.selectOps)
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      if (load) {
        const { sCheckGroupIds } = searchInfo
        if (!sCheckGroupIds) {
          this.$message.warning('核算组不能为空!')
          return
        } else if (sCheckGroupIds && !sCheckGroupIds.length) {
          this.$message.warning('核算组不能为空!')
          return
        }
        this.$refs.aggrid.loadTableData()
      }
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        processLedgerPage(this.searchInfo, pagination).then(res => {
          this.rowData = res.data.content.map(item => {
            item._selected = false
            item._selectedKeys = []
            item._isEdit = false
            return item
          })
          resolve(res.data)
        }).catch(() => {
          reject([])
        })
      })
    },
    handleFooterCount(rowData) {
      // console.log(rowData)
    },
    resetRowChange(rowIndex, value) {
      this.$nextTick(() => {
        this.$refs.aggrid.gridApi.setFocusedCell(rowIndex, value)
        this.$refs.aggrid.gridApi.startEditingCell({
          rowIndex,
          colKey: value
        })
      })
    },
    rowValueChanged(params) {
      if (params.data.sPickPurPrice === '' || params.data.sPickPurPrice === null) {
        this.$message.warning('请输入原料采购单价')
        this.resetRowChange(params.rowIndex, 'sPickPurPrice')
        return false
      }
      params.data._isEdit = true
      this.$refs.aggrid.gridApi.refreshCells({ force: true })
      // processLedgerModify(params.data).then(res => {
      //   if (res.code === '0000') {
      //     this.$message.success(this.$t('grid.others.editSuccessfully'))
      //   }
      // }).finally(() => {
      //   this.$refs.aggrid.loadTableData()
      // })
    },
    save() {
      const pickPurPriceHasEmpty = this.rowData.some(item => item.sPickPurPrice === '' || item.sPickPurPrice === null)
      if (pickPurPriceHasEmpty) {
        this.$message.warning('请输入原料采购单价')
        return
      }
      const list = this.rowData.filter(item => item._isEdit === true)
      if (!list.length) {
        this.$message.warning('数据无变更，无需保存')
        return
      }
      processLedgerBatchModify(list).then(res => {
        if (res.code === '0000') {
          this.$message.success(this.$t('grid.others.editSuccessfully'))
        }
      }).finally(() => {
        this.$refs.aggrid.reloadTableData()
      })
    },
    successImport() {
      this.$refs.aggrid.reloadTableData()
    }
  }
}
</script>

<style scoped>

</style>
