<template>
  <div class="page-container">
    <p class="page-title">云钢业务数据统计报表</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form ref="searchForm" :form-items="formItems" @search="onSearch" />
      <div class="btn-group mt-10" style="display: flex">
        <div class="text" style="flex:1">
          云钢业务数据统计报表
        </div>
        <div>
          <!-- <export-btn
            class="ml-10"
            :file-name="'云钢业务数据统计报表'"
            api-url="/esc/ledger/customer/export"
            :post-params="searchInfo"
            :export-config="exportConfig"
            :get-grid-api="getGridApi"
          /> -->
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :header-total="headerCount"
        :footer-total="footerCount"
        :auto-load-data="false"
        table-selection="multiple"
        @selectedChange="handleFooterCount"
        @cellValueChanged="cellValueChanged"
      />
    </div>
  </div>
</template>

<script>
// import { Moment } from 'cnd-utils'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import exportBtn from '@/components/exportBtnV2'
import { Format } from 'cnd-horizon-utils'
import {
  getStatisticalPage
} from '@/api/queryStatistic/purchase'
export default {
  name: 'BusinessReport',
  components: { steelTradeAggrid, exportBtn },
  data() {
    return {
      searchInfo: null,
      formItems: [
        {
          label: '日期',
          value: ['sStartTime', 'sEndTime'],
          unlinkPanels: true,
          type: 'elDatePicker'
        },
        {
          label: '经营单位',
          value: 'sManagementId',
          type: 'cndInputDialog',
          dialogType: 'escOrg',
          itemType: 'occultation'
        }
      ],
      columnDefs: [
        {
          headerName: '日期',
          field: 'month'
        },
        {
          headerName: '每月新客户数',
          field: 'newCustomersQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return Format.formatThousandthSign(params.value, 0)
          }
        },
        {
          headerName: '销售合同数量',
          field: 'saleContractQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return Format.formatThousandthSign(params.value)
          }
        },
        {
          headerName: '销售合同金额',
          field: 'saleContractAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return Format.formatPrice(params.value)
          }
        },
        {
          headerName: '开票数量',
          field: 'invQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return Format.formatThousandthSign(params.value)
          }
        },
        {
          headerName: '开票金额',
          field: 'invoiceAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return Format.formatPrice(params.value)
          }
        },

        {
          headerName: '发货数量',
          field: 'saleQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return Format.formatThousandthSign(params.value)
          }
        },
        {
          headerName: '发货金额',
          field: 'saleAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return Format.formatPrice(params.value)
          }
        },
        {
          headerName: '结算数量',
          field: 'settleQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return Format.formatThousandthSign(params.value)
          }
        },
        {
          headerName: '结算金额',
          field: 'settleAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return Format.formatPrice(params.value)
          }
        }
      ],
      exportConfig: [
        { label: '日期', value: 'month' },
        { label: '每月新客户数', value: 'newCustomersQty' },
        { label: '销售合同数量', value: 'saleContractQty' },
        { label: '销售合同金额', value: 'saleContractAmt' },
        { label: '开票数量', value: 'invQty' },
        { label: '开票金额', value: 'invoiceAmt' },
        { label: '发货数量', value: 'saleQty' },
        { label: '发货金额', value: 'saleAmt' },
        { label: '结算数量', value: 'settleQty' },
        { label: '结算金额', value: 'settleAmt' }
      ],
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      headerCount: null,
      footerCount: null,
      rowData: []
    }
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      searchInfo.sCustomerIds = searchInfo.sCustomerIds?.toString()
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        getStatisticalPage(this.searchInfo, pagination).then(res => {
          this.rowData = res.data.content.map(item => {
            item._selected = false
            item._selectedKeys = []
            return item
          })
          resolve(res.data)
        }).catch(() => {
          reject([])
        })
      })
    },
    handleFooterCount(rowData) {
      console.log(rowData)
    }
  }
}
</script>

<style scoped>

</style>
