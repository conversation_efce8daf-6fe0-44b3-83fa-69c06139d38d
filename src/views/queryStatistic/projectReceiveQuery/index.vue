<template>
  <div class="page-container">
    <p class="page-title">项目收款查询</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form
        ref="searchForm"
        :form-items="formItems"
        :active-panel="formActivePanel"
        @search="onSearch"
      />
      <div class="btn-group mt-10">
        <div class="text">
          收款明细
        </div>
        <div>
          <export-btn
            file-name="项目收款"
            api-url="/esc/stock/delivery/project/receive/export"
            :post-params="searchInfo"
            :export-config="exportConfig"
            :get-grid-api="getGridApi"
          />
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        table-selection="multiple"
        row-key="sId"
        :header-total="headerTotal"
        :footer-total="footerTotal"
        @selectedChange="selectedChange"
      />
    </div>
  </div>
</template>

<script>
var Decimal = window.Decimal
import { SteelFormat } from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
import { handleDict } from '@/utils/common'
import {
  projectReceiveQuery
} from '@/api/queryStatistic/purchase'
import {
  getDictet, getCnDitc
} from '@/api/logistics/saleDelivery/saleorder'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import exportBtn from '@/components/exportBtnV2'
export default {
  name: 'ProjectReceiveQuery',
  components: { steelTradeAggrid, exportBtn },

  data() {
    return {
      formActivePanel: '1',
      formItems: [
        {
          label: this.$t('grid.others.itemNumberTag'),
          value: 'sProjectCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.title.purchaseContractNumber'),
          value: 'sPurContractCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.title.salesContractNumber'),
          value: 'sSaleContractCode',
          type: 'elInput'
        },
        {
          label: '来款' + this.$t('grid.others.customer'),
          value: 'sCustomerId',
          type: 'cndInputDialogItem',
          dialogType: 'customer',
          defaultUrl: '/esc/customer/page',
          option: { valueKey: 'sPath' },
          customerType: '10'
        },
        {
          label: '收款日期',
          value: ['sReceiveTime', 'sReceiveTimeTo'],
          placeholder: [this.$t('grid.others.startDate'), this.$t('grid.others.endDate')],
          unlinkPanels: true,
          type: 'elDatePicker'
        },
        {
          label: '经营单位',
          value: 'sManagementId',
          type: 'cndInputDialog',
          dialogType: 'escOrg',
          itemType: 'occultation'
        }
      ],
      searchInfo: null,
      columnDefs: [
        {
          headerName: '来款客户',
          field: 'sCustomerName'
        },
        {
          headerName: this.$t('grid.title.amount'),
          field: 'sTaxAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: '收款日期',
          field: 'sPayDate',
          valueGetter: params => {
            return Moment.time('YYYY-MM-DD', params.data.sPayDate)
          }
        },
        {
          headerName: this.$t('grid.others.originalDocumentNumber'),
          field: 'sUpCode'
        },
        {
          headerName: this.$t('grid.others.shippingOrderNumber'),
          field: 'sSaleNoticeCode'
        },
        {
          headerName: this.$t('grid.others.itemNumberTag'),
          field: 'sProjectCode'
        },
        {
          headerName: this.$t('grid.title.purchaseContractNumber'),
          field: 'sPurContractCode'
        },
        {
          headerName: this.$t('grid.title.salesContractNumber'),
          field: 'sSaleContractCode'
        },
        {
          headerName: this.$t('grid.others.transactionType'),
          field: 'sInteractionSubclass',
          valueGetter: params => {
            return getCnDitc(params, this.options['pay.subtype'], 'sInteractionSubclass')
          }
        },
        {
          headerName: this.$t('grid.others.paragraphType'),
          field: 'sFundType',
          valueGetter: params => {
            return getCnDitc(params, this.options['pay.payment.type'], 'sFundType')
          }
        },
        {
          headerName: this.$t('grid.others.noteNumber'),
          field: 'sBillCode'
        },
        {
          headerName: this.$t('grid.others.maturityDateOfAcceptance'),
          field: 'sMaturityDate',
          valueGetter(params) {
            return Moment.time('YYYY-MM-DD', params.data.sMaturityDate)
          }
        },
        {
          headerName: '经营单位',
          field: 'sManagementName'
        }
      ],
      rowData: [],
      headerTotal: null,
      footerTotal: null,
      delDisable: true,
      options: {
        'pay.subtype': [],
        'pay.payment.type': []
      },
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      exportConfig: [
        {
          label: '来款客户',
          value: 'sCustomerName'
        },
        {
          label: this.$t('grid.title.amount'),
          value: 'sTaxAmt',
          setValue: (value) => {
            return Number((+value).toFixed(2))
          }
        },
        {
          label: '收款日期',
          value: 'sPayDate',
          setValue: (value) => {
            return Moment.time('YYYY-MM-DD HH:mm:ss', value)
          }
        },
        {
          label: this.$t('grid.others.originalDocumentNumber'),
          value: 'sUpCode'
        },
        {
          label: this.$t('grid.others.shippingOrderNumber'),
          value: 'sSaleNoticeCode'
        },
        {
          label: this.$t('grid.others.itemNumberTag'),
          value: 'sProjectCode'
        },
        {
          label: this.$t('grid.title.purchaseContractNumber'),
          value: 'sPurContractCode'
        },
        {
          label: this.$t('grid.title.salesContractNumber'),
          value: 'sSaleContractCode'
        },
        {
          label: this.$t('grid.others.transactionType'),
          value: 'sInteractionSubclass',
          setValue: value => {
            return handleDict(value, this.options['pay.subtype'])
          }
        },
        {
          label: this.$t('grid.others.paragraphType'),
          value: 'sFundType',
          setValue: value => {
            return handleDict(value, this.options['pay.payment.type'])
          }
        },
        {
          label: this.$t('grid.others.noteNumber'),
          value: 'sBillCode'
        },
        {
          label: this.$t('grid.others.maturityDateOfAcceptance'),
          value: 'sMaturityDate',
          setValue: (value) => {
            return Moment.time('YYYY-MM-DD HH:mm:ss', value)
          }
        }
      ]
    }
  },

  created() {
    getDictet([
      'pay.subtype',
      'pay.payment.type'
    ]).then(result => {
      this.options['pay.subtype'] = result.data[0].dicts
      this.options['pay.payment.type'] = result.data[1].dicts
    }).catch(() => {
    })
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      if (load) {
        const { sPurContractCode, sProjectCode } = searchInfo
        if (!sPurContractCode && !sProjectCode) {
          this.$message.warning('项目号和采购合同号不能同时为空!')
          this.formActivePanel = '1'
          return
        }
        this.$refs.aggrid.loadTableData()
      }
    },
    loadData(pagination) {
      this.formActivePanel = ''
      return new Promise((resolve, reject) => {
        projectReceiveQuery(this.searchInfo, pagination).then(res => {
          this.rowData = res.data.details.content.map(item => {
            item._selected = false
            return item
          })
          const { totalAmt } = res.data
          const { totalElements } = res.data.details
          this.setCount(totalElements, totalAmt, 'headerTotal')
          resolve(res.data.details)
        }).catch(() => {
          this.rowData = []
          reject([])
        })
      })
    },
    selectedChange(rowData) {
      const details = rowData.filter((item) => item._selected)
      const vCount = details.length
      let sTaxAmt = new Decimal(0)
      details.forEach(item => {
        sTaxAmt = sTaxAmt.add(Number(item.sTaxAmt))
      })
      this.setCount(vCount, sTaxAmt, 'footerTotal')
    },
    setCount(vCount = 0, vSumAmt = 0, flag) {
      this[flag] = [
        { count: vCount, key: 'count' },
        { title: '金额', count: SteelFormat.formatPrice(vSumAmt, 2), unit: this.$t('grid.others.yuan') }
      ]
    }
  }
}
</script>

<style scoped>

</style>
