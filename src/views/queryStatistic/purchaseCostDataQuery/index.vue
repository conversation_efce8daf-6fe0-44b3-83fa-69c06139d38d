
<template>
  <div class="page-container">
    <p class="page-title">采购成本数据查询</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form ref="searchForm" :form-items="formItems" @search="onSearch" />
      <div class="btn-group mt-10">
        <div class="text">
          采购成本数据列表
        </div>
        <div />
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        row-key="_rowKey"
        :paginationinif="false"
        :table-selection="null"
        @selectedChange="selectedChange"
      />
    </div>
  </div>
</template>

<script>
import { Moment } from 'cnd-utils'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import { SteelFormat } from 'cnd-horizon-utils'
import { costSplitLogList } from '@/api/queryStatistic/purchase'

export default {
  name: 'PurchaseCostDataQuery',
  components: { steelTradeAggrid },
  data() {
    return {
      searchInfo: null,
      originalData: [],
      formItems: [
        {
          label: '结算单号',
          value: 'sSettlementCode',
          type: 'elInput'
        },
        {
          label: '销售合同号',
          value: 'sSaleContractCode',
          type: 'elInput'
        },
        {
          label: '项目分类',
          value: 'sType',
          type: 'elSelect',
          required: true,
          allHide: true,
          dict: [{
            sCodeName: '采购成本',
            sCodeValue: 'purCostData'
          }, {
            sCodeName: '加价区间',
            sCodeValue: 'markRangeData'
          }, {
            sCodeName: '运杂费金额',
            sCodeValue: 'transportCostData'
          }, {
            sCodeName: '结算增值',
            sCodeValue: 'addPriceData'
          }, {
            sCodeName: '总价',
            sCodeValue: 'totalPriceData'
          }]
        }
      ],
      columnDefs: [
        {
          field: 'fieldName',
          headerName: '项目明细'
        },
        {
          field: 'sSheetVersion',
          headerName: '版本数据'
        },
        {
          field: 'price',
          headerName: '单价',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            console.log('params: ', params)
            if (params.data.noPrice) {
              return '-'
            } else {
              return params.data.isBlankRow ? '' : SteelFormat.formatPrice(params.value)
            }
          }
        },
        {
          field: 'amount',
          headerName: '金额',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => params.data.isBlankRow ? '' : SteelFormat.formatPrice(params.value)
        },
        {
          field: 'sCreateTime',
          headerName: '维护时间/维护人员',
          minWidth: 300,
          valueGetter: params => params.data.isBlankRow ? '' : `${Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)}-${params.data.vCreatorName}`
        }
      ],
      rowData: []
    }
  },
  methods: {
    onSearch() {
      const searchInfo = this.$refs.searchForm.getSearchData()
      const previousSearchInfo = this.searchInfo
      this.searchInfo = searchInfo

      const isOnlyTypeChange = previousSearchInfo &&
        previousSearchInfo.sSettlementCode === searchInfo.sSettlementCode &&
        previousSearchInfo.sSaleContractCode === searchInfo.sSaleContractCode &&
        previousSearchInfo.sType !== searchInfo.sType

      if (isOnlyTypeChange && this.originalData.length > 0) {
        const data = this.formatDataByType(this.originalData)
        this.rowData = data
        this.$refs.aggrid.setRowData(data)
      } else {
        this.$refs.aggrid.loadTableData()
      }
    },
    selectedChange() {},
    loadData() {
      return new Promise((resolve, reject) => {
        costSplitLogList(this.searchInfo).then(response => {
          if (response && response.data) {
            this.originalData = response.data
            const data = this.formatDataByType(response.data)
            this.rowData = data
            resolve({ content: data, totalElements: data.length })
          } else {
            this.originalData = []
            this.rowData = []
            resolve({ content: [], totalElements: 0 })
          }
        }).catch(error => {
          console.error('获取采购成本数据失败:', error)
          this.originalData = []
          this.rowData = []
          reject(error)
        })
      })
    },
    formatDataByType(responseData) {
      if (!responseData?.length) return []

      const { sType } = this.searchInfo
      const formattedData = []
      const fieldMaps = {
        purCostData: [
          { label: '基础货值', priceKey: 'sBasicPrice', amountKey: 'sBasicAmt' },
          { label: '一票制运费', priceKey: 'sOneCarriagePrice', amountKey: 'sOneCarriageAmt' },
          { label: '一票制运费税差', priceKey: 'sOneCarriageAmtRatePrice', amountKey: 'sOneCarriageAmtRate' },
          { label: '返利金额', priceKey: 'sRebateaPrice', amountKey: 'sRebateaAmt' },
          { label: '采购成本合计', priceKey: 'sPurCostPrice', amountKey: 'sPurCost' }
        ],
        markRangeData: [
          { label: '加价区间', priceKey: 'sAddRangePrice', amountKey: 'sAddRange' }
        ],
        transportCostData: [
          { label: '运杂费', priceKey: 'sFreightPrice', amountKey: 'sFreightAmt' }
        ],
        addPriceData: [
          { label: '--货值增值', priceKey: 'sGoodAddPrice', amountKey: 'sGoodAddAmt' },
          { label: '--运费增值', priceKey: 'sTransportAddPrice', amountKey: 'sTransportAddAmt' },
          { label: '客户增值', priceKey: 'sCustomerPrice', amountKey: 'sCustomerAmt' },
          { label: '其他增值', priceKey: 'sOtherToAddPrice', amountKey: 'sOtherToAddAmt' },
          { label: '结算增值合计', priceKey: 'sSettleAddPrice', amountKey: 'sSettleAddAmt' }
        ],
        totalPriceData: [
          { label: '总单价', amountKey: 'sTotalPrice' },
          { label: '总金额', amountKey: 'sTotalAmt' }
        ]
      }

      const fields = fieldMaps[sType] || []

      responseData.forEach((item, index) => {
        if (index > 0) {
          formattedData.push({
            fieldName: '',
            amount: '',
            sCreateTime: '',
            vCreatorName: '',
            isBlankRow: true,
            _rowKey: index
          })
        }

        fields.forEach(field => {
          formattedData.push({
            fieldName: field.label,
            sSheetVersion: item.sSheetVersion,
            price: item[field.priceKey],
            amount: item[field.amountKey],
            sCreateTime: item.sCreateTime,
            vCreatorName: item.sCreator,
            noPrice: !field.priceKey,
            _rowKey: index
          })
        })
      })

      return formattedData
    }
  }
}
</script>
<style>
</style>
