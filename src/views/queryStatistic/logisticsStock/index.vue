<template>
  <div class="page-container">
    <p class="page-title">物流库存查询</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form ref="searchForm" :form-items="formItems" @search="onSearch" />
      <div class="btn-group mt-10">
        <div class="text">
          合同列表
        </div>
        <div>
          <export-btn
            file-name="物流库存查询"
            api-url="/esc/bi/stock/balance/export"
            :post-params="searchInfo"
            :get-grid-api="getGridApi"
            :isload-detail="true"
            ids-key="purCtrtCodeList"
            :detail-params="detailParams"
          />
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :heightinif="400"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        :show-header-select="false"
        table-selection="multiple"
        :header-total="headerCount"
        :footer-total="footerCount"
        @rowClicked="rowClicked"
        @selectedChange="handleFooterCount"
        @rowDoubleClicked="onRowDoubleClicked"
      />
      <div class="btn-group mt-10 pd">
        <div class="text">
          库存明细
        </div>
      </div>
      <steelTradeAggrid
        ref="detailAggrid"
        :load-data="loadDetail"
        :column-defs="childColumnDefs"
        :row-data="rowDataChild"
        :header-total="childHeaderCount"
        :footer-total="childFooterCount"
        :auto-load-data="false"
        table-selection="multiple"
        @selectedChange="handleChildFooterCount"
      />
    </div>
  </div>
</template>

<script>
var Decimal = window.Decimal
import { SteelFormat } from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
// import { handleDict } from '@/utils/common'
import {
  getDictet
  // getCnDitc
} from '@/api/logistics/saleDelivery/saleorder'
import { stockBalancePage, stockBalanceDetailPage } from '@/api/queryStatistic/purchase'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import exportBtn from '@/components/exportBtnV2'
export default {
  name: 'LogisticsStockQuery',
  components: { steelTradeAggrid, exportBtn },
  data() {
    return {
      disRemove: true,
      options: {
        'stock.out.goods.type': []
      },
      headerCount: null,
      footerCount: null,
      childHeaderCount: null,
      childFooterCount: null,
      searchInfo: null,
      formItems: [
        {
          label: '物流单据号',
          value: 'iowhRtrcCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.title.purchaseContractNumber'),
          value: 'purCtrtCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.title.salesContractNumber'),
          value: 'salCtrtCode',
          type: 'elInput'
        },
        {
          label: '项目号',
          value: 'prjtCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.others.customer'),
          value: 'custTrclIcode',
          type: 'cndInputDialogItem',
          dialogType: 'customer',
          defaultUrl: '/esc/customer/page',
          option: { valueKey: 'sPath' },
          placeholder: this.$t('grid.others.pleaseSelectTheCustomer')
        },
        {
          label: this.$t('grid.others.warehouse'),
          value: 'wrhsIcode',
          type: 'cndInputDialogItem',
          defaultUrl: '/esc/warehouse/info/dialog/v2/page-dialog',
          option: { valueKey: 'sPath' },
          placeholder: this.$t('grid.others.pleaseSelectAWarehouse')
        },
        {
          label: this.$t('grid.others.steelCoilNumber'),
          value: 'stcoCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.others.carriageNumber'),
          value: 'vhshCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.title.createdAt'),
          value: ['startCratDate', 'endCratDate'],
          placeholder: [this.$t('grid.others.pleaseSelectTheDate'), this.$t('grid.others.pleaseSelectTheDate')],
          itemType: 'occultation',
          unlinkPanels: true,
          type: 'elDatePicker'
        },
        {
          label: '经营单位',
          value: 'manaOrgIcode',
          type: 'cndInputDialog',
          dialogType: 'escOrg',
          itemType: 'occultation'
        },
        {
          label: this.$t('grid.title.accountingGroup'),
          value: 'sCheckGroupId',
          type: 'cndInputDialog',
          dialogType: 'cost'
        },
        {
          label: this.$t('grid.others.item'),
          value: 'cgcodeName',
          type: 'elInput'
        },
        {
          label: this.$t('grid.others.supplier'),
          value: 'sSupplierId',
          type: 'cndInputDialogItem',
          dialogType: 'customer',
          defaultUrl: '/esc/customer/page',
          option: { valueKey: 'sPath' }
        },
        {
          label: this.$t('grid.title.company'),
          value: 'corpOrgIcodeList',
          type: 'cndInputDialogItem',
          dialogType: 'company',
          multiple: true,
          reserveKeyword: false
        },
        {
          label: '是否纯采购',
          value: 'purchaseFlag',
          type: 'elSelect',
          dict: 'base.yes-no'
        }
      ],
      columnDefs: [
        {
          headerName: this.$t('grid.title.purchaseContractNumber'),
          field: 'purCtrtCode'
        },
        {
          headerName: this.$t('grid.others.itemNumberTag'),
          field: 'prjCodeSplc'
        },
        {
          headerName: this.$t('grid.others.supplier'),
          field: 'splrTrclName'
        },
        {
          headerName: '可钢厂直放数量',
          field: 'stmlDrcQtx',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: '在途数量',
          field: 'onpaQtx',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: '库存数量',
          field: 'invrQtx',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: '库存件数',
          field: 'invrQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value, 0)
          }
        },
        {
          field: 'manaOrgName',
          headerName: '经营单位'
        },
        {
          headerName: this.$t('grid.title.createdAt'),
          field: 'cratDate',
          cellStyle: { textAlign: 'right' },
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD', params.data.cratDate)
          }
        }

      ],
      childColumnDefs: [
        {
          headerName: '物流单据号',
          field: 'iowhRtrcCode'
        },
        {
          headerName: '入库时间',
          field: 'inwhTime'
        },
        {
          headerName: '库龄天数',
          field: 'stag'
        },
        {
          headerName: '项目号',
          field: 'prjtCode'
        },
        {
          headerName: this.$t('grid.others.warehouse'),
          field: 'wrhsName'
        },
        {
          headerName: this.$t('grid.others.item'),
          field: 'cgcodeName'
        },
        {
          headerName: '待发货件数',
          field: 'waitDspaQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value)
          }
        },
        {
          headerName: '待发货数量',
          field: 'waitDspaQtx',
          cellStyle: { textAlign: 'right' },
          valueFormatter: (params) => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: this.$t('grid.others.steelCoilNumber'),
          field: 'stcoCode'
        },
        {
          headerName: this.$t('grid.others.carriageNumber'),
          field: 'vhshCode'
        },
        {
          headerName: '类型',
          field: 'logtType'
        },
        {
          headerName: this.$t('grid.title.salesContractNumber'),
          field: 'salCtrtCode'
        },
        {
          headerName: this.$t('grid.others.customer'),
          field: 'custTrclName'
        },
        {
          headerName: this.$t('grid.title.company'),
          field: 'corpOrgName'
        },
        {
          headerName: this.$t('grid.title.remarks'),
          field: 'sRemark'
        },
        {
          headerName: this.$t('grid.title.accountingGroup'),
          field: 'acntOrgName'
        },
        {
          headerName: this.$t('grid.others.department'),
          field: 'busiOrgName'
        },
        {
          headerName: this.$t('grid.title.personnel'),
          field: 'stffName'
        }
      ],
      rowData: [],
      selectCode: null,
      rowDataChild: [],
      dialogVisibleadd: false,
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      }
    }
  },
  computed: {
    detailParams() {
      const sIds = []
      const selectIds = []
      this.rowData?.forEach(item => {
        sIds.push(item.purCtrtCode)
        if (item && item._selected) {
          selectIds.push(item.purCtrtCode)
        }
      })
      return {
        sIds,
        selectIds
      }
    }
  },
  beforeCreate() {
    getDictet([
      'stock.out.goods.type'
    ]).then(result => {
      this.options['stock.out.goods.type'] = result.data[0].dicts
    }).catch(() => {
    })
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      this.selectCode = null
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },

    setCount(vCount = 0, vSumQty = 0, vSumPkg = 0, flag) {
      this[flag] = [
        { count: vCount, key: 'count' },
        { title: '订货件数', count: SteelFormat.formatThousandthSign(vSumPkg), unit: this.$t('grid.others.pieces') },
        { title: '订货数量', count: SteelFormat.formatThousandthSign(vSumQty, 4), unit: this.$t('grid.others.ton') }
        // { title: this.$t('grid.others.shipmentAmount'), count: SteelFormat.formatPrice(vSumAmt), unit: this.$t('grid.others.yuan') }
      ]
    },
    hidebody(id) {
      this.dialogVisibleadd = false
      this.purInvoiceId = id
      // this.purVisible = true
    },
    // 点击 表格
    onRowDoubleClicked(params) {

    },

    onCloseAdd() {
      this.dialogVisibleadd = false
      this.$refs.aggrid.reloadTableData()
    },
    // 选中
    handleFooterCount(rowData) {
      this.$refs.aggrid.getSelectedData(res => {
        const vCount = res.length
        let gcQtx = new Decimal(0)
        let ztQtx = new Decimal(0)
        let kcQtx = new Decimal(0)
        res.forEach(el => {
          gcQtx = gcQtx.add(el.stmlDrcQtx)
          ztQtx = ztQtx.add(el.onpaQtx)
          kcQtx = kcQtx.add(el.invrQtx)
        })
        this.setTotal(vCount, +gcQtx, +ztQtx, +kcQtx, 'footerCount')
      })
    },
    setChildTotal(vCount = 0, waitDspaQtx = 0, waitDspaQty = 0, flag) {
      this[flag] = [
        { count: vCount, key: 'count' },
        { title: '待发货件数', count: SteelFormat.formatThousandthSign(waitDspaQty), unit: this.$t('grid.others.pieces') },
        { title: '待发货数量', count: SteelFormat.formatThousandthSign(waitDspaQtx, 4), unit: this.$t('grid.others.ton') }
      ]
    },
    setTotal(vCount = 0, gcQtx = 0, ztQtx = 0, kcQtx = 0, flag) {
      this[flag] = [
        { count: vCount, key: 'count' },
        { title: '可钢厂直放数量', count: SteelFormat.formatThousandthSign(gcQtx, 4), unit: this.$t('grid.others.ton') },
        { title: '在途数量', count: SteelFormat.formatThousandthSign(ztQtx, 4), unit: this.$t('grid.others.ton') },
        { title: '库存数量', count: SteelFormat.formatPrice(kcQtx, 4), unit: this.$t('grid.others.ton') }
      ]
    },
    handleChildFooterCount(rowData) {
      this.$refs.detailAggrid.getSelectedData(res => {
        const vCount = res.length
        let waitDspaQtx = new Decimal(0)
        let waitDspaQty = new Decimal(0)
        res.forEach(el => {
          waitDspaQtx = waitDspaQtx.add(el.waitDspaQtx)
          waitDspaQty = waitDspaQty.add(el.waitDspaQty)
        })
        this.setChildTotal(vCount, +waitDspaQtx, +waitDspaQty, 'childFooterCount')
      })
    },
    rowClicked({ data }) {
      console.log('data: ', data)
      if (!data) {
        this.rowDataChild = []
        this.selectCode = null
        // this.$refs.detailAggrid.loadTableData()
      } else if (data && this.selectCode !== data.purCtrtCode) {
        this.selectCode = data.purCtrtCode
        this.$refs.detailAggrid.loadTableData()
      }
    },
    // 主表
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        stockBalancePage(this.$refs.searchForm.getSearchData(), {
          ...pagination
        }).then(res => {
          this.rowData = res.data?.data?.list.map((item, index) => {
            if (this.selectCode) {
              item._selected = this.selectCode === item?.purCtrtCode
            } else {
              item._selected = index === 0
            }
            item._selectedKeys = []
            return item
          })
          this.rowClicked({ data: res.data?.data?.list[0] })
          resolve(res.data?.page?.totalCount)
        }).catch(() => {
          this.totalRowData = null
          this.rowData = []
          reject(0)
        })
      })
    },
    loadDetail(pagination) {
      return new Promise((resolve, reject) => {
        stockBalanceDetailPage({
          ...this.$refs.searchForm.getSearchData(),
          purCtrtCode: this.selectCode
        }, pagination).then(res => {
          this.rowDataChild = res.data.data.map(item => {
            item._selected = false
            item._selectedKeys = []
            return item
          })
          resolve(res.data.page.totalCount)
        }).catch(() => {
          reject([])
        })
      })
    }
  }
}
</script>
<style scoped>
.pd{
  padding:2px 10px;
}
</style>
