<template>
  <steelTradeAggrid
    ref="aggrid"
    class="stellTradeAggridBor"
    :column-defs="columnDefs"
    :auto-height="true"
    :child-column-defs="childColumnDefs"
    :row-data="rowData"
    :load-data="loadData"
    table-selection="multiple"
    row-key="sId"
    child-row-key="sId"
    children-list-key="noticeSaleDeliveryDetailVos"
    :header-total="headerTotal"
    :footer-total="footerTotal"
    is-subtable
    :paginationinif="false"
    @selectedChange="gridSelectedChange"
  />
</template>

<script>
import {
  SteelFormat,
  DictUtil
} from 'cnd-horizon-utils'
var Decimal = window.Decimal
import { Moment } from 'cnd-utils'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import {
  getContractSaleProcessDelivery,
  getContractSaleProcessDeliveryDetail
} from '@/api/queryStatistic/saleV2'

export default {
  components: {
    steelTradeAggrid
  },
  props: {
    sId: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      rowData: [],
      columnDefs: [{
        headerName: this.$t('grid.others.salesOrderNumber'),
        field: 'sCode'
      }, {
        headerName: this.$t('grid.title.status'),
        field: 'sSheetStatus',
        valueGetter: (params) => {
          const status = this.sheetStatus.filter(item => item.sCodeValue === params.data.sSheetStatus)
          return status.length ? status[0].sCodeName : params.value
        }
      }, {
        headerName: this.$t('grid.others.numberOfShipments'),
        field: 'vSumQty',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatThousandthSign(params.value, 4)
        }
      }, {
        headerName: this.$t('grid.others.numberOfPiecesShipped'),
        cellStyle: { textAlign: 'right' },
        field: 'vSumPkgQty',
        valueFormatter: params => {
          return SteelFormat.formatThousandthSign(params.value)
        }
      }, {
        headerName: this.$t('grid.others.shipmentAmount'),
        field: 'vSumAmt',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatPrice(params.value)
        }
      }, {
        headerName: this.$t('grid.others.adjustedQuantity'),
        field: 'vSumAdjustQty',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatThousandthSign(params.value, 4)
        }
      }, {
        headerName: this.$t('grid.others.adjustedPieceCount'),
        field: 'vSumAdjustPiece',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          if (params.value !== null) {
            return SteelFormat.formatThousandthSign(params.value, 0)
          }
        }
      }, {
        headerName: '调整后金额',
        field: 'vSumAdjustAmt',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatPrice(params.value)
        }
      }, {
        headerName: this.$t('grid.others.warehouse'),
        field: 'vWarehouseName'
      }, {
        headerName: '提货客户',
        field: 'vDeliCustomerName'
      }, {
        headerName: this.$t('grid.others.receiptStatus'),
        field: 'sSignStatus',
        valueGetter: (params) => {
          const status = this.signStatus.filter(item => item.sCodeValue === params.data.sSignStatus)
          return status.length ? status[0].sCodeName : params.data.sSignStatus
        }
      }, {
        headerName: this.$t('grid.title.createdBy'),
        field: 'vCreatorName'
      }, {
        headerName: this.$t('grid.title.createdAt'),
        field: 'sCreateTime',
        minWidth: 150,
        valueGetter(params) {
          return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
        }
      }],
      childColumnDefs: [{
        headerName: this.$t('grid.others.item'),
        field: 'vGoodsDesc'
      }, {
        headerName: this.$t('grid.others.steelCoilNumber'),
        field: 'sExtend4'
      }, {
        headerName: this.$t('grid.others.carriageNumber'),
        field: 'sExtend5'
      }, {
        headerName: this.$t('grid.others.numberOfShipments'),
        field: 'sContractQty',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatThousandthSign(params.value, 4)
        }
      }, {
        headerName: this.$t('grid.others.numberOfPiecesShipped'),
        field: 'sQty',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatThousandthSign(params.value)
        }
      }, {
        headerName: this.$t('grid.others.shipmentAmount'),
        field: 'sTaxAmt',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatPrice(params.value)
        }
      }, {
        headerName: this.$t('grid.others.adjustedQuantity'),
        field: 'vSumAdjustQty',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatThousandthSign(params.value, 4)
        }
      }, {
        headerName: this.$t('grid.others.adjustedPieceCount'),
        field: 'vSumAdjustPiece',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          if (params.value !== null) {
            return SteelFormat.formatThousandthSign(params.value, 0)
          }
        }
      }, {
        headerName: '调整后金额',
        field: 'vSumAdjustAmt',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatPrice(params.value)
        }
      }],

      headerTotal: [],
      footerTotal: [],
      sheetStatus: [],
      signStatus: []
    }
  },
  watch: {
    sId() {
      this.$refs.aggrid.loadTableData()
    }
  },
  mounted() {
    this.loadDict()
  },
  methods: {
    loadDict() {
      DictUtil.getDict(['dev.common.sheet.status', 'stock.sign.type'], res => {
        this.sheetStatus = res[0].dicts
        this.signStatus = res[1].dicts
      })
    },
    loadData() {
      return new Promise((resolve, reject) => {
        getContractSaleProcessDelivery({
          sContractId: this.sId
        }).then(res => {
          this.rowData = res.data.map(item => {
            item._selected = false
            item._selectedKeys = []
            item._details = item._details || []
            return item
          })
          resolve(res.data)
          this.headerTotal = res.data.reduce((prev, next, index) => {
            prev[1].count = +new Decimal(prev[1].count).add(next.vSumQty || 0)
            prev[2].count = +new Decimal(prev[2].count).add(next.vSumPkgQty || 0)
            prev[3].count = +new Decimal(prev[3].count).add(next.vSumAmt || 0)
            prev[4].count = +new Decimal(prev[4].count).add(next.vSumAdjustQty || 0)
            prev[5].count = +new Decimal(prev[5].count).add(next.vSumAdjustPiece || 0)
            prev[6].count = +new Decimal(prev[6].count).add(next.vSumAdjustAmt || 0)
            if (index + 1 === res.data.length) {
              prev[1].count = SteelFormat.formatThousandthSign(prev[1].count, 4)
              prev[2].count = SteelFormat.formatThousandthSign(prev[2].count)
              prev[3].count = SteelFormat.formatPrice(prev[3].count)
              prev[4].count = SteelFormat.formatThousandthSign(prev[4].count, 4)
              prev[5].count = SteelFormat.formatThousandthSign(prev[5].count, 0)
              prev[6].count = SteelFormat.formatPrice(prev[6].count)
            }
            return prev
          }, [{
            key: 'count',
            count: res.data.length,
            unit: this.$t('pagination.items')
          }, {
            title: this.$t('grid.others.numberOfShipments'),
            count: 0,
            unit: this.$t('grid.others.ton')
          }, {
            title: this.$t('grid.others.numberOfPiecesShipped'),
            count: 0,
            unit: this.$t('grid.others.pieces')
          }, {
            title: this.$t('grid.others.shipmentAmount'),
            count: 0,
            unit: this.$t('grid.others.yuan')
          }, {
            title: this.$t('grid.others.adjustedQuantity'),
            count: 0,
            unit: this.$t('grid.others.ton')
          }, {
            title: this.$t('grid.others.adjustedPieceCount'),
            count: 0,
            unit: this.$t('grid.others.pieces')
          }, {
            title: '调整后金额',
            count: 0,
            unit: this.$t('grid.others.yuan')
          }])
        })
      })
    },
    loadDetail(data) {
      return new Promise((resolve, reject) => {
        getContractSaleProcessDeliveryDetail({
          sContractId: this.sId,
          sNoticeId: data.sId
        }).then(res => {
          resolve(res.data)
        }).catch(() => {
          reject([])
        })
      })
    },
    gridSelectedChange() {
      this.$refs.aggrid.getSelectedData(list => {
        this.footerTotal = list.reduce((prev, next, index) => {
          prev[1].count = +new Decimal(prev[1].count).add(next.sContractQty || 0)
          prev[2].count = +new Decimal(prev[2].count).add(next.sQty || 0)
          prev[3].count = +new Decimal(prev[3].count).add(next.sTaxAmt || 0)
          prev[4].count = +new Decimal(prev[4].count).add(next.vSumAdjustQty || 0)
          prev[5].count = +new Decimal(prev[5].count).add(next.vSumAdjustPiece || 0)
          prev[6].count = +new Decimal(prev[6].count).add(next.vSumAdjustAmt || 0)
          if (index + 1 === list.length) {
            prev[1].count = SteelFormat.formatThousandthSign(prev[1].count, 4)
            prev[2].count = SteelFormat.formatThousandthSign(prev[2].count)
            prev[3].count = SteelFormat.formatPrice(prev[3].count)
            prev[4].count = SteelFormat.formatThousandthSign(prev[4].count, 4)
            prev[5].count = SteelFormat.formatThousandthSign(prev[5].count, 0)
            prev[6].count = SteelFormat.formatPrice(prev[6].count)
          }
          return prev
        }, [{
          key: 'count',
          count: list.length,
          unit: this.$t('pagination.items')
        }, {
          title: this.$t('grid.others.numberOfShipments'),
          count: 0,
          unit: this.$t('grid.others.ton')
        }, {
          title: this.$t('grid.others.numberOfPiecesShipped'),
          count: 0,
          unit: this.$t('grid.others.pieces')
        }, {
          title: this.$t('grid.others.shipmentAmount'),
          count: 0,
          unit: this.$t('grid.others.yuan')
        }, {
          title: this.$t('grid.others.adjustedQuantity'),
          count: 0,
          unit: this.$t('grid.others.ton')
        }, {
          title: this.$t('grid.others.adjustedPieceCount'),
          count: 0,
          unit: this.$t('grid.others.pieces')
        }, {
          title: '调整后金额',
          count: 0,
          unit: this.$t('grid.others.yuan')
        }])
      }, 'margeChild')
    },
    // 子表合计
    handleDetailCount(list) {
      return list.reduce((prev, next, index) => {
        prev[1].count = +new Decimal(prev[1].count).add(next.sContractQty || 0)
        prev[2].count = +new Decimal(prev[2].count).add(next.sQty || 0)
        prev[3].count = +new Decimal(prev[3].count).add(next.sTaxAmt || 0)
        if (index + 1 === list.length) {
          prev[1].count = SteelFormat.formatThousandthSign(prev[1].count, 4)
          prev[2].count = SteelFormat.formatThousandthSign(prev[2].count)
          prev[3].count = SteelFormat.formatPrice(prev[3].count)
        }
        return prev
      }, [{
        key: 'count',
        count: list.length,
        unit: this.$t('pagination.items')
      }, {
        title: this.$t('grid.others.numberOfShipments'),
        count: 0,
        unit: this.$t('grid.others.ton')
      }, {
        title: this.$t('grid.others.numberOfPiecesShipped'),
        count: 0,
        unit: this.$t('grid.others.pieces')
      }, {
        title: this.$t('grid.others.shipmentAmount'),
        count: 0,
        unit: this.$t('grid.others.yuan')
      }])
    }
  }
}
</script>
<style lang="scss" scoped>
.stellTradeAggridBor {
  ::v-deep .steel-trade-aggrid-header {
    border-right: 0;
    border-left: 0;
  }
  ::v-deep .ag-root-wrapper {
    border-right: 0;
    border-left: 0;
  }
  ::v-deep .cnd-pagination {
    border: 0;
  }
  ::v-deep .steel-trade-aggrid-footer {
    border-right: 0;
    border-left: 0;
  }
}
</style>
