<template>
  <div>
    <steelTradeAggrid
      ref="aggrid"
      class="stellTradeAggridBor"
      row-key="sId"
      :auto-height="true"
      :row-data="rowData"
      :load-data="loadData"
      :auto-load-data="false"
      :column-defs="columnDefs"
      :header-total="headerTotal"
      :footer-total="footerTotal"
      table-selection="multiple"
      :paginationinif="false"
      @selectedChange="gridSelectedChange"
    />
    <paymentDialog
      v-if="dialogVisible"
      :dialog-visible="dialogVisible"
      :pay-type-list="payTypeList"
      :select-obj="selectObj"
      @close="dialogVisible = false"
    />
  </div>
</template>

<script>
import {
  SteelFormat,
  DictUtil
} from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
var Decimal = window.Decimal
import steelTradeAggrid from '@/components/steelTradeAggrid'
import paymentDialog from '../dialog/paymentDialog.vue'
import {
  getContractSaleProcessPayment
} from '@/api/queryStatistic/saleV2'

export default {
  components: {
    steelTradeAggrid,
    paymentDialog
  },
  props: {
    sId: {
      type: String,
      default: null
    },
    selectData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      dialogVisible: false,
      rowData: [],
      columnDefs: [{
        headerName: this.$t('grid.others.receiptDate'),
        field: 'sApplyDate',
        valueGetter: params => Moment.time('YYYY-MM-DD', params.data.sApplyDate),
        valueFormatter(params) {
          return Moment.time('YYYY-MM-DD', params.data.sApplyDate)
        }
      }, {
        headerName: this.$t('grid.others.customer'),
        field: 'vCustomerName'
      }, {
        headerName: this.$t('grid.others.amountReceived'),
        field: 'sTaxAmt',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatPrice(params.value)
        }
      }, {
        headerName: '冻结金额',
        field: 'sFrozenAmt',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatPrice(params.value)
        }
      }, {
        headerName: this.$t('grid.others.transactionType'),
        field: 'sInteractionSubType',
        valueGetter: (params) => {
          const status = this.payTypeList.filter(item => item.sCodeValue === params.data.sInteractionSubType)
          return status.length ? status[0].sCodeName : params.data.sInteractionSubType
        }
      }, {
        headerName: this.$t('grid.others.paragraphType'),
        field: 'sFundType',
        valueGetter: (params) => {
          const status = this.payPaymentTypeList.filter(item => item.sCodeValue === params.data.sFundType)
          return status.length ? status[0].sCodeName : params.data.sFundType
        }
      }, {
        headerName: this.$t('grid.others.noteNumber'),
        field: 'sBillCode'
      }, {
        headerName: this.$t('grid.others.maturityDateOfAcceptance'),
        field: 'sMaturityDate',
        valueGetter: params => Moment.time('YYYY-MM-DD', params.data.sMaturityDate),
        valueFormatter(params) {
          return Moment.time('YYYY-MM-DD', params.data.sMaturityDate)
        }
      }, {
        headerName: this.$t('grid.others.documentType'),
        field: 'sUpSheetType',
        valueGetter: (params) => {
          const status = this.paySheetTypeList.filter(item => item.sCodeValue === params.data.sUpSheetType)
          return status.length ? status[0].sCodeName : params.data.sUpSheetType
        }
      }, {
        headerName: this.$t('grid.others.originalDocumentNumber'),
        field: 'sUpCode'
      }, {
        headerName: this.$t('grid.title.status'),
        field: 'sSheetStatus',
        valueGetter: (params) => {
          const status = this.sSheetStatusList.filter(item => item.sCodeValue === params.data.sSheetStatus)
          return status.length ? status[0].sCodeName : params.value
        }
      }, {
        headerName: '余款结算日期',
        field: 'balanceSettleDate',
        valueGetter(params) {
          return Moment.time('YYYY-MM-DD', params.data.balanceSettleDate)
        }
      }, {
        field: '',
        headerName: this.$t('grid.others.operation'),
        cellStyle: { textAlign: 'center' },
        onCellClicked: (params) => {
          this.openDialog(params)
        },
        cellRenderer: (params) => {
          const sHtml = '<span style=color:#3E8DDC;cursor:pointer>' + '明细' + '</span>'
          return params.data._hiddenCheckbox ? params.value : sHtml
        }
      }],
      headerTotal: [],
      footerTotal: [],
      payModeList: [],
      paySubtypeList: [],
      paySheetTypeList: [],
      sSheetStatusList: [],
      selectObj: {}
    }
  },
  watch: {
    sId() {
      this.$refs.aggrid.loadTableData()
    }
  },
  mounted() {
    this.loadDict()
  },
  methods: {
    loadDict() {
      DictUtil.getDict([
        'pay.subtype',
        'pay.payment.type',
        'sheet.payment.all',
        'dev.common.sheet.status'
      ], res => {
        this.payTypeList = res[0].dicts
        this.payPaymentTypeList = res[1].dicts
        this.paySheetTypeList = res[2].dicts
        this.sSheetStatusList = res[3].dicts
        this.$refs.aggrid.loadTableData()
      })
    },
    loadData() {
      return new Promise((resolve, reject) => {
        getContractSaleProcessPayment({
          sContractId: this.sId
        }).then(res => {
          this.rowData = res.data.map(item => {
            item._selected = false
            return item
          })
          resolve(res.data)
          this.headerTotal = res.data.reduce((prev, next, index) => {
            prev[1].count = +new Decimal(prev[1].count).add(next.sTaxAmt || 0)
            if (index + 1 === res.data.length) {
              prev[1].count = SteelFormat.formatPrice(prev[1].count)
            }
            return prev
          }, [{
            key: 'count',
            count: res.data.length,
            unit: this.$t('pagination.items')
          }, {
            title: this.$t('grid.others.amountReceived'),
            count: 0,
            unit: this.$t('grid.others.yuan')
          }])
        })
      })
    },
    gridSelectedChange(list) {
      this.footerTotal = list.reduce((prev, next, index) => {
        if (next._selected) {
          prev[1].count = +new Decimal(prev[1].count).add(next.sTaxAmt || 0)
        }
        if (index + 1 === list.length) {
          prev[1].count = SteelFormat.formatPrice(prev[1].count)
        }
        return prev
      }, [{
        key: 'count',
        count: list.filter(item => item._selected).length,
        unit: this.$t('pagination.items')
      }, {
        title: this.$t('grid.others.amountReceived'),
        count: 0,
        unit: this.$t('grid.others.yuan')
      }])
    },
    openDialog(params) {
      this.selectObj = {
        sSaleContractCode: this.selectData.sCode,
        sOriUpId: params.data.sUpId
      }
      this.dialogVisible = true
    }
  }
}
</script>
<style lang="scss" scoped>
.stellTradeAggridBor {
  ::v-deep .steel-trade-aggrid-header {
    border-right: 0;
    border-left: 0;
  }
  ::v-deep .ag-root-wrapper {
    border-right: 0;
    border-left: 0;
  }
  ::v-deep .steel-trade-aggrid-footer {
    border: 0;
  }
}
</style>
