<template>
  <div class="auto-page-title">
    <div class="btn-group nobtn-group no-border-right no-border-left">
      <div class="text">
        {{ $t('grid.title.settlementMethod') }}
      </div>
    </div>
    <steelTradeAggrid
      ref="aggrid"
      class="stellTradeAggridBor"
      :heightinif="150"
      :column-defs="paymentColumnDefs"
      :row-data="paymentRowData"
      :load-data="loadPaymentData"
      :paginationinif="false"
    />
    <div class="btn-group nobtn-group no-border-left no-border-right no-border-top" style="border-bottom:1px solid #dfe4ed;">
      <div class="text">
        {{ $t('grid.others.settlementTerms') }}
      </div>
    </div>
    <div style="padding:10px 20px 0 0;background-color: #ffffff">
      <cnd-form :model="form" disabled label-width="130px" style="margin-bottom: 0px">
        <el-row>
          <cnd-form-item v-show="!showFullAmount" :label="$t('grid.others.numberOfDaysUnderwritten')">
            <el-input v-model="form.sMinDays" disabled />
          </cnd-form-item>
          <cnd-form-item :label="$t('grid.others.settlementDays')">
            <el-input v-model="form.sPayDays" disabled />
          </cnd-form-item>
          <cnd-form-item v-show="form.sIsCashRt === '0'" :label="$t('grid.tips.doesItInvolveCashBackInterest')">
            <el-select v-model="form.sIsCashRt" filterable disabled :placeholder="$t('components.pleaseSelect')">
              <el-option
                v-for="item in options['base.yes-no']"
                :key="item.sCodeValue"
                :label="item.sCodeName"
                :value="item.sCodeValue"
              />
            </el-select>
          </cnd-form-item>
          <!-- <cnd-form-item v-show="form.sIsCashRt === '1'" :label="this.$t('grid.others.cashRebateMethod')">
            <el-select v-model="form.sCashRtType" filterable disabled :placeholder="$t('components.pleaseSelect')">
              <el-option
                v-for="item in options['trade.irt.return.type']"
                :key="item.sCodeValue"
                :label="item.sCodeName"
                :value="item.sCodeValue"
              />
            </el-select>
          </cnd-form-item> -->
          <cnd-form-item v-show="form.sIsCashRt === '1' && form.sCashRtType==='20'" :label="$t('grid.others.cashRefundRateMonthlyInterest')">
            <cnd-input-number v-model="form.sCashRtRate" type="percent" disabled />
          </cnd-form-item>
          <cnd-form-item v-show="form.sIsCashRt === '1' && form.sCashRtType==='10'" :label="$t('grid.others.singleTonInterestRebate')">
            <el-input v-model="form.sCashSignalRate" disabled />
          </cnd-form-item>
          <cnd-form-item v-show="form.sIsAcceptRt === '0'" :label="$t('grid.tips.doesItInvolveThePromissoryNote')">
            <el-select v-model="form.sIsAcceptRt" filterable disabled :placeholder="$t('components.pleaseSelect')">
              <el-option
                v-for="item in options['base.yes-no']"
                :key="item.sCodeValue"
                :label="item.sCodeName"
                :value="item.sCodeValue"
              />
            </el-select>
          </cnd-form-item>
          <!-- <cnd-form-item v-show="form.sIsAcceptRt === '1'" :label="$t('grid.others.methodOfDiscounting')">
            <el-select v-model="form.sAcceptRtType" filterable disabled :placeholder="$t('components.pleaseSelect')">
              <el-option
                v-for="item in options['trade.irt.return.type']"
                :key="item.sCodeValue"
                :label="item.sCodeName"
                :value="item.sCodeValue"
              />
            </el-select>
          </cnd-form-item> -->
          <cnd-form-item v-show="form.sIsAcceptRt === '1' && form.sAcceptRtType==='20'" :label="$t('grid.others.acceptanceDiscountRnterestKey')">
            <cnd-input-number v-model="form.sAcceptRtRate" type="percent" disabled />
          </cnd-form-item>
          <cnd-form-item v-show="form.sIsAcceptRt === '1' && form.sAcceptRtType==='10'" :label="$t('grid.others.singleTonDiscount')">
            <el-input v-model="form.sAcceptSignalRate" disabled />
          </cnd-form-item>
          <template v-if="showFullAmount">
            <cnd-form-item
              v-if="showStopIrtType"
              label="止息方式"
            >
              <el-select v-model="form.sStopIrtType" :placeholder="$t('components.pleaseSelect')">
                <el-option
                  v-for="item in options['esc.stopirt.type']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item label="商业折让方式" prop="sRebateType">
              <el-select v-model="form.sRebateType" disabled :placeholder="$t('components.pleaseSelect')">
                <el-option
                  v-for="item in options['esc.rebate.type']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
            <cnd-form-item label="锁定一票制" prop="sIsLockOne">
              <el-select v-model="form.sIsLockOne" disabled :placeholder="$t('components.pleaseSelect')">
                <el-option
                  v-for="item in options['base.yes-no']"
                  :key="item.sCodeValue"
                  :label="item.sCodeName"
                  :value="item.sCodeValue"
                />
              </el-select>
            </cnd-form-item>
          </template>
        </el-row>
      </cnd-form>
    </div>
    <div class="btn-group nobtn-group no-border-right no-border-left" style="border-right: 0;">
      <div class="text">
        <!-- 结算条款明细 -->
        {{ $t('grid.others.settlementTermsDetail') }}
      </div>
    </div>
    <steelTradeAggrid
      ref="clauseGrid"
      class="stellTradeAggridBor"
      :heightinif="150"
      :column-defs="settlementDetailColumn"
      :row-data="clauseDetailRowData"
      :load-data="loadClauseData"
      :paginationinif="false"
      :auto-load-data="false"
    />
  </div>
</template>

<script>
import {
  SteelFormat
} from 'cnd-horizon-utils'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import {
  getContractSaleSettlementList,
  getContractSaleSettlementClause,
  getSettlementClauseDetail
} from '@/api/queryStatistic/saleV2'
import { getDictList, getCnDitc } from '@/utils/dict'

export default {
  components: { steelTradeAggrid },
  props: {
    sId: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      paymentColumnDefs: [{
        headerName: this.$t('grid.others.beforeAndAfterTheGoods'),
        field: 'sSettlementType',
        valueGetter: params => {
          return getCnDitc(params, this.options['trade.settlement.type'], 'sSettlementType')
        }
      }, {
        headerName: this.$t('grid.others.whetherRiskTransfer'),
        field: 'sIsRiskTransfer',
        valueGetter: params => {
          return getCnDitc(params, this.options['base.yes-no'], 'sIsRiskTransfer')
        }
      }, {
        headerName: this.$t('grid.title.paymentMethod'),
        field: 'sPaymentType',
        valueGetter: params => {
          return getCnDitc(params, this.options['trade.pay.mode'], 'sPaymentType')
        }
      }, {
        headerName: this.$t('grid.others.dateMethod'),
        field: 'sDateType',
        valueGetter: params => {
          return getCnDitc(params, this.options['trade.date.type'], 'sDateType')
        }
      }, {
        headerName: this.$t('grid.others.Days'),
        field: 'sDays'
      }, {
        headerName: this.$t('grid.others.dateMethodDescription'),
        field: 'sDateTypeDescribe'
      },
      // {
      //   headerName: '支付凭据',
      //   field: 'sPayReceipts'
      // },
      {
        headerName: this.$t('grid.title.paymentRatio'),
        field: 'sPaymentRate',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.toPercent(params.value)
        }
      }, {
        headerName: this.$t('grid.others.originalCurrencyAmount'),
        field: 'sTaxAmt',
        cellStyle: { textAlign: 'right' },
        valueFormatter: params => {
          return SteelFormat.formatPrice(params.value)
        }
      }],
      paymentRowData: [],
      clauseDetailDefs: [
        {
          headerName: this.$t('grid.others.interestBearingItems'),
          field: 'sField1',
          valueGetter: (params) => {
            return getCnDitc(params, this.options['esc.clause.item'], 'sField1')
          }
        },
        {
          headerName: this.$t('grid.others.startDays'),
          field: 'sField11',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value)
          }
        },
        {
          headerName: this.$t('grid.others.cutOffDays'),
          field: 'sField12',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return params.data.sField2 === '1' ? params.value : SteelFormat.formatThousandthSign(params.value)
          }
        },
        {
          headerName: this.$t('grid.others.whetherUnlimited'),
          cellStyle: { textAlign: 'right' },
          field: 'sField2',
          valueGetter: params => {
            return getCnDitc(params, this.options['base.yes-no'], 'sField2')
          }
        },
        {
          headerName: this.$t('grid.others.interestRateTag'),
          field: 'sField6',
          cellStyle: { textAlign: 'right' },
          valueFormatter(params) {
            if (params.value) {
              const Digits = String(params.value).length - (String(params.value).indexOf('.') + 1)
              if (Digits > 3) {
                return SteelFormat.toPercent(params.value)
              } else {
                return SteelFormat.toPercent(params.value, 2)
              }
            } else {
              return ''
            }
          }
        },
        {
          headerName: this.$t('grid.others.singleTonMarkup'),
          field: 'sField7',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return params.value ? SteelFormat.formatPrice(params.value) : ''
          }
        }
      ],
      clauseDetailDefsV2: [
        {
          field: 'sField1',
          headerName: '结算项目',
          cellStyle: { textAlign: 'left' },
          valueGetter: params => {
            return getCnDitc(params, this.options['esc.clause.item'], 'sField1')
          }
        },
        {
          field: 'sField11',
          headerName: '提前/逾期天数从',
          cellStyle: { textAlign: 'right' }
        },
        {
          field: 'sField12',
          headerName: '提前/逾期天数到',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            console.log(params)
            return params.data.sField2 === '1' ? params.value : SteelFormat.formatThousandthSign(params.value)
          }
        },
        // {
        //   field: 'sField13',
        //   headerName: this.$t('grid.others.numberOfDays'),
        //   cellStyle: { textAlign: 'right' }
        // },
        {
          field: 'sField2',
          headerName: this.$t('grid.others.whetherUnlimited'),
          cellStyle: { textAlign: 'left' },
          valueGetter: params => {
            return getCnDitc(params, this.options['base.yes-no'], 'sField2')
          }
        },
        {
          field: 'sField6',
          headerName: '费率',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            if (params.value) {
              const Digits = String(params.value).length - (String(params.value).indexOf('.') + 1)
              if (Digits > 3) {
                return SteelFormat.toPercent(params.value)
              } else {
                return SteelFormat.toPercent(params.value, 2)
              }
            } else {
              return ''
            }
          }
        }
      ],
      settlementDetailColumn: this.clauseDetailDefs,
      clauseDetailRowData: [],
      form: {},
      options: {
        'trade.settlement.type': [],
        'trade.pay.mode': [],
        'base.yes-no': [],
        'trade.irt.return.type': [],
        'trade.date.type': [],
        'esc.clause.item': [],
        'esc.rebate.type': [],
        'esc.stopirt.type': []
      },
      showFullAmount: false
    }
  },
  watch: {
    sId() {
      this.$refs.aggrid.loadTableData()
      this.loadDict()
      this.loadDetail()
    }
  },
  mounted() {
    getDictList(this.options)
    this.loadDetail()
  },
  methods: {
    loadPaymentData() {
      return new Promise((resolve, reject) => {
        getContractSaleSettlementList({
          sContractId: this.sId
        }).then(res => {
          this.paymentRowData = res.data
          resolve(res.data)
        }).catch(() => {
          reject(0)
        })
      })
    },
    loadDetail() {
      getContractSaleSettlementClause({
        sContractId: this.sId
      }).then(res => {
        this.form = res.data
        this.$refs.clauseGrid.loadTableData()
      })
    },
    loadClauseData() {
      return new Promise((resolve, reject) => {
        getSettlementClauseDetail({
          sUpId: this.sId,
          classifyId: '136111111111111'
        }).then(res => {
          this.clauseDetailRowData = res.data
          this.showFullAmount = this.clauseDetailRowData.some(item => item.sField1 && ['40', '50', '60'].includes(item.sField1)
          )
          if (this.showFullAmount === true) {
            this.$refs.clauseGrid.setColumnDefs(this.clauseDetailDefsV2)
          } else {
            this.$refs.clauseGrid.setColumnDefs(this.clauseDetailDefs)
          }
          resolve(res.data)
        }).catch(() => {
          reject(0)
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.nobtn-group{
  height:34px;
  line-height:17px;
}
.stellTradeAggridBor {
  ::v-deep .steel-trade-aggrid-header {
    border-right: 0;
    border-left: 0;
  }
  ::v-deep .ag-root-wrapper {
    border-right: 0;
    border-left: 0;
  }
}
</style>
