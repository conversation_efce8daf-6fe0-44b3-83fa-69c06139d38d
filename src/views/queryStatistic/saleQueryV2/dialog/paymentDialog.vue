<template>
  <cnd-dialog
    title="明细"
    append-to-body
    height="500"
    width="1000px"
    :fullscreen="false"
    :visible.sync="dialogVisible"
    @close="handleClose"
  >
    <template slot="content">
      <auto-wrap class="mt-10">
        <steelTradeAggrid
          ref="aggrid"
          class="mb-10"
          :load-data="loadData"
          :column-defs="columnDefs"
          :row-data="rowData"
        />
      </auto-wrap>
    </template>
    <template slot="footer">
      <div>
        <el-button size="mini" @click="handleClose">关 闭</el-button>
      </div>
    </template>
  </cnd-dialog>
</template>

<script>
import { SteelFormat } from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import {
  getContractSaleProcessPaymentDialog
} from '@/api/queryStatistic/saleV2'
export default {
  components: {
    steelTradeAggrid
  },
  props: {
    dialogVisible: {
      type: <PERSON>olean,
      default: false
    },
    payTypeList: {
      type: Array,
      default: () => {
        return []
      }
    },
    selectObj: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      rowData: [],
      columnDefs: [
        {
          headerName: this.$t('grid.others.transactionType'),
          field: 'sInteractionSubclass',
          valueGetter: (params) => {
            const status = this.payTypeList.filter(item => item.sCodeValue === params.data.sInteractionSubclass)
            return status.length ? status[0].sCodeName : params.data.sInteractionSubclass
          }
        },
        {
          headerName: '往来单号',
          field: 'sUpCode'
        },
        {
          headerName: '收款',
          width: '106px',
          field: 'sTaxAmt',
          valueGetter: (params) => {
            return SteelFormat.formatPrice(params.data.sTaxAmt)
          }
        },
        {
          headerName: '收款日期',
          field: 'sInteractionDate',
          valueGetter: params => Moment.time('YYYY-MM-DD', params.data.sInteractionDate)
        },
        {
          headerName: '调入类型',
          field: 'vInteractionSubclass',
          valueGetter: (params) => {
            const status = this.payTypeList.filter(item => item.sCodeValue === params.data.vInteractionSubclass)
            return status.length ? status[0].sCodeName : params.data.vInteractionSubclass
          }
        },
        {
          headerName: '销售合同号',
          width: '168px',
          field: 'sSaleContractCode'
        },
        {
          headerName: '销售发货单',
          width: '168px',
          field: 'vSaleNoticeCode'
        }
      ]
    }
  },
  methods: {
    loadData(pagination = { page: 0, limit: 30 }) {
      return new Promise((resolve, reject) => {
        getContractSaleProcessPaymentDialog(
          { ...this.selectObj }, pagination).then(res => {
          this.rowData = res.data.detailVos.content
          console.log('this.rowData: ', this.rowData)
          resolve(res.data.detailVos)
        })
      })
    },
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>
