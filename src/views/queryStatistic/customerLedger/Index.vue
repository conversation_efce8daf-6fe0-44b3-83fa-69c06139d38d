<template>
  <div class="page-container">
    <p class="page-title">客户余额台账</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form ref="searchForm" :form-items="formItems" @search="onSearch" />
      <div class="btn-group mt-10" style="display: flex">
        <div class="text" style="flex:1">
          客户余额台账列表
        </div>
        <div>
          <export-btn
            class="ml-10"
            :file-name="'客户余额台账'"
            api-url="/esc/contract/sale/balance/excel/export"
            :post-params="searchInfo"
            :export-config="exportConfig"
            :get-grid-api="getGridApi"
          />
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :header-total="headerCount"
        :footer-total="footerCount"
        :auto-load-data="false"
        table-selection="multiple"
        @selectedChange="handleFooterCount"
      />
    </div>
  </div>
</template>

<script>
import steelTradeAggrid from '@/components/steelTradeAggrid'
import exportBtn from '@/components/exportBtnV2'
import { SteelFormat } from 'cnd-horizon-utils'
import {
  getContractSaleBalancePage
} from '@/api/queryStatistic/purchase'
export default {
  name: 'CustomerLedger',
  components: { steelTradeAggrid, exportBtn },
  data() {
    return {
      searchInfo: null,
      formItems: [
        {
          label: this.$t('grid.others.customer'),
          value: 'sCustomerIds',
          type: 'cndInputDialogItem',
          dialogType: 'customer',
          defaultUrl: '/esc/customer/page',
          option: { valueKey: 'sPath' },
          multiple: true,
          reserveKeyword: false,
          placeholder: this.$t('grid.others.pleaseSelectTheCustomer')
        }],
      columnDefs: [
        {
          headerName: '法人公司',
          field: 'vCompanyName',
          width: 206
        },
        {
          headerName: '客户名称',
          field: 'vName',
          width: 206
        },
        {
          headerName: '现款',
          field: 'vLeftAmt1',
          cellStyle: { textAlign: 'right' },
          width: 206,
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: '银承',
          field: 'vLeftAmt2',
          cellStyle: { textAlign: 'right' },
          width: 206,
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: '返利',
          field: 'vLeftAmt3',
          cellStyle: { textAlign: 'right' },
          width: 206,
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        }
      ],
      exportConfig: [
        { label: '法人公司', value: 'vCompanyName' },
        { label: '客户名称', value: 'vName' },
        { label: '现款', value: 'vLeftAmt1' },
        { label: '银承', value: 'vLeftAmt2' },
        { label: '返利', value: 'vLeftAmt3' }
      ],
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      headerCount: null,
      footerCount: null,
      rowData: []
    }
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      searchInfo.sCustomerIds = searchInfo.sCustomerIds?.toString()
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        getContractSaleBalancePage(this.searchInfo, pagination).then(res => {
          this.rowData = res.data.content.map(item => {
            item._selected = false
            item._selectedKeys = []
            return item
          })
          resolve(res.data)
        }).catch(() => {
          reject([])
        })
      })
    },
    handleFooterCount(rowData) {
      console.log(rowData)
    }
  }
}
</script>

<style scoped>

</style>
