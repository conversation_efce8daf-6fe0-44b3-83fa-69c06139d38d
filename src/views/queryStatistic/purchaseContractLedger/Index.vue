<template>
  <div class="page-container">
    <p class="page-title">采购合同台账</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form ref="searchForm" :form-items="formItems" @search="onSearch" />
      <div class="btn-group mt-10" style="display: flex">
        <div class="text" style="flex:1">
          采购合同台账列表
        </div>
        <div>
          <export-btn
            class="ml-10"
            :file-name="'采购合同台账'"
            api-url="/esc/contract/pur/book/excel/export"
            :post-params="searchInfo"
            :export-config="exportConfig"
            :get-grid-api="getGridApi"
          />
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :header-total="headerCount"
        :footer-total="footerCount"
        :auto-load-data="false"
        table-selection="multiple"
        @selectedChange="handleFooterCount"
      />
    </div>
  </div>
</template>

<script>
import steelTradeAggrid from '@/components/steelTradeAggrid'
import exportBtn from '@/components/exportBtnV2'
import { SteelFormat } from 'cnd-horizon-utils'
import {
  getContractPurBookPage
} from '@/api/queryStatistic/purchase'
export default {
  name: 'PurchaseContractLedger',
  components: { steelTradeAggrid, exportBtn },
  data() {
    return {
      searchInfo: null,
      formItems: [{
        label: this.$t('grid.others.contractNumber'),
        value: 'sCode',
        type: 'elInput',
        placeholder: this.$t('grid.others.pleaseEnterTheContractNumber')
      }],
      columnDefs: [
        {
          headerName: '合同号',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sCode',
              headerClass: 'c-header_child'
              // cellStyle: {
              //   color: '#409EFF',
              //   textDecoration: 'underline',
              //   cursor: 'pointer'
              // },
              // onCellClicked: e => {
              //   this.$router.push({
              //     path: '/purchaseQuerys',
              //     query: {
              //       sCode: e.value,
              //       name: this.$t('grid.others.procurementContractProcess'),
              //       activeId: localStorage.getItem('menuId')
              //     }
              //   })
              // }
            }
          ]
        },
        {
          headerName: '云钢到货',
          children: [
            {
              field: 'sQtx',
              headerName: '数量',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatThousandthSign(params.value, 4)
              }
            },
            {
              field: 'sQty',
              headerName: '件数',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatThousandthSign(params.value)
              }
            }
          ]
        },
        {
          headerName: '云钢采购发票',
          children: [
            {
              field: 'sInvQty',
              headerName: '数量',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatThousandthSign(params.value, 4)
              }
            },
            {
              field: 'sTaxAmt',
              headerName: '金额',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.value)
              }
            }
          ]
        }

      ],
      exportConfig: [
        { label: '合同号', value: 'sCode' },
        { label: '云钢到货数量', value: 'sQtx' },
        { label: '云钢到货件数', value: 'sQty' },
        { label: '云钢采购发票数量', value: 'sInvQty' },
        { label: '云钢采购发票金额', value: 'sTaxAmt' }
      ],
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      headerCount: null,
      footerCount: null,
      rowData: []
    }
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        getContractPurBookPage(this.searchInfo, pagination).then(res => {
          this.rowData = res.data.content.map(item => {
            item._selected = false
            item._selectedKeys = []
            return item
          })
          resolve(res.data.totalElements)
        }).catch(() => {
          reject([])
        })
      })
    }
  }
}
</script>

<style scoped>

</style>
