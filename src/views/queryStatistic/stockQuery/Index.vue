<!--
 * @Author: 沈鹭荣
 * @Date: 2021-01-11 11:19:38
 * @LastEditors: 沈鹭荣
 * @LastEditTime: 2021-01-12 10:17:33
 * @Description: file content
-->
<template>
  <div class="page-container">
    <p class="page-title">库存查询</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form
        ref="searchForm"
        :form-items="formItems"
        @search="loadData"
      />
      <div class="btn-group-no-bordr mt-15 mb-15">
        <div />
        <div class="text">
          <el-button
            key="comm_purcontact_list_add"
            type="primary"
            size="mini"
          >导出</el-button>
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        class="mt-15"
        :column-defs="columnDefs"
        :child-column-defs="childColumnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        table-selection="multiple"
        row-key="sContractCode"
        child-row-key="childId"
        children-list-key="details"
        is-subtable
      >
        <p slot="header">{{ totalRowData }}</p>
        <p slot="footer">选中合计</p>
      </steeltradeaggrid>
    </div>
  </div>
</template>

<script>
import { SteelFormat } from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
import {
  getStockOnRoadPage
} from '@/api/logistics/instransitOrderManage'
import steelTradeAggrid from '@/components/steelTradeAggrid'

export default {
  name: 'StockQuery',
  components: { steelTradeAggrid },
  data() {
    return {

      formItems: [{
        label: this.$t('grid.others.itemNumberTag'),
        value: 'aaa',
        type: 'elInput',
        placeholder: this.$t('grid.others.pleaseEnterTheItemNumberTag')
      }, {
        label: this.$t('grid.title.purchaseContractNumber'),
        value: 'aaa',
        type: 'elInput',
        placeholder: this.$t('grid.others.enterPurchaseContractNumber')
      }, {
        label: this.$t('grid.title.salesContractNumber'),
        value: 'aaa',
        type: 'elInput',
        placeholder: this.$t('grid.others.pleaseEnterTheSalesContractNumber')
      }, {
        label: this.$t('grid.title.company'),
        value: 'sCompanyId',
        type: 'cndInputDialog',
        dialogType: 'company',
        placeholder: this.$t('grid.others.pleaseSelectCompany')
      }, {
        label: this.$t('grid.others.supplier'), // 供应商,
        value: 'sCompanyId',
        type: 'cndInputDialog',
        dialogType: 'company',
        placeholder: this.$t('grid.others.pleaseSelectCompany')
      }, {
        label: this.$t('grid.others.warehouse'),
        value: 'warehouseId',
        type: 'cndInputDialogItem',
        defaultUrl: '/esc/warehouse/info/dialog/v2/page-dialog',
        option: { valueKey: 'sPath' }
      }, {
        label: this.$t('grid.title.accountingGroup'),
        value: 'costId',
        type: 'cndInputDialog',
        dialogType: 'cost'
      }, {
        label: this.$t('grid.title.personnel'),
        value: 'staffId',
        type: 'cndInputDialogItem',
        option: { valueKey: 'sPath' },
        dialogType: 'staff'
      }, {
        label: this.$t('grid.title.status'),
        value: 'istest',
        type: 'elSelect',
        dict: 'base.yes-no'
      }, {
        label: this.$t('grid.title.createdBy'),
        value: 'staffId',
        type: 'cndInputDialog',
        dialogType: 'applicant'
      }, {
        label: this.$t('grid.title.createdAt'),
        value: ['sCreateTime', 'vCreateTimeTo'],
        placeholder: [this.$t('grid.others.pleaseSelectTheDate'), this.$t('grid.others.pleaseSelectTheDate')],
        unlinkPanels: true,
        type: 'elDatePicker'
      }],
      columnDefs: [
        {
          headerName: this.$t('grid.others.inTransitOrderNumber'),
          field: 'sCode'
        },
        {
          headerName: this.$t('grid.title.status'),
          field: 'sSheetStatus'
        },
        {
          headerName: this.$t('grid.others.currentApprover'),
          field: 'vCurTaskUesr'
        },
        {
          headerName: this.$t('grid.others.approvalComments'),
          field: 'vComment'
        },
        {
          headerName: this.$t('grid.others.quantityNumberOfPieces'),
          field: 'vSumQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            // vSumQty/vSumPkgQty
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: this.$t('grid.title.amount'),
          field: 'vSumAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: this.$t('grid.others.warehouse'),
          field: 'aaa'
        },
        {
          headerName: this.$t('grid.others.supplier'), // 供应商,
          field: 'vSupplierIName'
        },
        {
          headerName: this.$t('grid.title.company'),
          field: 'vCompanyName'
        },
        {
          headerName: this.$t('grid.title.createdBy'),
          field: 'vCreatorName'
        },
        {
          headerName: this.$t('grid.title.createdAt'),
          field: 'sCreateTime',
          cellStyle: { textAlign: 'right' },
          minWidth: 150,
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
          }
        }
      ],
      childColumnDefs: [
        {
          headerName: this.$t('grid.others.itemNumberTag'),
          field: 'sContractCode'
        },
        {
          headerName: this.$t('grid.title.purchaseContractNumber'),
          field: 'vGoodsDesc'
        },
        {
          headerName: this.$t('grid.title.salesContractNumber'),
          field: 'vOrgin'
        },
        {
          headerName: this.$t('grid.others.item'),
          field: 'vMaterial'
        },
        {
          headerName: this.$t('grid.others.placeOfOrigin'),
          field: 'vSpec'
        },
        {
          headerName: this.$t('grid.others.material'),
          field: 'sQty',
          cellStyle: { textAlign: 'right' }
        },

        {
          headerName: this.$t('grid.others.specification'),
          field: 'vInvQty'
        },
        {
          headerName: this.$t('grid.others.steelCoilNumber'),
          field: 'sExtend4'
        },
        {
          headerName: this.$t('grid.others.carriageNumber'),
          field: 'sExtend5'
        },
        {
          headerName: this.$t('grid.others.quantityNumberOfPieces'),
          field: 'vTaxAmt',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            // vTaxAmt/vNetAmt
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: this.$t('grid.title.unitPrice'),
          field: 'sVatRate',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          field: 'sVatAmt',
          headerName: this.$t('grid.title.amount'),
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatPrice(params.value)
          }
        },
        {
          headerName: this.$t('grid.title.accountingGroup'),
          field: 'vCheckGroupName'

        },
        {
          headerName: this.$t('grid.title.personnel'),
          field: 'vStaffName'
        }
      ],
      rowData: []
    }
  },
  mounted() {
    this.$refs.aggrid.loadTableData()
  },
  methods: {
    loadData(pagination = { page: 0, limit: 20 }) {
      return new Promise((resolve, reject) => {
        getStockOnRoadPage({
          ...pagination
        }).then(res => {
          this.rowData = res.data.onroadPage.content
          const { vCount, vSumAmt, vSumQty, vSumPkgQty } = res.data
          this.totalRowData = `共${vCount}条，合计：数量${vSumQty}吨，件数${vSumPkgQty}件，金额${vSumAmt}元`
          resolve(res.data.onroadPage)
          console.log(res)
        }).catch(() => {
          reject(0)
        })
      })
    }
  }
}
</script>
