<template>
  <div class="page-container">
    <p class="page-title">销售合同台账对比</p>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form ref="searchForm" :form-items="formItems" @search="onSearch" />
      <div class="btn-group mt-10" style="display: flex">
        <div class="text" style="flex:1">
          销售合同台账对比列表
        </div>
        <div>
          <export-btn
            class="ml-10"
            :file-name="'销售合同台账对比'"
            api-url="/esc/ledger/sale/contract/export"
            :post-params="searchInfo"
            :export-config="exportConfig"
            :get-grid-api="getGridApi"
          />
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :header-total="headerCount"
        :footer-total="footerCount"
        :auto-load-data="false"
        table-selection="multiple"
        @selectedChange="handleFooterCount"
        @cellValueChanged="cellValueChanged"
      />
    </div>
  </div>
</template>

<script>
import steelTradeAggrid from '@/components/steelTradeAggrid'
import exportBtn from '@/components/exportBtnV2'
import { Moment } from 'cnd-utils'
import moment from 'moment'

import { SteelFormat } from 'cnd-horizon-utils'
import {
  getLedgerSaleContractPage,
  getLedgerSaleContractRemark
} from '@/api/queryStatistic/purchase'
export default {
  name: 'SaleContractContrast',
  components: { steelTradeAggrid, exportBtn },
  data() {
    return {
      statusList: [
        { 'sCodeValue': '70',
          'sCodeName': '执行',
          'sSort': 70,
          'sFilter': null,
          'sIsEnabled': '1',
          'sLanguage': 'zh_CN'
        },
        {
          'sCodeValue': '75',
          'sCodeName': this.$t('grid.others.completion'),
          'sSort': 75,
          'sFilter': null,
          'sIsEnabled': '1',
          'sLanguage': 'zh_CN'
        },
        {
          'sCodeValue': '90',
          'sCodeName': this.$t('grid.others.invalidate'),
          'sSort': 90,
          'sFilter': null,
          'sIsEnabled': '1',
          'sLanguage': 'zh_CN'
        }
      ],
      searchInfo: null,
      formItems: [{
        label: this.$t('grid.others.contractNumber'),
        value: 'sSaleContractCode',
        type: 'elInput',
        placeholder: this.$t('grid.others.pleaseEnterTheContractNumber')
      },
      // {
      //   label: this.$t('grid.others.customer'),
      //   value: 'sCustomerId',
      //   type: 'cndInputDialogItem',
      //   dialogType: 'customer',
      //   placeholder: this.$t('grid.others.pleaseSelectTheCustomer')
      // }, {
      //   label: this.$t('grid.others.itemNumberTag'),
      //   value: 'sProjectCode',
      //   type: 'elInput',
      //   placeholder: this.$t('grid.others.pleaseEnterTheItemNumberTag')
      // }, {
      //   label: this.$t('grid.title.company'),
      //   value: 'sCompanyId',
      //   type: 'cndInputDialog',
      //   dialogType: 'company',
      //   placeholder: this.$t('grid.others.pleaseSelectCompany')
      // }, {
      //   label: this.$t('grid.title.accountingGroup'),
      //   value: 'sCheckGroupId',
      //   type: 'cndInputDialog',
      //   dialogType: 'cost',
      //   placeholder: this.$t('grid.others.pleaseSelectAccountingGroup')
      // }, {
      //   label: this.$t('grid.others.department'),
      //   value: 'sDepartmentId',
      //   type: 'cndInputDialog',
      //   dialogType: 'depart',
      //   placeholder: this.$t('grid.others.pleaseSelectTheDepartment')
      // }, {
      //   label: this.$t('grid.title.personnel'),
      //   value: 'sStaffId',
      //   type: 'cndInputDialog',
      //   dialogType: 'staff',
      //   placeholder: this.$t('grid.others.pleaseSelectPersonnel')
      // }, {
      //   label: this.$t('grid.title.createdBy'),
      //   value: 'sCreator',
      //   type: 'cndInputDialog',
      //   dialogType: 'creater',
      //   placeholder: this.$t('grid.others.pleaseSelectTheCreator')
      // }, {
      //   label: this.$t('grid.title.status'),
      //   value: 'sSheetStatus',
      //   type: 'elSelect',
      //   itemType: 'occultation',
      //   default: '70',
      //   dict: [
      //     { 'sCodeValue': '70',
      //       'sCodeName': '执行',
      //       'sSort': 70,
      //       'sFilter': null,
      //       'sIsEnabled': '1',
      //       'sLanguage': 'zh_CN'
      //     },
      //     {
      //       'sCodeValue': '75',
      //       'sCodeName': this.$t('grid.others.completion'),
      //       'sSort': 75,
      //       'sFilter': null,
      //       'sIsEnabled': '1',
      //       'sLanguage': 'zh_CN'
      //     }
      //     // {
      //     //   'sCodeValue': '90',
      //     //   'sCodeName': this.$t('grid.others.invalidate'),
      //     //   'sSort': 90,
      //     //   'sFilter': null,
      //     //   'sIsEnabled': '1',
      //     //   'sLanguage': 'zh_CN'
      //     // }
      //   ] }, {
      //   label: this.$t('grid.title.createdAt'),
      //   value: ['sCreateTime', 'vCreateTimeTo'],
      //   default: ['', ''],
      //   itemType: 'occultation',
      //   unlinkPanels: true,
      //   type: 'elDatePicker'
      // },
      {
        label: '日期',
        value: ['startDateTime', 'endDateTime'],
        default: [
          moment().subtract(1, 'day').startOf('day').format('YYYY-MM-DDTHH:mm:ss'),
          moment().subtract(1, 'day').endOf('day').format('YYYY-MM-DDTHH:mm:ss')
        ],
        itemType: 'occultation',
        unlinkPanels: true,
        type: 'elDatePicker'
      },
      {
        label: '经营单位',
        value: 'sManagementId',
        type: 'cndInputDialog',
        dialogType: 'escOrg',
        itemType: 'occultation'
      },
      {
        label: '当日新增问题',
        value: 'sIsEmptyRemark',
        type: 'elCheckbox'
      },
      {
        label: '处理人',
        value: 'sHandleBy',
        type: 'elInput'
      }
      ],
      columnDefs: [
        {
          headerName: '合同号',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sSaleContractCode',
              headerClass: 'c-header_child',
              pinned: 'left',
              cellStyle: {
                color: '#409EFF',
                textDecoration: 'underline',
                cursor: 'pointer'
              },
              onCellClicked: e => {
                this.$router.push({
                  path: '/SaleQuery',
                  query: {
                    sCode: e.value,
                    name: this.$t('grid.others.salesContractProcess'),
                    activeId: localStorage.getItem('menuId')
                  }
                })
              }
            }
          ]
        },
        {
          headerName: '云钢收款金额',
          headerClass: 'c-header_parent',
          children: [
            {
              field: 'sRecAmt',
              headerName: '',
              headerClass: 'c-header_child',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.value)
              }
            }
          ]
        },
        {
          headerName: 'ERP收款金额',
          headerClass: 'c-header_parent',
          children: [
            {
              field: 'sErpRecAmt',
              headerName: '',
              headerClass: 'c-header_child',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.value)
              }
            }
          ]
        },
        {
          headerName: '收款金额差异',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sRecDiff',
              headerClass: 'c-header_child',
              cellStyle: { textAlign: 'right' },
              cellRenderer: params => {
                const text = SteelFormat.formatPrice(params.value)
                const sHtml = '<span  style=color:red>' + text + '</span>'
                return params.value !== null && params.value !== 0 ? sHtml : text
              }
            }
          ]
        },

        {
          headerName: '云钢赊销金额',
          headerClass: 'c-header_parent',
          children: [
            {
              field: 'sCreditAmt',
              headerName: '',
              headerClass: 'c-header_child',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.value)
              }
            }
          ]
        },
        {
          headerName: 'ERP赊销金额',
          headerClass: 'c-header_parent',
          children: [
            {
              field: 'sErpCreditAmt',
              headerName: '',
              headerClass: 'c-header_child',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.value)
              }
            }
          ]
        },
        {
          headerName: '赊销金额差异',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sCreditDiff',
              headerClass: 'c-header_child',
              cellStyle: { textAlign: 'right' },
              cellRenderer: params => {
                const text = SteelFormat.formatPrice(params.value)
                const sHtml = '<span  style=color:red>' + text + '</span>'
                return params.value !== null && params.value !== 0 ? sHtml : text
              }
            }
          ]
        },

        {
          headerName: '云钢赊销还款金额',
          headerClass: 'c-header_parent',
          children: [
            {
              field: 'sRefundAmt',
              headerName: '',
              headerClass: 'c-header_child',
              cellStyle: { textAlign: 'right' },
              width: 120,
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.value)
              }
            }
          ]
        },
        {
          headerName: 'ERP赊销还款金额',
          headerClass: 'c-header_parent',
          children: [
            {
              field: 'sErpRefundAmt',
              headerName: '',
              headerClass: 'c-header_child',
              cellStyle: { textAlign: 'right' },
              width: 120,
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.value)
              }
            }
          ]
        },
        {
          headerName: '赊销还款金额差异',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sRefundDiff',
              headerClass: 'c-header_child',
              cellStyle: { textAlign: 'right' },
              width: 120,
              cellRenderer: params => {
                const text = SteelFormat.formatPrice(params.value)
                const sHtml = '<span  style=color:red>' + text + '</span>'
                return params.value !== null && params.value !== 0 ? sHtml : text
              }
            }
          ]
        },

        {
          headerName: '云钢返利金额',
          headerClass: 'c-header_parent',
          children: [
            {
              field: 'sRebateAmt',
              headerName: '',
              headerClass: 'c-header_child',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.value)
              }
            }
          ]
        },
        {
          headerName: 'ERP返利金额',
          headerClass: 'c-header_parent',
          children: [
            {
              field: 'sErpRebateAmt',
              headerName: '',
              headerClass: 'c-header_child',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.value)
              }
            }
          ]
        },
        {
          headerName: '返利金额差异',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sRebateDiff',
              headerClass: 'c-header_child',
              cellStyle: { textAlign: 'right' },
              cellRenderer: params => {
                const text = SteelFormat.formatPrice(params.value)
                const sHtml = '<span  style=color:red>' + text + '</span>'
                return params.value !== null && params.value !== 0 ? sHtml : text
              }
            }
          ]
        },
        {
          headerName: '云钢销售发票',
          children: [
            {
              field: 'sInvQtx',
              headerName: '数量',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatThousandthSign(params.value, 4)
              }
            },
            {
              field: 'sInvAmt',
              headerName: '金额',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.value)
              }
            }
          ]
        },
        {
          headerName: 'ERP销售发票',
          children: [
            {
              field: 'sErpInvQtx',
              headerName: '数量',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatThousandthSign(params.value, 4)
              }
            },
            {
              field: 'sErpInvAmt',
              headerName: '金额',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.value)
              }
            }
          ]
        },
        {
          headerName: '发票金额差异',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sInvDiff',
              headerClass: 'c-header_child',
              cellStyle: { textAlign: 'right' },
              cellRenderer: params => {
                const text = SteelFormat.formatPrice(params.value)
                const sHtml = '<span  style=color:red>' + text + '</span>'
                return params.value !== 0 ? sHtml : text
              }
            }
          ]
        },
        {
          headerName: '云钢发货',
          children: [
            {
              field: 'sSaleQtx',
              headerName: '数量',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatThousandthSign(params.value, 4)
              }
            },
            {
              field: 'sSaleQty',
              headerName: '件数',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatThousandthSign(params.value)
              }
            },
            {
              field: 'sSaleAmt',
              headerName: '金额',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.value)
              }
            }
          ]
        },
        {
          headerName: 'ERP发货',
          children: [
            {
              field: 'sErpSaleQtx',
              headerName: '数量',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatThousandthSign(params.value, 4)
              }
            },
            {
              field: 'sErpSaleQty',
              headerName: '件数',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatThousandthSign(params.value)
              }
            },
            {
              field: 'sErpSaleAmt',
              headerName: '金额',
              cellStyle: { textAlign: 'right' },
              valueFormatter: params => {
                return SteelFormat.formatPrice(params.value)
              }
            }
          ]
        },
        {
          headerName: '发货数量差异',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sQtxDiff',
              headerClass: 'c-header_child',
              cellStyle: { textAlign: 'right' },
              cellRenderer: params => {
                const text = SteelFormat.formatThousandthSign(params.value, 4)
                const sHtml = '<span  style=color:red>' + text + '</span>'
                return params.value !== 0 ? sHtml : text
              }
            }
          ]
        },
        {
          headerName: '备注',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sRemark',
              headerClass: 'c-header_child',
              width: 400,
              editable: true // 可编辑
            }
          ]
        },
        {
          headerName: '处理人',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sHandledBy',
              headerClass: 'c-header_child',
              width: 400,
              editable: true // 可编辑
            }
          ]
        },
        {
          headerName: '日期',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'sRdate',
              headerClass: 'c-header_child',
              valueFormatter(params) {
                return Moment.time('YYYY-MM-DD', params.data.sRdate)
              }
            }
          ]
        },
        {
          headerName: '经营单位',
          headerClass: 'c-header_parent',
          children: [
            {
              headerName: '',
              field: 'vManagementName',
              headerClass: 'c-header_child'
            }
          ]
        }
      ],
      exportConfig: [
        { label: '合同号', value: 'sSaleContractCode' },
        { label: '云钢收款金额', value: 'sRecAmt' },
        { label: 'ERP收款金额', value: 'sErpRecAmt' },
        { label: '收款金额差异', value: 'sRecDiff' },

        { label: '云钢赊销金额', value: 'sCreditAmt' },
        { label: 'ERP赊销金额', value: 'sErpCreditAmt' },
        { label: '赊销金额差异', value: 'sCreditDiff' },

        { label: '云钢赊销还款金额', value: 'sRefundAmt' },
        { label: 'ERP赊销还款金额', value: 'sErpRefundAmt' },
        { label: '赊销还款金额差异', value: 'sRefundDiff' },

        { label: '云钢返利金额', value: 'sRebateAmt' },
        { label: 'ERP返利金额', value: 'sErpRebateAmt' },
        { label: '返利金额差异', value: 'sRebateDiff' },
        { label: '云钢销售发票数量', value: 'sInvQtx' },
        { label: '云钢销售发票金额', value: 'sInvAmt' },
        { label: 'ERP销售发票数量', value: 'sErpInvQtx' },
        { label: 'ERP销售发票金额', value: 'sErpInvAmt' },
        { label: '发票金额差异', value: 'sInvDiff' },
        { label: '云钢发货数量', value: 'sSaleQtx' },
        { label: '云钢发货件数', value: 'sSaleQty' },
        { label: '云钢发货金额', value: 'sSaleAmt' },
        { label: 'ERP发货数量', value: 'sErpSaleQtx' },
        { label: 'ERP发货件数', value: 'sErpSaleQty' },
        { label: 'ERP发货金额', value: 'sErpSaleAmt' },
        { label: '发货数量差异', value: 'sQtxDiff' },
        { label: '备注', value: 'sRemark' },
        { label: '处理人', value: 'sHandledBy' },
        { label: '日期', value: 'sRdate',
          setValue(value) {
            return Moment.time('YYYY-MM-DD', value)
          }
        },
        { label: '经营单位', value: 'vManagementName' }
      ],
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      },
      headerCount: null,
      footerCount: null,
      rowData: []
    }
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        getLedgerSaleContractPage(this.searchInfo, pagination).then(res => {
          this.rowData = res.data.content.map(item => {
            item._selected = false
            item._selectedKeys = []
            return item
          })
          resolve(res.data)
        }).catch(() => {
          reject([])
        })
      })
    },
    handleFooterCount(rowData) {
      console.log(rowData)
    },
    cellValueChanged({ data }) {
      getLedgerSaleContractRemark(data).then(() => {
        this.$message.success(this.$t('grid.others.modifySuccessfully'))
        this.$refs.aggrid.reloadTableData()
      })
    }
  }
}
</script>

<style scoped>

</style>
