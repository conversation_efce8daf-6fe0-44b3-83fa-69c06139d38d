
<template>
  <div class="page-container">
    <div class="page-title">盘盈单管理</div>
    <div class="layout-content auto-page-title flexV">
      <steel-search-form
        ref="searchForm"
        :form-items="formItems"
        @search="onSearch"
      />
      <div class="btn-group mt-10">
        <div class="text">
          盘盈单列表
        </div>
        <div>
          <el-button
            v-has:esc_process_profit_page_add
            type="primary"
            size="mini"
            @click="dialogVisibleadd=true"
          >{{ $t('btns.add') }}</el-button>
          <el-button
            v-has:esc_process_profit_page_delete
            class="ml-10"
            type="danger"
            size="mini"
            :disabled="disRemove"
            @click="removes"
          >{{ $t('btns.delete') }}</el-button>
          <export-btn
            v-has:esc_process_profit_page_export
            class="ml-10"
            file-name="盘盈单管理"
            api-url="/esc/inventory/export"
            :post-params="searchInfo"
            :export-config="exportConfig"
            :get-grid-api="getGridApi"
          />
        </div>
      </div>
      <steelTradeAggrid
        ref="aggrid"
        :column-defs="columnDefs"
        :row-data="rowData"
        :load-data="loadData"
        :auto-load-data="false"
        table-selection="multiple"
        row-key="sId"
        :header-total="headerTotal"
        :footer-total="footerTotal"
        @rowDoubleClicked="rowDoubleClicked"
        @selectedChange="selectedChange"
      />
    </div>
    <add-plan
      v-if="dialogVisibleadd"
      :visible="dialogVisibleadd"
      @hidebody="hidebody"
      @onclose="onCloseAdd"
    />
  </div>
</template>

<script>
var Decimal = window.Decimal
import addPlan from './addPlan.vue'
import { SteelFormat } from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
import {
  getDictet, getCnDitc
} from '@/api/logistics/saleDelivery/saleorder'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import exportBtn from '@/components/exportBtnV2'
import { handleDict } from '@/utils/common'

import { statusDict } from '@/utils/dict'
import {
  inventoryProfitPage,
  inventoryRemoves
} from '@/api/stocktakeManage/index.js'
export default {
  name: 'InventoryLossOrderManage',
  components: { steelTradeAggrid, exportBtn, addPlan },
  data() {
    return {
      disRemove: true,
      dialogVisibleadd: false,
      searchInfo: null,
      delDisable: true,
      rowData: [],
      headerTotal: null,
      footerTotal: null,
      options: {
        'dev.common.sheet.status': []
      },
      formItems: [
        {
          label: '盘盈单号',
          value: 'sCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.title.purchaseContractNumber'),
          value: 'sContractCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.others.itemNumberTag'),
          value: 'sProjectCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.others.warehouse'),
          value: 'sWarehouseId',
          type: 'cndInputDialogItem',
          defaultUrl: '/esc/warehouse/info/dialog/v2/page-dialog',
          option: { valueKey: 'sPath' }
        },
        // {
        //   label: this.$t('grid.others.department'),
        //   value: 'sDepartmentId',
        //   type: 'cndInputDialog',
        //   dialogType: 'depart'
        // },
        {
          label: this.$t('grid.title.personnel'),
          value: 'sStaffId',
          type: 'cndInputDialogItem',
          option: { valueKey: 'sPath' },
          dialogType: 'staff'
        },
        {
          label: this.$t('grid.title.accountingGroup'),
          value: 'sCheckGroupId',
          type: 'cndInputDialog',
          dialogType: 'cost'
        },
        {
          label: this.$t('grid.title.status'),
          value: 'sSheetStatus',
          type: 'elSelect',
          dict: statusDict,
          placeholder: this.$t('grid.others.pleaseSelectStatus')
        },

        {
          label: this.$t('grid.title.createdBy'),
          value: 'sCreator',
          type: 'cndInputDialog',
          placeholder: this.$t('grid.others.pleaseSelectTheCreator'),
          dialogType: 'applicant'
        },
        {
          label: '经营单位',
          value: 'sManagementId',
          type: 'cndInputDialog',
          dialogType: 'escOrg'
        },
        {
          label: this.$t('grid.title.company'),
          value: 'sCompanyId',
          type: 'cndInputDialog',
          dialogType: 'company'
        },
        {
          label: this.$t('grid.title.createdAt'),
          value: ['sCreateTime', 'vCreateTimeTo'],
          placeholder: [this.$t('grid.others.pleaseSelectTheDate'), this.$t('grid.others.pleaseSelectTheDate')],
          unlinkPanels: true,
          type: 'elDatePicker'
        }
      ],
      columnDefs: [
        {
          headerName: '盘盈单号',
          field: 'sCode'
        },
        {
          headerName: this.$t('grid.title.status'),
          field: 'sSheetStatus',
          valueGetter: (params) => {
            return getCnDitc(params, this.options['dev.common.sheet.status'], 'sSheetStatus')
          }
        },
        {
          headerName: this.$t('grid.others.warehouse'),
          field: 'vWarehouseName'
        },
        {
          headerName: this.$t('grid.title.purchaseContractNumber'),
          field: 'sContractCode'
        },
        {
          headerName: this.$t('grid.others.itemNumberTag'),
          field: 'sProjectCode'
        },
        {
          headerName: '盘盈数量/件数',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return `${SteelFormat.formatThousandthSign(params.data.vSumQty, 4)}/${params.data.vSumPkgQty}`
          }
        },
        {
          headerName: this.$t('grid.title.company'),
          field: 'vCompanyName'
        },
        {
          headerName: this.$t('grid.title.accountingGroup'),
          field: 'vCheckGroupName'
        },
        {
          headerName: this.$t('grid.title.personnel'),
          field: 'vStaffName'
        },
        {
          headerName: this.$t('grid.title.createdBy'),
          field: 'vCreatorName'
        },
        {
          headerName: this.$t('grid.title.createdAt'),
          field: 'sCreateTime',
          minWidth: 150,
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD HH:mm:ss', params.data.sCreateTime)
          }
        },
        {
          headerName: '经营单位',
          field: 'vManagementName'
        }
      ],
      exportConfig: [
        {
          label: '盘盈单号',
          value: 'sCode'
        },
        {
          label: this.$t('grid.title.status'),
          value: 'sSheetStatus',
          setValue: (value) => {
            return handleDict(value, this.options['dev.common.sheet.status'])
          }
        },
        {
          label: this.$t('grid.others.warehouse'),
          value: 'vWarehouseName'
        },
        {
          label: this.$t('grid.title.purchaseContractNumber'),
          value: 'sContractCode'
        },
        {
          label: this.$t('grid.others.itemNumberTag'),
          value: 'sProjectCode'
        },
        {
          label: '盘盈数量',
          value: 'vSumQty',
          setValue: (value) => {
            return Number((+value).toFixed(4))
          }
        },
        {
          label: '盘盈件数',
          value: 'vSumPkgQty',
          setValue: (value) => {
            return Number(+value)
          }
        },
        {
          label: this.$t('grid.title.company'),
          value: 'vCompanyName'
        },
        {
          label: this.$t('grid.title.accountingGroup'),
          value: 'vCheckGroupName'
        },
        {
          label: this.$t('grid.title.personnel'),
          value: 'vStaffName'
        },
        {
          label: this.$t('grid.title.createdBy'),
          value: 'vCreatorName'
        },
        {
          label: this.$t('grid.title.createdAt'),
          value: 'sCreateTime',
          setValue: (value) => Moment.time('YYYY-MM-DD HH:mm:ss', value)
        },
        {
          label: '经营单位',
          value: 'vManagementName'
        }
      ],
      getGridApi: () => {
        return this.$refs.aggrid.gridApi
      }
    }
  },
  created() {
    getDictet([
      'dev.common.sheet.status'
    ]).then(result => {
      this.options['dev.common.sheet.status'] = result.data[0].dicts
    }).catch(() => {
    })
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      searchInfo.sSheetType = '110'
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    setTotal(vCount = 0, vSumQtx = 0, vSumQty = 0, flag) {
      this[flag] = [
        { count: vCount, key: 'count' },
        { title: '盘盈数量', count: SteelFormat.formatThousandthSign(vSumQtx, 4), unit: this.$t('grid.others.ton') },
        { title: '盘盈件数', count: vSumQty, unit: this.$t('grid.others.pieces') }
      ]
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        inventoryProfitPage(
          this.searchInfo,
          pagination
        ).then(res => {
          this.rowData = res.data.page.content.map(item => {
            item._selected = false
            return item
          })
          const { vCount, vSumQty, vSumPkgQty } = res.data
          this.setTotal(vCount, vSumQty, vSumPkgQty, 'headerTotal')
          resolve(res.data.page)
        }).catch(() => {
          reject([])
        })
      })
    },
    toDetailPage(data) {
      const { sId, sSheetStatus, sCode } = data
      this.$router.push({
        path: `/inventoryProfitDetail/${sId}`,
        query: {
          Id: sId,
          status: sSheetStatus,
          type: 'edit',
          name: `盘盈单【${sCode}】`,
          activeId: localStorage.getItem('menuId')
        }
      })
    },
    rowDoubleClicked(params) {
      this.toDetailPage(params.data)
    },
    selectedChange(list) {
      this.disRemove = true
      this.$refs.aggrid.getSelectedData(res => {
        if (res.length > 0) {
          this.disRemove = !res.every(val => val.sSheetStatus === '10')
        }
        const vCount = res.length
        let vSumQtx = 0
        let vSumQty = 0
        res.forEach(el => {
          vSumQtx = +new Decimal(vSumQtx).add(+el.vSumQty)
          vSumQty = +new Decimal(vSumQty).add(+el.vSumPkgQty)
        })
        this.setTotal(vCount, vSumQtx, vSumQty, 'footerTotal')
      })
    },
    hidebody(id) {
      this.dialogVisibleadd = false
    },
    onCloseAdd() {
      this.dialogVisibleadd = false
      this.$refs.aggrid.reloadTableData()
    },
    removes() {
      this.$refs.aggrid.getSelectedData(res => {
        this.$confirm(this.$t('tips.isItOkToDelete'), this.$t('grid.others.prompt'), {
          confirmButtonText: this.$t('btns.confirm'),
          cancelButtonText: this.$t('btns.cancel'),
          type: 'info'
        }).then(() => {
          inventoryRemoves(res.map(item => item.sId))
            .then(() => {
              this.$message.success(this.$t('grid.tips.deletionSuccess'))
              this.$refs.aggrid.reloadTableData()
            })
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
