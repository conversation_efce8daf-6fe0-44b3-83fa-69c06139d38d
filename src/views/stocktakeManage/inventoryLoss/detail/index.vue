<template>
  <div class="page-container">
    <cnd-dialog :title="$t('grid.tabs.basicInformation')" :visible="true" @close="onClose">
      <template slot="leftBtn">
        <el-button
          v-has:esc_process_loss_submit
          type="primary"
          size="mini"
          :disabled="isBusinessDisabled('submit', form.sSheetStatus)"
          @click="submit"
        >{{ $t('grid.others.submit') }}</el-button>
        <el-button
          v-has:esc_process_loss_delete
          type="danger"
          size="mini"
          :disabled="isBusinessDisabled('remove', form.sSheetStatus)"
          @click="remove"
        >{{ $t('btns.delete') }}</el-button>
        <el-button
          v-has:esc_process_loss_cancel
          type="warning"
          size="mini"
          :disabled="isBusinessDisabled('withdraw', sSheetStatus)"
          @click="withdraw"
        >{{ $t('grid.others.withdrawRequest') }}</el-button>
        <el-button
          v-has:esc_process_loss_revoke
          type="warning"
          size="mini"
          :disabled="isBusinessDisabled('revoke',form.sSheetStatus)"
          @click="revoke"
        >撤单</el-button>
        <el-button
          type="primary"
          size="mini"
          :disabled="isBusinessDisabled('annex', sSheetStatus)"
          @click="dialogVisible.annex = true"
        >{{ $t('btns.attachmentManagement') }}</el-button>
        <!-- <el-button
          type="primary"
          size="mini"
          @click="dialogVisible.approvalN8 = true"
        >ERP{{ $t('grid.others.approvalInquiry') }}</el-button> -->
        <el-button
          v-has:esc_process_loss_erp_approval_new
          type="primary"
          size="mini"
          :disabled="isBusinessDisabled('approval',sSheetStatus)"
          @click="dialogVisible.approvaldiaV2 = true"
        >ERP{{ $t('grid.others.approvalInquiry') }}</el-button>
      </template>
      <template slot="content">
        <el-tabs v-model="activeName" :before-leave="beforeLeave">
          <el-tab-pane :label="$t('grid.tabs.basicInformation')" name="basic">
            <baseInfo
              v-if="activeName === 'basic'"
              :id="id"
              ref="basic"
              :info="form"
              @success="getDetail"
            />
          </el-tab-pane>
          <el-tab-pane :label="$t('grid.tabs.commodityDetails')" name="goods">
            <goodsDetail
              v-if="activeName === 'goods'"
              :id="id"
              ref="goods"
              :info="form"
            />
          </el-tab-pane>
        </el-tabs>
      </template>
    </cnd-dialog>
    <steel-annex-dialog
      :visible="dialogVisible.annex"
      append-to-body
      :biz-id="id"
      :disabled-btn="{ scan: isBusinessDisabled('save', sSheetStatus), del: isBusinessDisabled('save', sSheetStatus) }"
      @onSelect="dialogVisible.annex = false"
    />
    <horizon-approval-dialog
      :id="id"
      :type="'n8'"
      :solt-btn="false"
      :visible.sync="dialogVisible.approvalN8"
      @handleClose="dialogVisible.approvalN8 = false"
    />
    <approval-dialog-v2
      :id="id"
      :solt-btn="false"
      :visible.sync="dialogVisible.approvaldiaV2"
      :sheet-code="form.sSheetCode"
      @handleClose="dialogVisible.approvaldiaV2 = false"
    />
  </div>
</template>

<script>
import baseInfo from './basicInfo'
import goodsDetail from './goodsDetail'
import businessMixin from '@/utils/businessMixin'
import approvalDialogV2 from '@/components/approvalDialogV2'
import {
  inventoryGet,
  inventorySubmit,
  inventoryRemove,
  inventorySubmitCancel,
  inventoryRevoke
} from '@/api/stocktakeManage/index.js'
// import {
//   getDictet
// } from '@/api/logistics/saleDelivery/saleorder'
export default {
  name: 'InventoryLossOrderDetail',
  components: {
    baseInfo,
    goodsDetail,
    approvalDialogV2
  },
  mixins: [businessMixin],
  data() {
    return {
      id: null,
      activeName: 'basic',
      dialogVisible: {
        annex: false,
        approvalN8: false,
        approvaldiaV2: false
      },
      form: {},
      oldForm: {}
    }
  },
  computed: {
    sSheetStatus() {
      return this.form?.sSheetStatus
    }
  },
  created() {
    this.id = this.$route.query.Id
    this.getDetail()
    // getDictet([
    //   'pay.payment.type',
    //   'pay.subtype'
    // ]).then(result => {
    //   this.paymentTypeList = result.data[0].dicts
    //   this.subTypeList = result.data[1].dicts
    // }).catch(() => {
    // })
  },
  methods: {
    getDetail(val) {
      if (this.id) {
        inventoryGet(this.id).then(res => {
          this.form = res.data
          this.oldForm = JSON.stringify(res.data)
        })
      }
    },
    async submit() {
      let saveDone = true
      if (this.activeName === 'basic') {
        saveDone = await this.checkSave()
      }
      if (saveDone) {
        inventorySubmit(this.id).then(() => {
          this.$message.success(this.$t('tips.submitSuccess'))
          this.getDetail()
        })
      }
    },
    checkSave() {
      return new Promise((resolve, reject) => {
        this.$refs.basic.saveForm((flag) => {
          if (flag) {
            if (this.oldForm !== JSON.stringify(this.form)) {
              this.$refs.basic.save().then(res => {
                this.getDetail()
                resolve(true)
              }).catch(() => {
                reject(false)
              })
            } else {
              resolve(true)
            }
          } else {
            reject(false)
          }
        })
      })
    },
    beforeLeave(activeName, oldActiveName) {
      const blFlag = new Promise((resolve, reject) => {
        if (activeName === 'basic') {
          this.getDetail()
          resolve()
        }
        if (oldActiveName === 'basic' && !this.isBusinessDisabled('save', this.sSheetStatus)) {
          this.checkSave().then(() => {
            resolve()
          }).catch(() => {
            reject()
          })
        } else {
          resolve()
        }
      })
      return blFlag
    },
    remove() {
      this.$confirm(this.$t('tips.isItOkToDelete'), this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'info'
      }).then(() => {
        inventoryRemove(this.id).then((res) => {
          if (res.code === '0000') {
            this.$message.success(this.$t('tips.deletedSuccessfully'))
            this.$tabDelete()
          }
        })
      })
    },
    withdraw() {
      this.$confirm(this.$t('grid.tips.whetherToRevokeTheApplication'), this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'info'
      }).then(() => {
        inventorySubmitCancel(this.id).then(() => {
          this.$message.success(this.$t('tips.withdrawalSuccessTag'))
        }).finally(() => {
          this.getDetail()
        })
      })
    },
    revoke() {
      this.$confirm('是否确认撤单？', this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'warning'
      }).then(() => {
        inventoryRevoke(this.id).then((res) => {
          this.$message.success('撤单成功')
        }).finally(() => {
          this.getDetail()
        })
      })
    },
    onClose() {
      this.$emit('onClose')
    }
  }
}
</script>

<style scoped>

</style>
