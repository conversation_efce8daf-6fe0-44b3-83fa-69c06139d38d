<template>
  <div>
    <div class="btn-group">
      <div class="text">
        盘亏批次商品明细
      </div>
      <div>
        <el-button
          v-has:esc_process_loss_detail_add
          type="primary"
          size="mini"
          :disabled="isBusinessDisabled('save', sSheetStatus)"
          @click="dialogVisible= true"
        >
          {{ $t('btns.add') }}
        </el-button>
        <el-button
          v-has:esc_process_loss_detail_delete
          type="danger"
          size="mini"
          :disabled="isBusinessDisabled('save', sSheetStatus)"
          @click="remove"
        >
          {{ $t('btns.delete') }}
        </el-button>
      </div>
    </div>
    <steelTradeAggrid
      ref="aggrid"
      :column-defs="columnDefs"
      :row-data="rowData"
      :heightinif="300"
      row-key="sId"
      table-selection="multiple"
      :load-data="loadData"
      :full-row-type="'parent'"
      @rowValueChanged="rowValueChanged"
      @selectedChange="selectedChange"
    />
    <goodsDetailDialog
      :id="id"
      :dialog-visible="dialogVisible"
      :info="info"
      @success="onSuccess"
      @close="closeDialog"
    />
  </div>
</template>

<script>
import { SteelFormat } from 'cnd-horizon-utils'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import {
  inventoryDetail,
  inventoryDetailRemoves
} from '@/api/stocktakeManage/index.js'
import goodsDetailDialog from '../dialog/goodsDetailDialog.vue'
import businessMixin from '@/utils/businessMixin'
import { computeCellTotal } from '@/utils/common'

export default {
  components: { steelTradeAggrid, goodsDetailDialog },
  mixins: [businessMixin],
  props: {
    id: {
      type: String,
      default: null
    },
    info: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dialogVisible: false,
      columnDefs: [
        {
          headerName: this.$t('grid.others.itemNumberTag'),
          field: 'sProjectCode'
        },
        {
          headerName: this.$t('grid.title.purchaseContractNumber'),
          field: 'sPurContractCode'
        },
        {
          headerName: this.$t('grid.title.salesContractNumber'),
          field: 'sSaleContractCode'
        },
        // {
        //   headerName: '加工任务单号',
        //   field: 'sExtend2'
        // },
        {
          headerName: this.$t('grid.others.item'),
          field: 'vArtName'
        },
        {
          headerName: this.$t('grid.title.quantity'),
          field: 'vSumQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: this.$t('grid.others.numberOfPiecesTag'),
          field: 'vSumPkgQty',
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value)
          }
        },
        {
          headerName: this.$t('grid.others.steelCoilNumber'),
          field: 'sExtend4'
        },
        {
          headerName: this.$t('grid.others.carriageNumber'),
          field: 'sExtend5'
        },
        {
          headerName: this.$t('grid.others.supplier'),
          field: 'vSupplierName'
        },
        {
          headerName: this.$t('grid.others.department'),
          field: 'vDepartmentName'
        },
        {
          headerName: this.$t('grid.title.personnel'),
          field: 'vStaffName'
        },
        {
          headerName: this.$t('grid.title.accountingGroup'),
          field: 'vCheckGroupName'
        },
        {
          headerName: '来源单据号',
          field: 'sUpCode'
        },
        {
          headerName: this.$t('grid.title.remarks'),
          field: 'sRemark'
        }
      ],
      rowData: []
    }
  },
  computed: {
    sSheetStatus() {
      return this.info.sSheetStatus
    }
  },
  methods: {
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        inventoryDetail(this.id, pagination).then(res => {
          this.rowData = res.data.page.content.map(item => {
            item._selected = false
            return item
          })
          resolve(res.data.page)
          this.handleCellTotal()
        }).catch(() => {
          reject(0)
        })
      })
    },
    selectedChange(list) {
      this.$refs.aggrid.getSelectedData(res => {
        this.handleCellTotal(res.length ? 'selected' : 'all')
      })
    },
    handleCellTotal(type = 'all') {
      if (!this.rowData.length) {
        this.$refs.aggrid.gridApi.setPinnedBottomRowData([])
        return false
      }
      setTimeout(() => {
        const totalList = computeCellTotal(
          this.rowData,
          {
            vSumQty: 0,
            vSumPkgQty: 0
          },
          {
            sProjectCode: type === 'all' ? this.$t('grid.others.amountTo') : `${this.$t('components.selected')} ${this.rowData.filter(item => item._selected).length} ${this.$t('pagination.items')}`,
            sPurContractCode: null,
            sSaleContractCode: null,
            vArtName: null,
            sExtend4: null,
            sExtend5: null,
            vSupplierName: null,
            vDepartmentName: null,
            vStaffName: null,
            vCheckGroupName: null,
            sUpCode: null,
            sRemark: null,
            _selected: false,
            _hiddenCheckbox: true
          },
          type
        )
        this.$refs.aggrid.gridApi.setPinnedBottomRowData([totalList])
      }, 0)
    },
    onSuccess() {
      this.$refs.aggrid.loadTableData()
    },
    closeDialog() {
      this.dialogVisible = false
    },
    remove() {
      this.$refs.aggrid.getSelectedData(res => {
        if (!res.length) {
          this.$message.warning(this.$t('grid.others.pleaseSelectData'))
          return false
        }
        inventoryDetailRemoves(res.map(item => item.sId)).then(e => {
          this.$message({
            message: this.$t('tips.deletedSuccessfully'),
            type: 'success'
          })
          this.$refs.aggrid.reloadTableData()
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
