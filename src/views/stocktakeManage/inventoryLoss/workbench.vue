<template>
  <div class="page-container">
    <p v-show="dtype !== 'oa'" class="page-title">盘亏工作台</p>
    <div class="flexV" :class="dtype === 'oa'? '' : 'layout-content auto-page-title'" :style="dtype === 'oa'? 'height:92%' : ''">
      <steel-search-form
        ref="searchForm"
        :form-items="formItems"
        @search="onSearch"
      />
      <el-tabs
        v-model="activeName"
        class="tabs-btn-position mt-10 "
        @tab-click="tabClick"
      >
        <cnd-btn-position right="10">
          <!-- 采购到货 -->
          <span v-show="caigouBtn">
            <span v-show="addToCartBtn" class="mr-10">
              <el-button
                v-has:esc_process_loss_wb_tobecreated
                type="primary"
                size="mini"
                @click="addToCart"
              >
                加入待生成盘亏单
              </el-button>
            </span>
            <el-button
              v-has:esc_process_loss_wb_create
              type="primary"
              size="mini"
              :disabled="addDiabled"
              @click="create(CAIGOU)"
            >
              盘亏
            </el-button>
            <el-dropdown v-has:esc_stock_stockToInventory_wb_loss_import_300 split-button size="mini" type="primary" class="ml-10">
              {{ $t('grid.others.importMatch') }}
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <importBtn
                    type="one"
                    action="/api/esc/stock/stockToInventory/wb/loss/import"
                    action-success-url="/esc/stock/stockToInventory/wb/loss/success/import/300"
                    btn-text="直接导入"
                    :params="{ sCustomReceiptType: '300' }"
                    success-mark="tskInventoryId"
                    @success="successImport"
                  />
                </el-dropdown-item>
                <el-dropdown-item>
                  <importBtn
                    type="two"
                    is-sync
                    action="/api/esc/stock/stockToInventory/wb/loss/asy/import"
                    :params="{ sCustomReceiptType: '300' }"
                    btn-text="后台导入"
                  />
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </span>
          <!-- 在途货物 -->
          <span v-show="zaituBtn">
            <span v-show="addToCartBtn" class="mr-10">
              <el-button
                v-has:esc_process_loss_wb_tobecreated
                type="primary"
                size="mini"
                @click="addToCart"
              >
                加入待生成盘亏单
              </el-button>
            </span>
            <el-button
              v-has:esc_process_loss_wb_create
              type="primary"
              size="mini"
              :disabled="addDiabled"
              @click="create(ZAITU)"
            >
              盘亏
            </el-button>
            <el-dropdown v-has:esc_stock_stockToInventory_wb_loss_import_200 split-button size="mini" type="primary" class="ml-10">
              {{ $t('grid.others.importMatch') }}
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <importBtn
                    type="one"
                    action="/api/esc/stock/stockToInventory/wb/loss/import"
                    action-success-url="/esc/stock/stockToInventory/wb/loss/success/import/200"
                    btn-text="直接导入"
                    :params="{ sCustomReceiptType: '200' }"
                    success-mark="tskInventoryId"
                    @success="successImport"
                  />
                </el-dropdown-item>
                <el-dropdown-item>
                  <importBtn
                    type="two"
                    is-sync
                    action="/api/esc/stock/stockToInventory/wb/loss/asy/import"
                    :params="{ sCustomReceiptType: '200' }"
                    btn-text="后台导入"
                  />
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </span>
        </cnd-btn-position>
        <el-tab-pane
          :label="$t('grid.others.purchaseArrival')"
          :name="CAIGOU"
        >
          <div class="h100 flexV ">
            <steelTradeAggrid
              v-if="activeName==CAIGOU"
              ref="aggrid"
              class="tabstablebor"
              :heightinif="heightinif"
              :column-defs="columnDefs"
              :row-data="rowData"
              :load-data="loadData"
              :auto-load-data="false"
              table-selection="multiple"
              row-key="sId"
              :header-total="headerTotal"
              :footer-total="footerTotal"
              full-row-type="parent"
              @selectedChange="selectedChange"
              @rowValueChanged="rowValueChanged"
            />
            <cndShoppingCart
              v-if="addToCartBtn && activeName==CAIGOU"
              ref="shoppingCart"
              title="待生成盘亏单"
              submit-text="盘亏"
              row-key="sId"
              button-title="待生成盘亏单"
              full-row-type="parent"
              :is-subtable="false"
              :columns="shoppingCartColumn"
              :merge-config="shoppingCartMerge"
              :count-list="shoppingCartCountList"
              :cur-config="curConfig"
              :validate-key="[
                'sWarehouseId',
                'sProjectType',
                'sCompanyId',
                'sManagementId'
              ]"
              @selectedChange="handleShoppingCartCount"
              @submit="checkCreate"
            />
          </div>
        </el-tab-pane>
        <el-tab-pane
          :label="$t('grid.others.goodsInTransit')"
          :name="ZAITU"
        >
          <div class="h100 flexV ">
            <steelTradeAggrid
              v-if="activeName==ZAITU"
              ref="aggrid"
              class="tabstablebor"
              :heightinif="heightinif"
              :column-defs="columnDefs"
              :row-data="rowData"
              :load-data="loadData"
              :auto-load-data="false"
              table-selection="multiple"
              row-key="sId"
              :header-total="headerTotal"
              :footer-total="footerTotal"
              full-row-type="parent"
              @selectedChange="selectedChange"
              @rowValueChanged="rowValueChanged"
            />
            <cndShoppingCart
              v-if="addToCartBtn && activeName==ZAITU"
              ref="shoppingCart"
              title="待生成盘亏单"
              submit-text="盘亏"
              row-key="sId"
              button-title="待生成盘亏单"
              full-row-type="parent"
              :is-subtable="false"
              :columns="shoppingCartColumn"
              :merge-config="shoppingCartMerge"
              :count-list="shoppingCartCountList"
              :cur-config="curConfig"
              :validate-key="[
                'sWarehouseId',
                'sProjectType',
                'sCompanyId',
                'sManagementId'
              ]"
              @selectedChange="handleShoppingCartCount"
              @submit="checkCreate"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
var Decimal = window.Decimal
import Vue from 'vue'
import { Middleware } from 'cndinfo-ui'
import { SteelFormat } from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
// import moment from 'moment'
const ZAITU = 'zaitu'
const CAIGOU = 'caigou'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import {
  stockToInventoryWbLoss,
  stockToInventoryWbCreate,
  inventoryGet
} from '@/api/stocktakeManage/index.js'
import curMixin from '@/utils/curMixin'
import cndShoppingCart from '@/components/cndShoppingCart/index'
import importBtn from '@/components/importBtn'
export default {
  name: 'InventoryLossOrderWorkbench',
  components: { steelTradeAggrid, cndShoppingCart, importBtn },
  mixins: [curMixin],
  props: {
    heightinif: {
      type: [Number, String],
      default: ''
    },
    dtype: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      CAIGOU: CAIGOU,
      ZAITU: ZAITU,
      activeName: CAIGOU,
      searchInfo: null,
      addDiabled: true,
      rowData: [],
      headerTotal: null,
      footerTotal: null,
      curConfig: {
        vCurQty: 'vCurQtx',
        vCurPkgQty: 'vCurQty',
        vLeftQty: 'sLeftQtx',
        vLeftPkgQty: 'sLeftQty'
      },
      formItems: [
        {
          label: '物流单号',
          value: 'sDeliveryCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.others.itemNumberTag'),
          value: 'sProjectCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.title.purchaseContractNumber'),
          value: 'sPurContractCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.title.salesContractNumber'),
          value: 'sSaleContractCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.others.warehouse'),
          value: 'sWarehouseId',
          required: true,
          type: 'cndInputDialogItem',
          defaultUrl: '/esc/warehouse/info/dialog/v2/page-dialog',
          option: { valueKey: 'sPath' }
        },
        {
          label: this.$t('grid.title.company'),
          value: 'sCompanyId',
          required: true,
          type: 'cndInputDialog',
          dialogType: 'company'
        },
        {
          label: this.$t('grid.others.steelCoilNumber'),
          value: 'sSteelNo',
          type: 'elInput',
          itemType: 'occultation'
        },
        {
          label: this.$t('grid.others.carriageNumber'),
          value: 'sVesselNo',
          type: 'elInput'
        },
        {
          label: this.$t('grid.title.accountingGroup'),
          value: 'sCheckGroupId',
          type: 'cndInputDialog',
          dialogType: 'cost'
        },
        {
          label: this.$t('grid.others.supplier'),
          value: 'sSupplierId',
          type: 'cndInputDialogItem',
          dialogType: 'customer',
          defaultUrl: '/esc/customer/page',
          option: { valueKey: 'sPath' }
        },

        {
          label: '入库时间',
          value: ['sStockReceiptDate', 'vStockReceiptDateTo'],
          placeholder: [this.$t('grid.others.pleaseSelectTheDate'), this.$t('grid.others.pleaseSelectTheDate')],
          type: 'elDatePicker',
          unlinkPanels: true,
          itemType: 'occultation'
        },
        {
          label: '经营单位',
          value: 'sManagementId',
          type: 'cndInputDialog',
          dialogType: 'escOrg'
        }
      ],
      columnDefs: [
        {
          headerName: '物流单号',
          field: 'sDeliveryCode'
        },
        {
          headerName: this.$t('grid.others.itemNumberTag'),
          field: 'sProjectCode'
        },
        {
          headerName: this.$t('grid.title.purchaseContractNumber'),
          field: 'sPurContractCode'
        },
        {
          headerName: this.$t('grid.title.salesContractNumber'),
          field: 'sSaleContractCode'
        },
        {
          headerName: this.$t('grid.others.supplier'),
          field: 'sSupplierName'
        },
        {
          headerName: this.$t('grid.title.company'),
          field: 'sCompanyName'
        },
        {
          headerName: this.$t('grid.others.warehouse'),
          field: 'sWarehouseName'
        },
        {
          headerName: this.$t('grid.others.item'),
          field: 'sArtName'
        },
        {
          headerName: this.$t('grid.others.steelCoilNumber'),
          field: 'sSteelNo'
        },
        {
          headerName: this.$t('grid.others.carriageNumber'),
          field: 'sVesselNo'
        },
        {
          headerName: this.$t('grid.others.remainingQuantityNumberOfPieces'),
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return `${SteelFormat.formatThousandthSign(params.data.sLeftQtx, 4)}/${params.data.sLeftQty}`
          }
        },
        {
          headerName: this.$t('grid.others.quantityConfirmedThisTime'),
          field: 'vCurQtx',
          editable: true,
          cellEditorFramework: Vue.extend(
            Middleware.createComponent(
              'CndInputNumber',
              {
                mark: 'vCurQtx',
                type: 'number',
                decimalDigit: 4,
                autoFocus: true,
                focusSelect: true
              },
              {
                blur: ({ event, rowData, middleware }) => {
                  this.setCurQty(event, rowData, middleware)
                }
              }
            )
          ),
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: this.$t('grid.others.theNumberOfConfirmedPieces'),
          field: 'vCurQty',
          editable: true,
          cellEditorFramework: Vue.extend(
            Middleware.createComponent(
              'CndInputNumber',
              {
                mark: 'vCurQty',
                type: 'number',
                decimalDigit: 0,
                focusSelect: true
              },
              {
                blur: ({ event, rowData, middleware }) => {
                  this.setCurPkgQty(event, rowData, middleware)
                }
              }
            )
          ),
          cellStyle: { textAlign: 'right' }
        },
        {
          headerName: this.$t('grid.title.remarks'),
          field: 'sRemark'
        },
        {
          headerName: '入库日期',
          field: 'sStockReceiptDate',
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD', params.data.sStockReceiptDate)
          }
        },
        {
          headerName: this.$t('grid.title.accountingGroup'),
          field: 'sCheckGroupName'
        },
        {
          headerName: this.$t('grid.title.personnel'),
          field: 'sStaffName'
        },
        {
          headerName: '经营单位',
          field: 'sManagementName'
        }
      ],
      shoppingCartColumn: [
        {
          headerName: '物流单号',
          field: 'sDeliveryCode'
        },
        {
          headerName: this.$t('grid.title.purchaseContractNumber'),
          field: 'sPurContractCode'
        },
        {
          headerName: this.$t('grid.title.salesContractNumber'),
          field: 'sSaleContractCode'
        },
        {
          headerName: this.$t('grid.others.item'),
          field: 'sArtName'
        },
        {
          headerName: this.$t('grid.others.quantityConfirmedThisTime'),
          field: 'vCurQtx',
          editable: true,
          cellEditorFramework: Vue.extend(
            Middleware.createComponent(
              'CndInputNumber',
              {
                mark: 'vCurQtx',
                type: 'number',
                decimalDigit: 4,
                autoFocus: true,
                focusSelect: true
              },
              {
                blur: ({ event, rowData, middleware }) => {
                  this.setCurQty(event, rowData, middleware)
                }
              }
            )
          ),
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: this.$t('grid.others.theNumberOfConfirmedPieces'),
          field: 'vCurQty',
          editable: true,
          cellEditorFramework: Vue.extend(
            Middleware.createComponent(
              'CndInputNumber',
              {
                mark: 'vCurQty',
                type: 'number',
                decimalDigit: 0,
                focusSelect: true
              },
              {
                blur: ({ event, rowData, middleware }) => {
                  this.setCurPkgQty(event, rowData, middleware)
                }
              }
            )
          ),
          cellStyle: { textAlign: 'right' }
        },
        {
          headerName: this.$t('grid.title.remarks'),
          field: 'sRemark'
        },
        {
          headerName: this.$t('grid.others.warehouse'),
          field: 'sWarehouseName'
        },
        {
          headerName: this.$t('grid.others.steelCoilNumber'),
          field: 'sSteelNo'
        },
        {
          headerName: this.$t('grid.others.carriageNumber'),
          field: 'sVesselNo'
        },
        {
          headerName: this.$t('grid.others.supplier'),
          field: 'sSupplierName'
        },
        {
          headerName: this.$t('grid.title.company'),
          field: 'sCompanyName'
        },
        {
          headerName: this.$t('grid.title.accountingGroup'),
          field: 'sCheckGroupName'
        },
        {
          headerName: this.$t('grid.title.personnel'),
          field: 'sStaffName'
        }
      ],
      shoppingCartMerge: [{
        field: 'vCurQty',
        mode: 'add',
        max: 'sLeftQty'
      }, {
        field: 'vCurPkgQty',
        mode: 'add',
        max: 'vLeftPkgQty'
      }],
      shoppingCartCountList: []
    }
  },
  computed: {
    addToCartBtn() {
      return this.dtype !== 'oa'
    },
    caigouBtn() {
      return this.activeName === CAIGOU
    },
    zaituBtn() {
      return this.activeName === ZAITU
    }
  },
  created() {
  },
  mounted() {
    this.onSearch(false)
  },
  methods: {
    tabClick() {
      this.headerTotal = null
      this.footerTotal = null
      this.rowData = []
      this.addDiabled = true
    },
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      searchInfo.sCustomReceiptType = this.activeName === CAIGOU ? '300' : '200'
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    setTotal(vCount = 0, vSumLeftQtx = 0, vSumLeftQty = 0, flag) {
      this[flag] = [
        { count: vCount, key: 'count' },
        { title: this.$t('grid.others.remainingQuantity'), count: SteelFormat.formatThousandthSign(vSumLeftQtx, 4), unit: this.$t('grid.others.ton') },
        { title: this.$t('grid.others.remainingPieces'), count: SteelFormat.formatThousandthSign(vSumLeftQty, 0), unit: this.$t('grid.others.pieces') }
      ]
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        stockToInventoryWbLoss(
          this.searchInfo,
          pagination
        ).then(res => {
          this.rowData = res.data.page.content.map(item => {
            item._selected = false
            return item
          })
          const { vCount, vLeftQtx, vLeftQty } = res.data
          this.setTotal(vCount, vLeftQtx, vLeftQty, 'headerTotal')
          resolve(res.data.page)
        }).catch(() => {
          reject([])
        })
      })
    },
    toDetailPage(data) {
      const { sId, sSheetStatus, sCode } = data
      this.$router.push({
        path: `/inventoryLossOrderDetail/${sId}`,
        query: {
          Id: sId,
          status: sSheetStatus,
          type: 'edit',
          name: `盘亏单【${sCode}】`,
          activeId: localStorage.getItem('menuId')
        }
      })
    },
    selectedChange(list) {
      this.$refs.aggrid.getSelectedData(res => {
        this.addDiabled = true
        const vCount = res.length
        if (vCount > 0) {
          this.addDiabled = false
        }
        let vSumLeftQtx = 0
        let vSumLeftQty = 0
        res.forEach(el => {
          vSumLeftQtx = +new Decimal(vSumLeftQtx).add(+el.sLeftQtx)
          vSumLeftQty = +new Decimal(vSumLeftQty).add(+el.sLeftQty)
        })
        this.setTotal(vCount, vSumLeftQtx, vSumLeftQty, 'footerTotal')
      })
    },
    handleShoppingCartCount(list) {
      let vSumLeftQty = 0; let vSumLeftPkgQty = 0
      list.forEach(el => {
        vSumLeftQty = +new Decimal(vSumLeftQty).add(+el.vCurQtx)
        vSumLeftPkgQty = +new Decimal(vSumLeftPkgQty).add(+el.vCurQty)
      })
      this.shoppingCartCountList = [
        { count: list.length, key: 'count' },
        { title: this.$t('grid.others.quantityConfirmedThisTime'), count: SteelFormat.formatThousandthSign(vSumLeftQty, 4), unit: this.$t('grid.others.ton') },
        { title: this.$t('grid.others.theNumberOfConfirmedPieces'), count: SteelFormat.formatThousandthSign(vSumLeftPkgQty), unit: this.$t('grid.others.pieces') }
      ]
      console.log('this.shoppingCartCountList: ', this.shoppingCartCountList)
    },
    create() {
      this.$refs.aggrid.gridApi.stopEditing()
      this.$refs.aggrid.getSelectedData(res => {
        if (!res.length) {
          this.$message.warning(this.$t('grid.others.pleaseSelectData'))
          return false
        }
        this.checkCreate(res)
      })
    },
    checkCreate(list) {
      this.$confirm(this.$t('此操作将生成盘亏单, 是否继续?'), this.$t('grid.others.prompt'), {
        confirmButtonText: this.$t('btns.confirm'),
        cancelButtonText: this.$t('btns.cancel'),
        type: 'warning'
      }).then(() => {
        stockToInventoryWbCreate({
          sSheetType: '120',
          stockToInventoryWbCreateInfoVoList: list
        }).then(res => {
          this.$message.success(this.$t('grid.tips.generateSuccess'))
          this.toDetailPage(res.data)
          this.$emit('hidebody', res.data.sId)
        })
      }).catch(() => { })
    },
    addToCart() {
      this.$refs.aggrid.stopChildEditing()
      this.$refs.aggrid.getSelectedData((res) => {
        if (!res.length) {
          this.$message.warning(this.$t('grid.others.pleaseSelectData'))
          return false
        }
        console.log('res: ', res)
        this.$refs.shoppingCart.addToCart(res)
        this.$refs.aggrid.clearSelection()
      })
    },
    successImport(res) {
      inventoryGet(res.data.tskInventoryId).then(res => {
        this.toDetailPage(res.data)
      })
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
