
<template>
  <cnd-dialog
    title="新增盘亏商品明细"
    append-to-body
    width="80%"
    height="520"
    :fullscreen="false"
    :visible.sync="dialogVisible"
    @close="handleClose"
  >
    <template v-if="dialogVisible" slot="content">
      <steel-search-form ref="searchForm" :form-items="formItems" @search="onSearch" />
      <auto-wrap>
        <steelTradeAggrid
          ref="aggrid"
          class="mt-10"
          :column-defs="columnDefs"
          :row-data="rowData"
          :load-data="loadData"
          :footer-total="footerTotal"
          :auto-load-data="false"
          table-selection="multiple"
          row-key="sId"
          full-row-type="parent"
          @selectedChange="selectedChange"
          @rowValueChanged="rowValueChanged"
        />
      </auto-wrap>
    </template>
    <template slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">{{ $t('btns.cancel') }}</el-button>
      <el-button type="primary" size="mini" @click="handleSelect">{{ $t('btns.confirm') }}</el-button>
    </template>
  </cnd-dialog>
</template>

<script>
var Decimal = window.Decimal
import Vue from 'vue'
import { Middleware } from 'cndinfo-ui'
import { SteelFormat } from 'cnd-horizon-utils'
import { Moment } from 'cnd-utils'
import {
  stockToInventoryWbLoss,
  inventoryDetailAdd
} from '@/api/stocktakeManage/index.js'
// import {
//   getDictet, getCnDitc
// } from '@/api/logistics/saleDelivery/saleorder'
import steelTradeAggrid from '@/components/steelTradeAggrid'
import curMixin from '@/utils/curMixin'

export default {
  components: { steelTradeAggrid },
  mixins: [curMixin],

  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: null
    },
    info: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      searchInfo: null,
      curConfig: {
        vCurQty: 'vCurQtx',
        vCurPkgQty: 'vCurQty',
        vLeftQty: 'sLeftQtx',
        vLeftPkgQty: 'sLeftQty'
      },
      formItems: [
        {
          label: '物流单号',
          value: 'sDeliveryCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.others.itemNumberTag'),
          value: 'sProjectCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.title.purchaseContractNumber'),
          value: 'sPurContractCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.title.salesContractNumber'),
          value: 'sSaleContractCode',
          type: 'elInput'
        },
        {
          label: this.$t('grid.others.steelCoilNumber'),
          value: 'sSteelNo',
          type: 'elInput'
        },
        {
          label: this.$t('grid.others.supplier'),
          value: 'sSupplierId',
          type: 'cndInputDialogItem',
          dialogType: 'customer',
          defaultUrl: '/esc/customer/page',
          option: { valueKey: 'sPath' }
        },
        {
          label: this.$t('grid.others.carriageNumber'),
          value: 'sVesselNo',
          type: 'elInput'
        },
        {
          label: this.$t('grid.title.accountingGroup'),
          value: 'sCheckGroupId',
          type: 'cndInputDialog',
          dialogType: 'cost'
        },
        {
          label: '入库时间',
          value: ['sStockReceiptDate', 'vStockReceiptDateTo'],
          placeholder: [this.$t('grid.others.pleaseSelectTheDate'), this.$t('grid.others.pleaseSelectTheDate')],
          type: 'elDatePicker',
          unlinkPanels: true,
          itemType: 'occultation'
        }
      ],
      columnDefs: [
        {
          headerName: '物流单号',
          field: 'sDeliveryCode'
        },
        {
          headerName: this.$t('grid.others.itemNumberTag'),
          field: 'sProjectCode'
        },
        {
          headerName: this.$t('grid.title.purchaseContractNumber'),
          field: 'sPurContractCode'
        },
        {
          headerName: this.$t('grid.title.salesContractNumber'),
          field: 'sSaleContractCode'
        },
        // {
        //   headerName: '购销方式',
        //   field: 'sProjectType',
        //   valueGetter: (params) => {
        //     return getCnDitc(params, this.options['trade.pur.sal.mode'], 'sProjectType')
        //   }
        // },
        {
          headerName: this.$t('grid.others.supplier'),
          field: 'sSupplierName'
        },
        {
          headerName: this.$t('grid.title.company'),
          field: 'sCompanyName'
        },
        {
          headerName: this.$t('grid.others.warehouse'),
          field: 'sWarehouseName'
        },
        {
          headerName: this.$t('grid.others.item'),
          field: 'sArtName'
        },
        {
          headerName: this.$t('grid.others.steelCoilNumber'),
          field: 'sSteelNo'
        },
        {
          headerName: this.$t('grid.others.carriageNumber'),
          field: 'sVesselNo'
        },
        {
          headerName: this.$t('grid.others.remainingQuantityNumberOfPieces'),
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return `${SteelFormat.formatThousandthSign(params.data.sLeftQtx, 4)}/${params.data.sLeftQty}`
          }
        },
        {
          headerName: this.$t('grid.others.quantityConfirmedThisTime'),
          field: 'vCurQtx',
          editable: true,
          cellEditorFramework: Vue.extend(
            Middleware.createComponent(
              'CndInputNumber',
              {
                mark: 'vCurQtx',
                type: 'number',
                decimalDigit: 4,
                autoFocus: true,
                focusSelect: true
              },
              {
                blur: ({ event, rowData, middleware }) => {
                  this.setCurQty(event, rowData, middleware)
                }
              }
            )
          ),
          cellStyle: { textAlign: 'right' },
          valueFormatter: params => {
            return SteelFormat.formatThousandthSign(params.value, 4)
          }
        },
        {
          headerName: this.$t('grid.others.theNumberOfConfirmedPieces'),
          field: 'vCurQty',
          editable: true,
          cellEditorFramework: Vue.extend(
            Middleware.createComponent(
              'CndInputNumber',
              {
                mark: 'vCurQty',
                type: 'number',
                decimalDigit: 0,
                focusSelect: true
              },
              {
                blur: ({ event, rowData, middleware }) => {
                  this.setCurPkgQty(event, rowData, middleware)
                }
              }
            )
          ),
          cellStyle: { textAlign: 'right' }
        },
        {
          headerName: this.$t('grid.title.remarks'),
          field: 'sRemark'
        },
        {
          headerName: '入库日期',
          field: 'sStockReceiptDate',
          valueFormatter(params) {
            return Moment.time('YYYY-MM-DD', params.data.sStockReceiptDate)
          }
        },
        {
          headerName: this.$t('grid.title.accountingGroup'),
          field: 'sCheckGroupName'
        },
        {
          headerName: this.$t('grid.title.personnel'),
          field: 'sStaffName'
        },
        {
          headerName: '经营单位',
          field: 'sManagementName'
        }
      ],
      rowData: [],
      options: {
        'trade.pur.sal.mode': []
      },
      footerTotal: null
    }
  },
  watch: {
    dialogVisible(val) {
      if (val) {
        this.$nextTick(() => {
          this.onSearch()
        })
      }
    }
  },
  created() {
    // getDictet([
    //   'trade.pur.sal.mode'
    // ]).then(result => {
    //   this.options['trade.pur.sal.mode'] = result.data[0].dicts
    // }).catch(() => {
    // })
  },
  methods: {
    onSearch(load = true) {
      const searchInfo = this.$refs.searchForm.getSearchData()
      searchInfo.sCustomReceiptType = this.info.sCustomReceiptType
      searchInfo.sWarehouseId = this.info.sWarehouseId
      searchInfo.sCompanyId = this.info.sCompanyId
      this.searchInfo = searchInfo
      if (load) {
        this.$refs.aggrid.loadTableData()
      }
    },
    loadData(pagination) {
      return new Promise((resolve, reject) => {
        stockToInventoryWbLoss(this.searchInfo, pagination).then(res => {
          this.rowData = res.data.page.content.map(item => {
            item._selected = false
            return item
          })
          resolve(res.data.page)
        }).catch(() => {
          reject([])
        })
      })
    },
    setTotal(vCount = 0, vSumLeftQtx = 0, vSumLeftQty = 0, flag) {
      this[flag] = [
        { count: vCount, key: 'count' },
        { title: this.$t('grid.others.remainingQuantity'), count: SteelFormat.formatThousandthSign(vSumLeftQtx, 4), unit: this.$t('grid.others.ton') },
        { title: this.$t('grid.others.remainingPieces'), count: SteelFormat.formatThousandthSign(vSumLeftQty, 0), unit: this.$t('grid.others.pieces') }
      ]
    },
    selectedChange(list) {
      this.$refs.aggrid.getSelectedData(res => {
        console.log('res: ', res)
        const vCount = res.length
        let vSumLeftQtx = 0
        let vSumLeftQty = 0
        res.forEach(el => {
          vSumLeftQtx = +new Decimal(vSumLeftQtx).add(+el.sLeftQtx)
          vSumLeftQty = +new Decimal(vSumLeftQty).add(+el.sLeftQty)
        })
        this.setTotal(vCount, vSumLeftQtx, vSumLeftQty, 'footerTotal')
      })
    },
    handleClose() {
      this.$emit('close')
    },
    handleSelect() {
      this.$refs.aggrid.gridApi.stopEditing()
      this.$refs.aggrid.getSelectedData(res => {
        if (!res.length) {
          this.$message.warning(this.$t('grid.others.pleaseSelectData'))
          return false
        }
        inventoryDetailAdd(res, this.id).then(res => {
          this.$message.success('操作成功')
          this.$emit('success')
          this.handleClose()
        })
      })
    }
  }
}
</script>
