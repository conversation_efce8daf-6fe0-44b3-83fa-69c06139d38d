<template>
  <cnd-dialog
    class="annex_dialog"
    title="新增盘亏单"
    :show-close="true"
    :fullscreen="false"
    :append-to-body="appendToBody"
    height="500"
    width="80%"
    :visible.sync="visible"
    @close="onClose"
  >
    <template slot="content">
      <auto-wrap>
        <wait-build-index
          ref="list"
          :dtype="'oa'"
          :zs-btn="false"
          :other-btn="true"
          @hidebody="hidebody"
        />
      </auto-wrap>
    </template>
    <!-- <template slot="footer">
      <div style="padding:10px 20px">
        <el-button
          size="mini"
          @click="onClose"
        >{{$t('btns.cancel')}}</el-button>
        <el-button
          type="primary"
          size="mini"
          @click="onClose"
        >{{$t('btns.confirm')}}</el-button>
      </div>
    </template> -->
  </cnd-dialog>
</template>
<script>
import WaitBuildIndex from './workbench.vue'
export default {
  name: 'AddPlan',
  components: { WaitBuildIndex },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    hidebody(id) {
      this.$emit('hidebody', id)
    },
    onClose() {
      this.$emit('onclose')
    }
  }
}
</script>

